directories:
  output: dist
  buildResources: build
appId: com.invisible.assessment-tool
productName: Invisible Assessment Tool
forceCodeSigning: false
files:
  - filter:
      - main.js
      - index.html
      - preload.js
      - package.json
      - node_modules/dotenv/**/*
      - screenshots/.gitkeep
      - temp/.gitkeep
      - cache/.gitkeep
      - '!screenshots/*.png'
      - '!screenshots/*.jpg'
      - '!screenshots/*.jpeg'
      - '!temp/*'
      - '!cache/*'
      - '!dist/**/*'
      - '!.git/**/*'
win:
  target:
    - target: portable
      arch:
        - x64
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Invisible Assessment Tool
portable:
  artifactName: InvisibleAssessmentTool-Portable-${arch}.exe
extraMetadata:
  name: System Calculator
  productName: System Calculator
electronVersion: 36.3.2
