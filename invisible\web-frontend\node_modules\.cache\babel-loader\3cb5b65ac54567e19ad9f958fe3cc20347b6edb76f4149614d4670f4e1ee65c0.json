{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Layers = createLucideIcon(\"Layers\", [[\"polygon\", {\n  points: \"12 2 2 7 12 12 22 7 12 2\",\n  key: \"1b0ttc\"\n}], [\"polyline\", {\n  points: \"2 17 12 22 22 17\",\n  key: \"imjtdl\"\n}], [\"polyline\", {\n  points: \"2 12 12 17 22 12\",\n  key: \"5dexcv\"\n}]]);\nexport { Layers as default };", "map": {"version": 3, "names": ["Layers", "createLucideIcon", "points", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\layers.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Layers\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEyIDIgMiA3IDEyIDEyIDIyIDcgMTIgMiIgLz4KICA8cG9seWxpbmUgcG9pbnRzPSIyIDE3IDEyIDIyIDIyIDE3IiAvPgogIDxwb2x5bGluZSBwb2ludHM9IjIgMTIgMTIgMTcgMjIgMTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/layers\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Layers = createLucideIcon('Layers', [\n  ['polygon', { points: '12 2 2 7 12 12 22 7 12 2', key: '1b0ttc' }],\n  ['polyline', { points: '2 17 12 22 22 17', key: 'imjtdl' }],\n  ['polyline', { points: '2 12 12 17 22 12', key: '5dexcv' }],\n]);\n\nexport default Layers;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,SAAW;EAAEC,MAAA,EAAQ,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,UAAY;EAAED,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,UAAY;EAAED,MAAA,EAAQ,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}