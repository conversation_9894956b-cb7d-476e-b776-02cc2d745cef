{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\PaymentCancel.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { XCircle, ArrowLeft } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CancelContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n_c = CancelContainer;\nconst CancelCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  max-width: 500px;\n  width: 100%;\n`;\n_c2 = CancelCard;\nconst CancelIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #FF5722;\n`;\n_c3 = CancelIcon;\nconst CancelTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #FF5722;\n`;\n_c4 = CancelTitle;\nconst CancelMessage = styled.p`\n  font-size: 1.2rem;\n  margin-bottom: 30px;\n  opacity: 0.9;\n`;\n_c5 = CancelMessage;\nconst PaymentCancel = () => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(CancelContainer, {\n    children: /*#__PURE__*/_jsxDEV(CancelCard, {\n      children: [/*#__PURE__*/_jsxDEV(CancelIcon, {\n        children: /*#__PURE__*/_jsxDEV(XCircle, {\n          size: 64\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CancelTitle, {\n        children: \"Payment Cancelled\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CancelMessage, {\n        children: \"Your payment was cancelled. No charges were made to your account.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '15px',\n          justifyContent: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-secondary\",\n          onClick: () => navigate('/purchase'),\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ArrowLeft, {\n            size: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), \"Try Again\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"btn-primary\",\n          onClick: () => navigate('/dashboard'),\n          children: \"Go to Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          marginTop: '30px',\n          fontSize: '0.9rem',\n          opacity: '0.7'\n        },\n        children: \"If you experienced any issues, please contact our support team.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_s(PaymentCancel, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c6 = PaymentCancel;\nexport default PaymentCancel;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"CancelContainer\");\n$RefreshReg$(_c2, \"CancelCard\");\n$RefreshReg$(_c3, \"CancelIcon\");\n$RefreshReg$(_c4, \"CancelTitle\");\n$RefreshReg$(_c5, \"CancelMessage\");\n$RefreshReg$(_c6, \"PaymentCancel\");", "map": {"version": 3, "names": ["React", "useNavigate", "styled", "XCircle", "ArrowLeft", "jsxDEV", "_jsxDEV", "CancelContainer", "div", "_c", "CancelCard", "_c2", "CancelIcon", "_c3", "CancelTitle", "h1", "_c4", "CancelMessage", "p", "_c5", "PaymentCancel", "_s", "navigate", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "gap", "justifyContent", "className", "onClick", "alignItems", "marginTop", "fontSize", "opacity", "_c6", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/PaymentCancel.js"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { XCircle, ArrowLeft } from 'lucide-react';\n\nconst CancelContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n`;\n\nconst CancelCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  max-width: 500px;\n  width: 100%;\n`;\n\nconst CancelIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #FF5722;\n`;\n\nconst CancelTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #FF5722;\n`;\n\nconst CancelMessage = styled.p`\n  font-size: 1.2rem;\n  margin-bottom: 30px;\n  opacity: 0.9;\n`;\n\nconst PaymentCancel = () => {\n  const navigate = useNavigate();\n\n  return (\n    <CancelContainer>\n      <CancelCard>\n        <CancelIcon>\n          <XCircle size={64} />\n        </CancelIcon>\n        <CancelTitle>Payment Cancelled</CancelTitle>\n        <CancelMessage>\n          Your payment was cancelled. No charges were made to your account.\n        </CancelMessage>\n        \n        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>\n          <button\n            className=\"btn-secondary\"\n            onClick={() => navigate('/purchase')}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}\n          >\n            <ArrowLeft size={18} />\n            Try Again\n          </button>\n          \n          <button\n            className=\"btn-primary\"\n            onClick={() => navigate('/dashboard')}\n          >\n            Go to Dashboard\n          </button>\n        </div>\n        \n        <p style={{ marginTop: '30px', fontSize: '0.9rem', opacity: '0.7' }}>\n          If you experienced any issues, please contact our support team.\n        </p>\n      </CancelCard>\n    </CancelContainer>\n  );\n};\n\nexport default PaymentCancel;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,EAAEC,SAAS,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,eAAe,GAAGL,MAAM,CAACM,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GARIF,eAAe;AAUrB,MAAMG,UAAU,GAAGR,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACG,GAAA,GATID,UAAU;AAWhB,MAAME,UAAU,GAAGV,MAAM,CAACM,GAAG;AAC7B;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAJID,UAAU;AAMhB,MAAME,WAAW,GAAGZ,MAAM,CAACa,EAAE;AAC7B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,WAAW;AAMjB,MAAMG,aAAa,GAAGf,MAAM,CAACgB,CAAC;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAE9B,oBACEK,OAAA,CAACC,eAAe;IAAAgB,QAAA,eACdjB,OAAA,CAACI,UAAU;MAAAa,QAAA,gBACTjB,OAAA,CAACM,UAAU;QAAAW,QAAA,eACTjB,OAAA,CAACH,OAAO;UAACqB,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACX,CAAC,eACbtB,OAAA,CAACQ,WAAW;QAAAS,QAAA,EAAC;MAAiB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC5CtB,OAAA,CAACW,aAAa;QAAAM,QAAA,EAAC;MAEf;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAe,CAAC,eAEhBtB,OAAA;QAAKuB,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAT,QAAA,gBACrEjB,OAAA;UACE2B,SAAS,EAAC,eAAe;UACzBC,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,WAAW,CAAE;UACrCO,KAAK,EAAE;YACLC,OAAO,EAAE,MAAM;YACfK,UAAU,EAAE,QAAQ;YACpBJ,GAAG,EAAE;UACP,CAAE;UAAAR,QAAA,gBAEFjB,OAAA,CAACF,SAAS;YAACoB,IAAI,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,aAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETtB,OAAA;UACE2B,SAAS,EAAC,aAAa;UACvBC,OAAO,EAAEA,CAAA,KAAMZ,QAAQ,CAAC,YAAY,CAAE;UAAAC,QAAA,EACvC;QAED;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAENtB,OAAA;QAAGuB,KAAK,EAAE;UAAEO,SAAS,EAAE,MAAM;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAM,CAAE;QAAAf,QAAA,EAAC;MAErE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEtB,CAAC;AAACP,EAAA,CA1CID,aAAa;EAAA,QACAnB,WAAW;AAAA;AAAAsC,GAAA,GADxBnB,aAAa;AA4CnB,eAAeA,aAAa;AAAC,IAAAX,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAoB,GAAA;AAAAC,YAAA,CAAA/B,EAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}