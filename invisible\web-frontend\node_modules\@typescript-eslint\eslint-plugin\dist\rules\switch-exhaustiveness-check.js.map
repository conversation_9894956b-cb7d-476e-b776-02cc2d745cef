{"version": 3, "file": "switch-exhaustiveness-check.js", "sourceRoot": "", "sources": ["../../src/rules/switch-exhaustiveness-check.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,qCAAwD;AACxD,+CAAiC;AAEjC,kCAOiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,6BAA6B;IACnC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,iEAAiE;YACnE,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,qBAAqB,EACnB,kEAAkE;YACpE,eAAe,EAAE,iCAAiC;SACnD;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,OAAO,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACjD,MAAM,eAAe,GAAG,OAAO,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAE7D,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACvD,OAAO,IAAA,mCAA4B,EAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC;QAED,SAAS,SAAS,CAChB,KAAyB,EACzB,IAA8B,EAC9B,kBAAkC,EAClC,UAAmB;;YAEnB,MAAM,QAAQ,GACZ,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACnE,MAAM,UAAU,GAAG,QAAQ;gBACzB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;gBACvC,CAAC,CAAC,iEAAiE;oBACjE,8CAA8C;oBAC9C,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAEtC,MAAM,YAAY,GAAG,EAAE,CAAC;YACxB,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE;gBAClD,8DAA8D;gBAC9D,8CAA8C;gBAC9C,EAAE;gBACF,8BAA8B;gBAC9B,qCAAqC;gBACrC,qBAAqB;gBACrB,qCAAqC;gBACrC,+BAA+B;gBAC/B,EAAE;gBACF,gCAAgC;gBAChC,IAAI,iBAAiB,CAAC,cAAc,EAAE,EAAE;oBACtC,SAAS;iBACV;gBAED,MAAM,iBAAiB,GAAG,MAAA,iBAAiB,CAAC,SAAS,EAAE,0CAAE,WAAW,CAAC;gBACrE,IAAI,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC;gBAEvD,IACE,UAAU;oBACV,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,EAAE,CAAC;oBAC/C,IAAA,sBAAe,EAAC,iBAAiB,CAAC,QAAQ,EAAE,EAAE,eAAe,CAAC,MAAM,CAAC,EACrE;oBACA,QAAQ,GAAG,GAAG,UAAU,KAAK,iBAAiB,IAAI,CAAC;iBACpD;gBAED,MAAM,YAAY,GAAG,wBAAwB,QAAQ,OAAO,CAAC;gBAE7D,YAAY,CAAC,IAAI,CACf,QAAQ,QAAQ,wBAAwB,YAAY,MAAM,CAC3D,CAAC;aACH;YAED,MAAM,SAAS,GAAG,YAAY;iBAC3B,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,UAAU,GAAG,IAAI,EAAE,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,IAAI,QAAQ,EAAE;gBACZ,OAAO,KAAK,CAAC,eAAe,CAAC,QAAQ,EAAE,KAAK,SAAS,EAAE,CAAC,CAAC;aAC1D;YAED,+BAA+B;YAC/B,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAC3C,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACnB,CAAC;YACH,MAAM,YAAY,GAAG,UAAU,CAAC,aAAa,CAC3C,IAAI,CAAC,YAAY,EACjB,0BAAmB,CACnB,CAAC;YAEH,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9C,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC9C,CAAC;QACJ,CAAC;QAED,SAAS,qBAAqB,CAAC,IAA8B;;YAC3D,MAAM,gBAAgB,GAAG,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACxD,MAAM,UAAU,GAAG,MAAA,gBAAgB,CAAC,SAAS,EAAE,0CAAE,WAAW,CAAC;YAE7D,IAAI,gBAAgB,CAAC,OAAO,EAAE,EAAE;gBAC9B,MAAM,UAAU,GAAG,IAAA,wBAAc,EAAC,gBAAgB,CAAC,CAAC;gBACpD,MAAM,SAAS,GAAiB,IAAI,GAAG,EAAE,CAAC;gBAC1C,KAAK,MAAM,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE;oBACnC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,EAAE;wBAC3B,4CAA4C;wBAC5C,OAAO;qBACR;oBAED,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;iBAC7C;gBAED,MAAM,kBAAkB,GAAG,UAAU,CAAC,MAAM,CAC1C,SAAS,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CACvC,CAAC;gBAEF,IAAI,kBAAkB,CAAC,MAAM,KAAK,CAAC,EAAE;oBACnC,kCAAkC;oBAClC,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,IAAI,CAAC,YAAY;oBACvB,SAAS,EAAE,uBAAuB;oBAClC,IAAI,EAAE;wBACJ,eAAe,EAAE,kBAAkB;6BAChC,GAAG,CAAC,WAAW,CAAC,EAAE;;4BACjB,OAAA,IAAA,uBAAa,EAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,YAAY,CAAC;gCACnD,CAAC,CAAC,UAAU,MAAA,WAAW,CAAC,SAAS,EAAE,0CAAE,WAAqB,EAAE;gCAC5D,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,CAAA;yBAAA,CACtC;6BACA,IAAI,CAAC,KAAK,CAAC;qBACf;oBACD,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,CAAC,KAAK;gCACP,OAAO,SAAS,CACd,KAAK,EACL,IAAI,EACJ,kBAAkB,EAClB,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,QAAQ,EAAE,CACvB,CAAC;4BACJ,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,eAAe,EAAE,qBAAqB;SACvC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}