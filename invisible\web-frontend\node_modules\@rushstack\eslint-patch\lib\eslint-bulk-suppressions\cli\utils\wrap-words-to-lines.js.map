{"version": 3, "file": "wrap-words-to-lines.js", "sourceRoot": "", "sources": ["../../../../src/eslint-bulk-suppressions/cli/utils/wrap-words-to-lines.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAuC3D,4CAyEC;AAzED,SAAgB,gBAAgB,CAC9B,IAAY,EACZ,aAAsB,EACtB,kBAAoC;;IAEpC,IAAI,UAAkB,CAAC;IACvB,QAAQ,OAAO,kBAAkB,EAAE,CAAC;QAClC,KAAK,QAAQ;YACX,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC;YAC5C,MAAM;QACR,KAAK,QAAQ;YACX,UAAU,GAAG,kBAAkB,CAAC;YAChC,MAAM;QACR;YACE,UAAU,GAAG,EAAE,CAAC;YAChB,MAAM;IACV,CAAC;IAED,MAAM,gBAAgB,GAAW,UAAU,CAAC,MAAM,CAAC;IAEnD,IAAI,CAAC,aAAa,EAAE,CAAC;QACnB,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;IACpD,CAAC;IAED,4FAA4F;IAC5F,+DAA+D;IAC/D,MAAM,KAAK,GAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;IAE5C,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACzB,IAAI,IAAI,CAAC,MAAM,GAAG,gBAAgB,IAAI,aAAa,EAAE,CAAC;YACpD,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC;QACvC,CAAC;aAAM,CAAC;YACN,MAAM,oBAAoB,GAAW,CAAA,MAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAG,CAAC,CAAC,KAAI,EAAE,CAAC;YACnE,MAAM,gBAAgB,GAAW,MAAM,CAAC;YACxC,IAAI,sBAAsB,GAA2B,IAAI,CAAC;YAC1D,IAAI,uBAAoD,CAAC;YACzD,IAAI,qBAAqB,GAAW,oBAAoB,CAAC,MAAM,CAAC;YAChE,IAAI,oBAAoB,GAAY,KAAK,CAAC;YAC1C,OAAO,CAAC,sBAAsB,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;gBACvE,IAAI,sBAAsB,CAAC,KAAK,GAAG,gBAAgB,GAAG,qBAAqB,GAAG,aAAa,EAAE,CAAC;oBAC5F,IAAI,mBAAgD,CAAC;oBACrD,IACE,CAAC,uBAAuB;wBACxB,mFAAmF;wBACnF,oBAAoB,EACpB,CAAC;wBACD,mBAAmB,GAAG,sBAAsB,CAAC;oBAC/C,CAAC;yBAAM,CAAC;wBACN,mBAAmB,GAAG,uBAAuB,CAAC;oBAChD,CAAC;oBAED,YAAY,CAAC,IAAI,CACf,UAAU;wBACR,oBAAoB;wBACpB,IAAI,CAAC,SAAS,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,KAAK,CAAC,CACnE,CAAC;oBACF,oBAAoB,GAAG,mBAAmB,CAAC,KAAK,GAAG,qBAAqB,GAAG,aAAa,CAAC;oBACzF,qBAAqB,GAAG,mBAAmB,CAAC,KAAK,GAAG,mBAAmB,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;gBACpF,CAAC;qBAAM,CAAC;oBACN,oBAAoB,GAAG,KAAK,CAAC;gBAC/B,CAAC;gBAED,uBAAuB,GAAG,sBAAsB,CAAC;YACnD,CAAC;YAED,IAAI,qBAAqB,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;gBACxC,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC,CAAC;YAC/F,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,YAAY,CAAC;AACtB,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON><PERSON><PERSON> in the project root for license information.\n\n// ----------------------------------------------------------------------------------------------------------\n// TO AVOID EXTRA DEPENDENCIES, THE CODE IN THIS FILE WAS BORROWED FROM:\n//\n// rushstack/libraries/terminal/src/PrintUtilities.ts\n//\n// KEEP IT IN SYNC WITH THAT FILE.\n// ----------------------------------------------------------------------------------------------------------\n\n/**\n * Applies word wrapping and returns an array of lines.\n *\n * @param text - The text to wrap\n * @param maxLineLength - The maximum length of a line, defaults to the console width\n * @param indent - The number of spaces to indent the wrapped lines, defaults to 0\n */\nexport function wrapWordsToLines(text: string, maxLineLength?: number, indent?: number): string[];\n/**\n * Applies word wrapping and returns an array of lines.\n *\n * @param text - The text to wrap\n * @param maxLineLength - The maximum length of a line, defaults to the console width\n * @param linePrefix - The string to prefix each line with, defaults to ''\n */\nexport function wrapWordsToLines(text: string, maxLineLength?: number, linePrefix?: string): string[];\n/**\n * Applies word wrapping and returns an array of lines.\n *\n * @param text - The text to wrap\n * @param maxLineLength - The maximum length of a line, defaults to the console width\n * @param indentOrLinePrefix - The number of spaces to indent the wrapped lines or the string to prefix\n * each line with, defaults to no prefix\n */\nexport function wrapWordsToLines(\n  text: string,\n  maxLineLength?: number,\n  indentOrLinePrefix?: number | string\n): string[];\nexport function wrapWordsToLines(\n  text: string,\n  maxLineLength?: number,\n  indentOrLinePrefix?: number | string\n): string[] {\n  let linePrefix: string;\n  switch (typeof indentOrLinePrefix) {\n    case 'number':\n      linePrefix = ' '.repeat(indentOrLinePrefix);\n      break;\n    case 'string':\n      linePrefix = indentOrLinePrefix;\n      break;\n    default:\n      linePrefix = '';\n      break;\n  }\n\n  const linePrefixLength: number = linePrefix.length;\n\n  if (!maxLineLength) {\n    maxLineLength = process.stdout.getWindowSize()[0];\n  }\n\n  // Apply word wrapping and the provided line prefix, while also respecting existing newlines\n  // and prefix spaces that may exist in the text string already.\n  const lines: string[] = text.split(/\\r?\\n/);\n\n  const wrappedLines: string[] = [];\n  for (const line of lines) {\n    if (line.length + linePrefixLength <= maxLineLength) {\n      wrappedLines.push(linePrefix + line);\n    } else {\n      const lineAdditionalPrefix: string = line.match(/^\\s*/)?.[0] || '';\n      const whitespaceRegexp: RegExp = /\\s+/g;\n      let currentWhitespaceMatch: RegExpExecArray | null = null;\n      let previousWhitespaceMatch: RegExpExecArray | undefined;\n      let currentLineStartIndex: number = lineAdditionalPrefix.length;\n      let previousBreakRanOver: boolean = false;\n      while ((currentWhitespaceMatch = whitespaceRegexp.exec(line)) !== null) {\n        if (currentWhitespaceMatch.index + linePrefixLength - currentLineStartIndex > maxLineLength) {\n          let whitespaceToSplitAt: RegExpExecArray | undefined;\n          if (\n            !previousWhitespaceMatch ||\n            // Handle the case where there are two words longer than the maxLineLength in a row\n            previousBreakRanOver\n          ) {\n            whitespaceToSplitAt = currentWhitespaceMatch;\n          } else {\n            whitespaceToSplitAt = previousWhitespaceMatch;\n          }\n\n          wrappedLines.push(\n            linePrefix +\n              lineAdditionalPrefix +\n              line.substring(currentLineStartIndex, whitespaceToSplitAt.index)\n          );\n          previousBreakRanOver = whitespaceToSplitAt.index - currentLineStartIndex > maxLineLength;\n          currentLineStartIndex = whitespaceToSplitAt.index + whitespaceToSplitAt[0].length;\n        } else {\n          previousBreakRanOver = false;\n        }\n\n        previousWhitespaceMatch = currentWhitespaceMatch;\n      }\n\n      if (currentLineStartIndex < line.length) {\n        wrappedLines.push(linePrefix + lineAdditionalPrefix + line.substring(currentLineStartIndex));\n      }\n    }\n  }\n\n  return wrappedLines;\n}\n"]}