{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Split = createLucideIcon(\"Split\", [[\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M8 3H3v5\",\n  key: \"15dfkv\"\n}], [\"path\", {\n  d: \"M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3\",\n  key: \"1qrqzj\"\n}], [\"path\", {\n  d: \"m15 9 6-6\",\n  key: \"ko1vev\"\n}]]);\nexport { Split as default };", "map": {"version": 3, "names": ["Split", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\split.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Split\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgM2g1djUiIC8+CiAgPHBhdGggZD0iTTggM0gzdjUiIC8+CiAgPHBhdGggZD0iTTEyIDIydi04LjNhNCA0IDAgMCAwLTEuMTcyLTIuODcyTDMgMyIgLz4KICA8cGF0aCBkPSJtMTUgOSA2LTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/split\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Split = createLucideIcon('Split', [\n  ['path', { d: 'M16 3h5v5', key: '1806ms' }],\n  ['path', { d: 'M8 3H3v5', key: '15dfkv' }],\n  ['path', { d: 'M12 22v-8.3a4 4 0 0 0-1.172-2.872L3 3', key: '1qrqzj' }],\n  ['path', { d: 'm15 9 6-6', key: 'ko1vev' }],\n]);\n\nexport default Split;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,uCAAyC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtE,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}