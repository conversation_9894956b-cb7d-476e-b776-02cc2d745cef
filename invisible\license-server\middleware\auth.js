const jwt = require('jsonwebtoken');
const User = require('../models/User');

// 🔐 JWT SECRET
const JWT_SECRET = process.env.JWT_SECRET || 'invisible-assessment-tool-super-secret-key-2024';
const JWT_EXPIRE = process.env.JWT_EXPIRE || '30d';

// 🔑 GENERATE JWT TOKEN
const generateToken = (userId) => {
    return jwt.sign(
        { 
            userId,
            timestamp: Date.now()
        },
        JWT_SECRET,
        { 
            expiresIn: JWT_EXPIRE,
            issuer: 'invisible-assessment-tool',
            audience: 'license-system'
        }
    );
};

// 🛡️ VERIFY JWT TOKEN MIDDLEWARE
const verifyToken = async (req, res, next) => {
    try {
        // Get token from header
        const authHeader = req.header('Authorization');
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. No valid token provided.'
            });
        }
        
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        
        // Verify token
        const decoded = jwt.verify(token, JWT_SECRET);
        
        // Check if user exists
        const user = await User.findById(decoded.userId);
        if (!user || !user.isActive) {
            return res.status(401).json({
                success: false,
                message: 'Access denied. User not found or inactive.'
            });
        }
        
        // Add user to request
        req.user = user;
        req.userId = decoded.userId;
        
        next();
    } catch (error) {
        console.error('❌ Token verification error:', error);
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                success: false,
                message: 'Access denied. Token has expired.'
            });
        }
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                success: false,
                message: 'Access denied. Invalid token.'
            });
        }
        
        return res.status(500).json({
            success: false,
            message: 'Token verification failed.'
        });
    }
};

module.exports = {
    generateToken,
    verifyToken,
    JWT_SECRET,
    JWT_EXPIRE
};
