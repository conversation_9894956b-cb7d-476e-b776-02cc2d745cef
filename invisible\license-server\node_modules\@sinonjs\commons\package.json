{"name": "@sinonjs/commons", "version": "3.0.1", "description": "Simple functions shared among the sinon end user libraries", "main": "lib/index.js", "types": "./types/index.d.ts", "scripts": {"build": "rm -rf types && tsc", "lint": "eslint .", "precommit": "lint-staged", "test": "mocha --recursive -R dot \"lib/**/*.test.js\"", "test-check-coverage": "npm run test-coverage && nyc check-coverage --branches 100 --functions 100 --lines 100", "test-coverage": "nyc --reporter text --reporter html --reporter lcovonly npm run test", "prepublishOnly": "npm run build", "prettier:check": "prettier --check '**/*.{js,css,md}'", "prettier:write": "prettier --write '**/*.{js,css,md}'", "preversion": "npm run test-check-coverage", "version": "changes --commits --footer", "postversion": "git push --follow-tags && npm publish", "prepare": "husky install"}, "repository": {"type": "git", "url": "git+https://github.com/sinonjs/commons.git"}, "files": ["lib", "types"], "author": "", "license": "BSD-3-<PERSON><PERSON>", "bugs": {"url": "https://github.com/sinonjs/commons/issues"}, "homepage": "https://github.com/sinonjs/commons#readme", "lint-staged": {"*.{js,css,md}": "prettier --check", "*.js": "eslint"}, "devDependencies": {"@sinonjs/eslint-config": "^4.0.6", "@sinonjs/eslint-plugin-no-prototype-methods": "^0.1.0", "@sinonjs/referee-sinon": "^10.1.0", "@studio/changes": "^2.2.0", "husky": "^6.0.0", "jsverify": "0.8.4", "knuth-shuffle": "^1.0.8", "lint-staged": "^13.0.3", "mocha": "^10.1.0", "nyc": "^15.1.0", "prettier": "^2.7.1", "typescript": "^4.8.4"}, "dependencies": {"type-detect": "4.0.8"}}