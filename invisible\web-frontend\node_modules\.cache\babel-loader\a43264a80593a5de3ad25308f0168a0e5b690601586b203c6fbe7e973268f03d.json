{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Castle = createLucideIcon(\"Castle\", [[\"path\", {\n  d: \"M22 20v-9H2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2Z\",\n  key: \"109fe4\"\n}], [\"path\", {\n  d: \"M18 11V4H6v7\",\n  key: \"mon5oj\"\n}], [\"path\", {\n  d: \"M15 22v-4a3 3 0 0 0-3-3v0a3 3 0 0 0-3 3v4\",\n  key: \"jdggr9\"\n}], [\"path\", {\n  d: \"M22 11V9\",\n  key: \"3zbp94\"\n}], [\"path\", {\n  d: \"M2 11V9\",\n  key: \"1x5rnq\"\n}], [\"path\", {\n  d: \"M6 4V2\",\n  key: \"1rsq15\"\n}], [\"path\", {\n  d: \"M18 4V2\",\n  key: \"1jsdo1\"\n}], [\"path\", {\n  d: \"M10 4V2\",\n  key: \"75d9ly\"\n}], [\"path\", {\n  d: \"M14 4V2\",\n  key: \"8nj3z6\"\n}]]);\nexport { Castle as default };", "map": {"version": 3, "names": ["Castle", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\castle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Castle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMjB2LTlIMnY5YTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMloiIC8+CiAgPHBhdGggZD0iTTE4IDExVjRINnY3IiAvPgogIDxwYXRoIGQ9Ik0xNSAyMnYtNGEzIDMgMCAwIDAtMy0zdjBhMyAzIDAgMCAwLTMgM3Y0IiAvPgogIDxwYXRoIGQ9Ik0yMiAxMVY5IiAvPgogIDxwYXRoIGQ9Ik0yIDExVjkiIC8+CiAgPHBhdGggZD0iTTYgNFYyIiAvPgogIDxwYXRoIGQ9Ik0xOCA0VjIiIC8+CiAgPHBhdGggZD0iTTEwIDRWMiIgLz4KICA8cGF0aCBkPSJNMTQgNFYyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/castle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Castle = createLucideIcon('Castle', [\n  [\n    'path',\n    { d: 'M22 20v-9H2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2Z', key: '109fe4' },\n  ],\n  ['path', { d: 'M18 11V4H6v7', key: 'mon5oj' }],\n  ['path', { d: 'M15 22v-4a3 3 0 0 0-3-3v0a3 3 0 0 0-3 3v4', key: 'jdggr9' }],\n  ['path', { d: 'M22 11V9', key: '3zbp94' }],\n  ['path', { d: 'M2 11V9', key: '1x5rnq' }],\n  ['path', { d: 'M6 4V2', key: '1rsq15' }],\n  ['path', { d: 'M18 4V2', key: '1jsdo1' }],\n  ['path', { d: 'M10 4V2', key: '75d9ly' }],\n  ['path', { d: 'M14 4V2', key: '8nj3z6' }],\n]);\n\nexport default Castle;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CACE,QACA;EAAEC,CAAA,EAAG,+CAAiD;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}