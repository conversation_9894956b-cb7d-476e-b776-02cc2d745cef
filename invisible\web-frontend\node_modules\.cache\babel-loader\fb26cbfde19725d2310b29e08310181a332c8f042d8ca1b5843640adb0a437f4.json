{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignVerticalDistributeStart = createLucideIcon(\"AlignVerticalDistributeStart\", [[\"rect\", {\n  width: \"14\",\n  height: \"6\",\n  x: \"5\",\n  y: \"14\",\n  rx: \"2\",\n  key: \"jmoj9s\"\n}], [\"rect\", {\n  width: \"10\",\n  height: \"6\",\n  x: \"7\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"aza5on\"\n}], [\"path\", {\n  d: \"M2 14h20\",\n  key: \"myj16y\"\n}], [\"path\", {\n  d: \"M2 4h20\",\n  key: \"mda7wb\"\n}]]);\nexport { AlignVerticalDistributeStart as default };", "map": {"version": 3, "names": ["AlignVerticalDistributeStart", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\align-vertical-distribute-start.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignVerticalDistributeStart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTQiIGhlaWdodD0iNiIgeD0iNSIgeT0iMTQiIHJ4PSIyIiAvPgogIDxyZWN0IHdpZHRoPSIxMCIgaGVpZ2h0PSI2IiB4PSI3IiB5PSI0IiByeD0iMiIgLz4KICA8cGF0aCBkPSJNMiAxNGgyMCIgLz4KICA8cGF0aCBkPSJNMiA0aDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/align-vertical-distribute-start\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignVerticalDistributeStart = createLucideIcon(\n  'AlignVerticalDistributeStart',\n  [\n    [\n      'rect',\n      { width: '14', height: '6', x: '5', y: '14', rx: '2', key: 'jmoj9s' },\n    ],\n    [\n      'rect',\n      { width: '10', height: '6', x: '7', y: '4', rx: '2', key: 'aza5on' },\n    ],\n    ['path', { d: 'M2 14h20', key: 'myj16y' }],\n    ['path', { d: 'M2 4h20', key: 'mda7wb' }],\n  ],\n);\n\nexport default AlignVerticalDistributeStart;\n"], "mappings": ";;;;;AAaA,MAAMA,4BAA+B,GAAAC,gBAAA,CACnC,gCACA,CACE,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CACE,QACA;EAAEL,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,EAE5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}