<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Payment System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .container {
            background: #2a2a2a;
            padding: 30px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #45a049;
        }
        .result {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .success {
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left: 4px solid #f44336;
        }
        input {
            padding: 10px;
            margin: 5px;
            border: 1px solid #555;
            background: #333;
            color: white;
            border-radius: 5px;
            width: 300px;
        }
    </style>
</head>
<body>
    <h1>🧪 Invisible Assessment Tool - Payment Test</h1>
    
    <div class="container">
        <h2>🔑 User Authentication</h2>
        <input type="email" id="email" placeholder="Email" value="<EMAIL>">
        <input type="password" id="password" placeholder="Password" value="your_password">
        <button onclick="login()">Login</button>
        <div id="authResult" class="result"></div>
    </div>

    <div class="container">
        <h2>💳 Test Payment Creation</h2>
        <button onclick="createPayment()">Create ₹10 Payment</button>
        <div id="paymentResult" class="result"></div>
    </div>

    <div class="container">
        <h2>📥 Test License Creation</h2>
        <button onclick="createTestLicense()">Create Test License (Direct)</button>
        <div id="licenseResult" class="result"></div>
    </div>

    <div class="container">
        <h2>📁 Test Download</h2>
        <input type="text" id="licenseKey" placeholder="License Key">
        <button onclick="createDownloadLink()">Create Download Link</button>
        <div id="downloadResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5002/api';
        let authToken = '';

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ email, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    authToken = data.data.token;
                    document.getElementById('authResult').innerHTML = `✅ Login successful!\nToken: ${authToken.substring(0, 50)}...`;
                    document.getElementById('authResult').className = 'result success';
                } else {
                    document.getElementById('authResult').innerHTML = `❌ Login failed: ${data.message}`;
                    document.getElementById('authResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('authResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('authResult').className = 'result error';
            }
        }

        async function createPayment() {
            if (!authToken) {
                alert('Please login first!');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/payments/create`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ tier: 1 })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('paymentResult').innerHTML = `✅ Payment created!\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('paymentResult').className = 'result success';
                } else {
                    document.getElementById('paymentResult').innerHTML = `❌ Payment failed: ${data.message}`;
                    document.getElementById('paymentResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('paymentResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('paymentResult').className = 'result error';
            }
        }

        async function createTestLicense() {
            try {
                const response = await fetch(`${API_BASE}/payments/create-test-license/683db215d6b19eec4bf84c31`);
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('licenseResult').innerHTML = `✅ License created!\n${JSON.stringify(data, null, 2)}`;
                    document.getElementById('licenseResult').className = 'result success';
                    document.getElementById('licenseKey').value = data.data.licenseKey;
                } else {
                    document.getElementById('licenseResult').innerHTML = `❌ License failed: ${data.message}`;
                    document.getElementById('licenseResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('licenseResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('licenseResult').className = 'result error';
            }
        }

        async function createDownloadLink() {
            const licenseKey = document.getElementById('licenseKey').value;
            
            if (!licenseKey) {
                alert('Please enter a license key!');
                return;
            }
            
            if (!authToken) {
                alert('Please login first!');
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/download/create-link`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    },
                    body: JSON.stringify({ licenseKey })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    const downloadUrl = `${API_BASE}${data.data.downloadUrl}`;
                    document.getElementById('downloadResult').innerHTML = `✅ Download link created!\n${JSON.stringify(data, null, 2)}\n\nDirect download: ${downloadUrl}`;
                    document.getElementById('downloadResult').className = 'result success';
                    
                    // Create download button
                    const downloadBtn = document.createElement('button');
                    downloadBtn.textContent = 'Download Software';
                    downloadBtn.onclick = () => window.open(downloadUrl, '_blank');
                    document.getElementById('downloadResult').appendChild(downloadBtn);
                } else {
                    document.getElementById('downloadResult').innerHTML = `❌ Download failed: ${data.message}`;
                    document.getElementById('downloadResult').className = 'result error';
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('downloadResult').className = 'result error';
            }
        }
    </script>
</body>
</html>
