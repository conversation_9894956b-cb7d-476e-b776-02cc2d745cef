{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bird = createLucideIcon(\"<PERSON>\", [[\"path\", {\n  d: \"M16 7h.01\",\n  key: \"1kdx03\"\n}], [\"path\", {\n  d: \"M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20\",\n  key: \"oj1oa8\"\n}], [\"path\", {\n  d: \"m20 7 2 .5-2 .5\",\n  key: \"12nv4d\"\n}], [\"path\", {\n  d: \"M10 18v3\",\n  key: \"1yea0a\"\n}], [\"path\", {\n  d: \"M14 17.75V21\",\n  key: \"1pymcb\"\n}], [\"path\", {\n  d: \"M7 18a6 6 0 0 0 3.84-10.61\",\n  key: \"1npnn0\"\n}]]);\nexport { Bird as default };", "map": {"version": 3, "names": ["<PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\bird.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bird\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2guMDEiIC8+CiAgPHBhdGggZD0iTTMuNCAxOEgxMmE4IDggMCAwIDAgOC04VjdhNCA0IDAgMCAwLTcuMjgtMi4zTDIgMjAiIC8+CiAgPHBhdGggZD0ibTIwIDcgMiAuNS0yIC41IiAvPgogIDxwYXRoIGQ9Ik0xMCAxOHYzIiAvPgogIDxwYXRoIGQ9Ik0xNCAxNy43NVYyMSIgLz4KICA8cGF0aCBkPSJNNyAxOGE2IDYgMCAwIDAgMy44NC0xMC42MSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bird\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bird = createLucideIcon('Bird', [\n  ['path', { d: 'M16 7h.01', key: '1kdx03' }],\n  [\n    'path',\n    { d: 'M3.4 18H12a8 8 0 0 0 8-8V7a4 4 0 0 0-7.28-2.3L2 20', key: 'oj1oa8' },\n  ],\n  ['path', { d: 'm20 7 2 .5-2 .5', key: '12nv4d' }],\n  ['path', { d: 'M10 18v3', key: '1yea0a' }],\n  ['path', { d: 'M14 17.75V21', key: '1pymcb' }],\n  ['path', { d: 'M7 18a6 6 0 0 0 3.84-10.61', key: '1npnn0' }],\n]);\n\nexport default Bird;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG,oDAAsD;EAAAC,GAAA,EAAK;AAAS,EAC3E,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,iBAAmB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}