{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CalendarDays = createLucideIcon(\"CalendarDays\", [[\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"4\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"eu3xkr\"\n}], [\"line\", {\n  x1: \"16\",\n  x2: \"16\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"m3sa8f\"\n}], [\"line\", {\n  x1: \"8\",\n  x2: \"8\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"18kwsl\"\n}], [\"line\", {\n  x1: \"3\",\n  x2: \"21\",\n  y1: \"10\",\n  y2: \"10\",\n  key: \"xt86sb\"\n}], [\"path\", {\n  d: \"M8 14h.01\",\n  key: \"6423bh\"\n}], [\"path\", {\n  d: \"M12 14h.01\",\n  key: \"1etili\"\n}], [\"path\", {\n  d: \"M16 14h.01\",\n  key: \"1gbofw\"\n}], [\"path\", {\n  d: \"M8 18h.01\",\n  key: \"lrp35t\"\n}], [\"path\", {\n  d: \"M12 18h.01\",\n  key: \"mhygvu\"\n}], [\"path\", {\n  d: \"M16 18h.01\",\n  key: \"kzsmim\"\n}]]);\nexport { CalendarDays as default };", "map": {"version": 3, "names": ["CalendarDays", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\calendar-days.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CalendarDays\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjQiIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iOCIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIzIiB4Mj0iMjEiIHkxPSIxMCIgeTI9IjEwIiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xNiAxNGguMDEiIC8+CiAgPHBhdGggZD0iTTggMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMiAxOGguMDEiIC8+CiAgPHBhdGggZD0iTTE2IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calendar-days\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CalendarDays = createLucideIcon('CalendarDays', [\n  [\n    'rect',\n    {\n      width: '18',\n      height: '18',\n      x: '3',\n      y: '4',\n      rx: '2',\n      ry: '2',\n      key: 'eu3xkr',\n    },\n  ],\n  ['line', { x1: '16', x2: '16', y1: '2', y2: '6', key: 'm3sa8f' }],\n  ['line', { x1: '8', x2: '8', y1: '2', y2: '6', key: '18kwsl' }],\n  ['line', { x1: '3', x2: '21', y1: '10', y2: '10', key: 'xt86sb' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M16 14h.01', key: '1gbofw' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M16 18h.01', key: 'kzsmim' }],\n]);\n\nexport default CalendarDays;\n"], "mappings": ";;;;;AAaM,MAAAA,YAAA,GAAeC,gBAAA,CAAiB,cAAgB,GACpD,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAEK,CAAA,EAAG,WAAa;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEK,CAAA,EAAG,YAAc;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEK,CAAA,EAAG,YAAc;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEK,CAAA,EAAG,WAAa;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEK,CAAA,EAAG,YAAc;EAAAL,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEK,CAAA,EAAG,YAAc;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}