{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Download.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Download as DownloadIcon, Shield, Key, Monitor, CheckCircle, AlertCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DownloadContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = DownloadContainer;\nconst DownloadContent = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n`;\n_c2 = DownloadContent;\nconst SuccessHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n  padding: 40px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(76, 175, 80, 0.3);\n`;\n_c3 = SuccessHeader;\nconst SuccessIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n_c4 = SuccessIcon;\nconst SuccessTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n_c5 = SuccessTitle;\nconst SuccessMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n_c6 = SuccessMessage;\nconst LicenseInfo = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c7 = LicenseInfo;\nconst LicenseTitle = styled.h2`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  color: #4CAF50;\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n_c8 = LicenseTitle;\nconst LicenseDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c9 = LicenseDetails;\nconst LicenseDetail = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  \n  .label {\n    font-size: 0.9rem;\n    opacity: 0.7;\n    margin-bottom: 5px;\n  }\n  \n  .value {\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: #4CAF50;\n  }\n`;\n_c0 = LicenseDetail;\nconst LicenseKey = styled.div`\n  background: rgba(0, 0, 0, 0.3);\n  padding: 15px;\n  border-radius: 10px;\n  font-family: 'Courier New', monospace;\n  font-size: 1.1rem;\n  text-align: center;\n  margin-top: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  .copy-btn {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    background: #4CAF50;\n    color: white;\n    border: none;\n    padding: 5px 10px;\n    border-radius: 5px;\n    cursor: pointer;\n    font-size: 0.8rem;\n  }\n`;\n_c1 = LicenseKey;\nconst DownloadSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c10 = DownloadSection;\nconst DownloadButton = styled.button`\n  width: 100%;\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n  border: none;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n_c11 = DownloadButton;\nconst InstructionsSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c12 = InstructionsSection;\nconst InstructionsList = styled.ol`\n  margin-left: 20px;\n  \n  li {\n    margin-bottom: 15px;\n    line-height: 1.6;\n    \n    strong {\n      color: #4CAF50;\n    }\n  }\n`;\n_c13 = InstructionsList;\nconst Download = () => {\n  _s();\n  var _licenseData$plan, _licenseData$plan2, _downloadStatus$licen, _downloadStatus$licen2, _downloadStatus$licen3, _downloadStatus$licen4, _downloadStatus$licen5, _downloadStatus$licen6, _downloadStatus$licen7, _downloadStatus$licen8, _downloadStatus$licen9, _downloadStatus$licen0, _downloadStatus$licen1, _downloadStatus$licen10;\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [downloading, setDownloading] = useState(false);\n  const [licenseData, setLicenseData] = useState(null);\n  const [downloadStatus, setDownloadStatus] = useState(null);\n  useEffect(() => {\n    var _location$state;\n    // Get license data from navigation state or fetch from API\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.licenseKey) {\n      setLicenseData({\n        licenseKey: location.state.licenseKey,\n        plan: location.state.plan\n      });\n    } else {\n      fetchLatestLicense();\n    }\n\n    // Fetch download status\n    fetchDownloadStatus();\n  }, [location.state]);\n  const fetchDownloadStatus = async () => {\n    try {\n      const response = await axios.get('/download/status');\n      if (response.data.success) {\n        setDownloadStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching download status:', error);\n    }\n  };\n  const fetchLatestLicense = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success && response.data.data.licenses.length > 0) {\n        const latestLicense = response.data.data.licenses[0];\n        setLicenseData({\n          licenseKey: latestLicense.licenseKey,\n          plan: {\n            title: latestLicense.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',\n            screenshots: latestLicense.screenshotsLimit\n          }\n        });\n      } else {\n        toast.error('No valid license found. Please purchase a license first.');\n        navigate('/purchase');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch license information');\n      navigate('/purchase');\n    }\n  };\n  const copyLicenseKey = () => {\n    if (licenseData !== null && licenseData !== void 0 && licenseData.licenseKey) {\n      navigator.clipboard.writeText(licenseData.licenseKey);\n      toast.success('License key copied to clipboard!');\n    }\n  };\n  const downloadSoftware = async () => {\n    try {\n      setDownloading(true);\n\n      // Create secure download link\n      const response = await axios.post('/download/create-link', {\n        licenseKey: licenseData.licenseKey\n      });\n      if (response.data.success) {\n        const {\n          downloadUrl,\n          fileName\n        } = response.data.data;\n\n        // Create download link\n        const link = document.createElement('a');\n        link.href = `http://localhost:5002${downloadUrl}`;\n        link.setAttribute('download', fileName);\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast.success('Download started! Check your downloads folder.');\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Download error:', error);\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Download failed. Please try again or contact support.';\n\n      // Check if it's a download limit error\n      if (errorMessage.includes('Download limit reached')) {\n        toast.error('Download limit reached! You can only download once per license.');\n        // Refresh download status\n        fetchDownloadStatus();\n      } else {\n        toast.error(errorMessage);\n      }\n    } finally {\n      setDownloading(false);\n    }\n  };\n  if (!licenseData) {\n    return /*#__PURE__*/_jsxDEV(DownloadContainer, {\n      children: /*#__PURE__*/_jsxDEV(DownloadContent, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '60px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\",\n            style: {\n              margin: '0 auto 20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading license information...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DownloadContainer, {\n    children: /*#__PURE__*/_jsxDEV(DownloadContent, {\n      children: [/*#__PURE__*/_jsxDEV(SuccessHeader, {\n        children: [/*#__PURE__*/_jsxDEV(SuccessIcon, {\n          children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 64\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SuccessTitle, {\n          children: \"Payment Successful!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SuccessMessage, {\n          children: \"Your license has been activated and you can now download the software.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LicenseInfo, {\n        children: [/*#__PURE__*/_jsxDEV(LicenseTitle, {\n          children: [/*#__PURE__*/_jsxDEV(Key, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 13\n          }, this), \"License Information\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LicenseDetails, {\n          children: [/*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: (_licenseData$plan = licenseData.plan) === null || _licenseData$plan === void 0 ? void 0 : _licenseData$plan.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 320,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Screenshots\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: [(_licenseData$plan2 = licenseData.plan) === null || _licenseData$plan2 === void 0 ? void 0 : _licenseData$plan2.screenshots, \"/month\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Your License Key:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseKey, {\n            children: [licenseData.licenseKey, /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"copy-btn\",\n              onClick: copyLicenseKey,\n              children: \"Copy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DownloadSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            marginBottom: '20px',\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(DownloadIcon, {\n            size: 24,\n            style: {\n              marginRight: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), \"Download Software\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), downloadStatus && downloadStatus.licenses && downloadStatus.licenses.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads ? 'rgba(220, 53, 69, 0.1)' : 'rgba(40, 167, 69, 0.1)',\n            border: `1px solid ${downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads ? 'rgba(220, 53, 69, 0.3)' : 'rgba(40, 167, 69, 0.3)'}`,\n            borderRadius: '10px',\n            padding: '15px',\n            marginBottom: '20px',\n            textAlign: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads ? '#dc3545' : '#28a745',\n              fontWeight: '600',\n              marginBottom: '5px'\n            },\n            children: [\"Downloads: \", downloadStatus.licenses[0].downloadCount || 0, \" / \", downloadStatus.licenses[0].maxDownloads || 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 367,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.9rem',\n              opacity: 0.8\n            },\n            children: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads ? '❌ Download limit reached for this license' : '✅ Download available'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 355,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n          onClick: downloadSoftware,\n          disabled: downloading || (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen = downloadStatus.licenses) === null || _downloadStatus$licen === void 0 ? void 0 : (_downloadStatus$licen2 = _downloadStatus$licen[0]) === null || _downloadStatus$licen2 === void 0 ? void 0 : _downloadStatus$licen2.downloadCount) >= (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen3 = downloadStatus.licenses) === null || _downloadStatus$licen3 === void 0 ? void 0 : (_downloadStatus$licen4 = _downloadStatus$licen3[0]) === null || _downloadStatus$licen4 === void 0 ? void 0 : _downloadStatus$licen4.maxDownloads),\n          style: {\n            opacity: (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen5 = downloadStatus.licenses) === null || _downloadStatus$licen5 === void 0 ? void 0 : (_downloadStatus$licen6 = _downloadStatus$licen5[0]) === null || _downloadStatus$licen6 === void 0 ? void 0 : _downloadStatus$licen6.downloadCount) >= (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen7 = downloadStatus.licenses) === null || _downloadStatus$licen7 === void 0 ? void 0 : (_downloadStatus$licen8 = _downloadStatus$licen7[0]) === null || _downloadStatus$licen8 === void 0 ? void 0 : _downloadStatus$licen8.maxDownloads) ? 0.5 : 1,\n            cursor: (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen9 = downloadStatus.licenses) === null || _downloadStatus$licen9 === void 0 ? void 0 : (_downloadStatus$licen0 = _downloadStatus$licen9[0]) === null || _downloadStatus$licen0 === void 0 ? void 0 : _downloadStatus$licen0.downloadCount) >= (downloadStatus === null || downloadStatus === void 0 ? void 0 : (_downloadStatus$licen1 = downloadStatus.licenses) === null || _downloadStatus$licen1 === void 0 ? void 0 : (_downloadStatus$licen10 = _downloadStatus$licen1[0]) === null || _downloadStatus$licen10 === void 0 ? void 0 : _downloadStatus$licen10.maxDownloads) ? 'not-allowed' : 'pointer'\n          },\n          children: downloading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner\",\n              style: {\n                width: '20px',\n                height: '20px',\n                marginRight: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 17\n            }, this), \"Preparing Download...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DownloadIcon, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 17\n            }, this), \"Download Invisible Assessment Tool\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: 0.8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Shield, {\n            size: 16,\n            style: {\n              marginRight: '5px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 407,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"Secure download \\u2022 Hardware-bound installation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 406,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InstructionsSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            marginBottom: '20px',\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Monitor, {\n            size: 24,\n            style: {\n              marginRight: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), \"Installation Instructions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InstructionsList, {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Download\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 17\n            }, this), \" the software using the button above\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Run\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 17\n            }, this), \" the installer as administrator on your target computer\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 420,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Enter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 17\n            }, this), \" your license key when prompted during installation\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Complete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 17\n            }, this), \" the hardware binding process (one-time setup)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Launch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 17\n            }, this), \" the tool - it will start completely invisible\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 17\n            }, this), \" to show/hide the interface\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+H\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 425,\n              columnNumber: 17\n            }, this), \" to take screenshots\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+Enter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), \" to analyze screenshots with AI\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            padding: '15px',\n            background: 'rgba(255, 193, 7, 0.1)',\n            borderRadius: '10px',\n            border: '1px solid rgba(255, 193, 7, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 20,\n              style: {\n                marginRight: '10px',\n                color: '#FFC107'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: '#FFC107'\n              },\n              children: \"Important Notes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: '30px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"The software is bound to your hardware and cannot be transferred\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Keep your license key safe - you'll need it for support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 442,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"The tool works completely in stealth mode during assessments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Contact support if you encounter any installation issues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 300,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 299,\n    columnNumber: 5\n  }, this);\n};\n_s(Download, \"GJS0+k36Uxq0bcX1O/5BwEVOU2A=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c14 = Download;\nexport default Download;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"DownloadContainer\");\n$RefreshReg$(_c2, \"DownloadContent\");\n$RefreshReg$(_c3, \"SuccessHeader\");\n$RefreshReg$(_c4, \"SuccessIcon\");\n$RefreshReg$(_c5, \"SuccessTitle\");\n$RefreshReg$(_c6, \"SuccessMessage\");\n$RefreshReg$(_c7, \"LicenseInfo\");\n$RefreshReg$(_c8, \"LicenseTitle\");\n$RefreshReg$(_c9, \"LicenseDetails\");\n$RefreshReg$(_c0, \"LicenseDetail\");\n$RefreshReg$(_c1, \"LicenseKey\");\n$RefreshReg$(_c10, \"DownloadSection\");\n$RefreshReg$(_c11, \"DownloadButton\");\n$RefreshReg$(_c12, \"InstructionsSection\");\n$RefreshReg$(_c13, \"InstructionsList\");\n$RefreshReg$(_c14, \"Download\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "styled", "toast", "axios", "useAuth", "Download", "DownloadIcon", "Shield", "Key", "Monitor", "CheckCircle", "AlertCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DownloadContainer", "div", "_c", "DownloadContent", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "_c3", "SuccessIcon", "_c4", "SuccessTitle", "h1", "_c5", "SuccessMessage", "p", "_c6", "LicenseInfo", "_c7", "LicenseTitle", "h2", "_c8", "LicenseDetails", "_c9", "LicenseDetail", "_c0", "LicenseKey", "_c1", "DownloadSection", "_c10", "DownloadButton", "button", "_c11", "InstructionsSection", "_c12", "InstructionsList", "ol", "_c13", "_s", "_licenseData$plan", "_licenseData$plan2", "_downloadStatus$licen", "_downloadStatus$licen2", "_downloadStatus$licen3", "_downloadStatus$licen4", "_downloadStatus$licen5", "_downloadStatus$licen6", "_downloadStatus$licen7", "_downloadStatus$licen8", "_downloadStatus$licen9", "_downloadStatus$licen0", "_downloadStatus$licen1", "_downloadStatus$licen10", "user", "location", "navigate", "downloading", "setDownloading", "licenseData", "setLicenseData", "downloadStatus", "setDownloadStatus", "_location$state", "state", "licenseKey", "plan", "fetchLatestLicense", "fetchDownloadStatus", "response", "get", "data", "success", "error", "console", "licenses", "length", "latestLicense", "title", "tier", "screenshots", "screenshotsLimit", "copyLicenseKey", "navigator", "clipboard", "writeText", "downloadSoftware", "post", "downloadUrl", "fileName", "link", "document", "createElement", "href", "setAttribute", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "Error", "message", "_error$response", "_error$response$data", "errorMessage", "includes", "children", "textAlign", "padding", "className", "margin", "_jsxFileName", "lineNumber", "columnNumber", "size", "name", "onClick", "marginBottom", "alignItems", "marginRight", "background", "downloadCount", "maxDownloads", "border", "borderRadius", "color", "fontWeight", "fontSize", "opacity", "disabled", "cursor", "width", "height", "justifyContent", "marginTop", "marginLeft", "lineHeight", "_c14", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Download.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Download as DownloadIcon, Shield, Key, Monitor, CheckCircle, AlertCircle } from 'lucide-react';\n\nconst DownloadContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst DownloadContent = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n`;\n\nconst SuccessHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n  padding: 40px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(76, 175, 80, 0.3);\n`;\n\nconst SuccessIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n\nconst SuccessTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n\nconst SuccessMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n\nconst LicenseInfo = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst LicenseTitle = styled.h2`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  color: #4CAF50;\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n\nconst LicenseDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst LicenseDetail = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  \n  .label {\n    font-size: 0.9rem;\n    opacity: 0.7;\n    margin-bottom: 5px;\n  }\n  \n  .value {\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: #4CAF50;\n  }\n`;\n\nconst LicenseKey = styled.div`\n  background: rgba(0, 0, 0, 0.3);\n  padding: 15px;\n  border-radius: 10px;\n  font-family: 'Courier New', monospace;\n  font-size: 1.1rem;\n  text-align: center;\n  margin-top: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  .copy-btn {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    background: #4CAF50;\n    color: white;\n    border: none;\n    padding: 5px 10px;\n    border-radius: 5px;\n    cursor: pointer;\n    font-size: 0.8rem;\n  }\n`;\n\nconst DownloadSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst DownloadButton = styled.button`\n  width: 100%;\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n  border: none;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n\nconst InstructionsSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst InstructionsList = styled.ol`\n  margin-left: 20px;\n  \n  li {\n    margin-bottom: 15px;\n    line-height: 1.6;\n    \n    strong {\n      color: #4CAF50;\n    }\n  }\n`;\n\nconst Download = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [downloading, setDownloading] = useState(false);\n  const [licenseData, setLicenseData] = useState(null);\n  const [downloadStatus, setDownloadStatus] = useState(null);\n\n  useEffect(() => {\n    // Get license data from navigation state or fetch from API\n    if (location.state?.licenseKey) {\n      setLicenseData({\n        licenseKey: location.state.licenseKey,\n        plan: location.state.plan\n      });\n    } else {\n      fetchLatestLicense();\n    }\n\n    // Fetch download status\n    fetchDownloadStatus();\n  }, [location.state]);\n\n  const fetchDownloadStatus = async () => {\n    try {\n      const response = await axios.get('/download/status');\n      if (response.data.success) {\n        setDownloadStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching download status:', error);\n    }\n  };\n\n  const fetchLatestLicense = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success && response.data.data.licenses.length > 0) {\n        const latestLicense = response.data.data.licenses[0];\n        setLicenseData({\n          licenseKey: latestLicense.licenseKey,\n          plan: {\n            title: latestLicense.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',\n            screenshots: latestLicense.screenshotsLimit\n          }\n        });\n      } else {\n        toast.error('No valid license found. Please purchase a license first.');\n        navigate('/purchase');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch license information');\n      navigate('/purchase');\n    }\n  };\n\n  const copyLicenseKey = () => {\n    if (licenseData?.licenseKey) {\n      navigator.clipboard.writeText(licenseData.licenseKey);\n      toast.success('License key copied to clipboard!');\n    }\n  };\n\n  const downloadSoftware = async () => {\n    try {\n      setDownloading(true);\n\n      // Create secure download link\n      const response = await axios.post('/download/create-link', {\n        licenseKey: licenseData.licenseKey\n      });\n\n      if (response.data.success) {\n        const { downloadUrl, fileName } = response.data.data;\n\n        // Create download link\n        const link = document.createElement('a');\n        link.href = `http://localhost:5002${downloadUrl}`;\n        link.setAttribute('download', fileName);\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        toast.success('Download started! Check your downloads folder.');\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      console.error('Download error:', error);\n      const errorMessage = error.response?.data?.message || 'Download failed. Please try again or contact support.';\n\n      // Check if it's a download limit error\n      if (errorMessage.includes('Download limit reached')) {\n        toast.error('Download limit reached! You can only download once per license.');\n        // Refresh download status\n        fetchDownloadStatus();\n      } else {\n        toast.error(errorMessage);\n      }\n    } finally {\n      setDownloading(false);\n    }\n  };\n\n  if (!licenseData) {\n    return (\n      <DownloadContainer>\n        <DownloadContent>\n          <div style={{ textAlign: 'center', padding: '60px 0' }}>\n            <div className=\"loading-spinner\" style={{ margin: '0 auto 20px' }}></div>\n            <p>Loading license information...</p>\n          </div>\n        </DownloadContent>\n      </DownloadContainer>\n    );\n  }\n\n  return (\n    <DownloadContainer>\n      <DownloadContent>\n        <SuccessHeader>\n          <SuccessIcon>\n            <CheckCircle size={64} />\n          </SuccessIcon>\n          <SuccessTitle>Payment Successful!</SuccessTitle>\n          <SuccessMessage>\n            Your license has been activated and you can now download the software.\n          </SuccessMessage>\n        </SuccessHeader>\n\n        <LicenseInfo>\n          <LicenseTitle>\n            <Key size={24} />\n            License Information\n          </LicenseTitle>\n\n          <LicenseDetails>\n            <LicenseDetail>\n              <div className=\"label\">Plan</div>\n              <div className=\"value\">{licenseData.plan?.title}</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">Screenshots</div>\n              <div className=\"value\">{licenseData.plan?.screenshots}/month</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">Status</div>\n              <div className=\"value\">Active</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">User</div>\n              <div className=\"value\">{user?.name}</div>\n            </LicenseDetail>\n          </LicenseDetails>\n\n          <div>\n            <strong>Your License Key:</strong>\n            <LicenseKey>\n              {licenseData.licenseKey}\n              <button className=\"copy-btn\" onClick={copyLicenseKey}>\n                Copy\n              </button>\n            </LicenseKey>\n          </div>\n        </LicenseInfo>\n\n        <DownloadSection>\n          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>\n            <DownloadIcon size={24} style={{ marginRight: '10px' }} />\n            Download Software\n          </h2>\n\n          {/* 📊 DOWNLOAD STATUS DISPLAY */}\n          {downloadStatus && downloadStatus.licenses && downloadStatus.licenses.length > 0 && (\n            <div style={{\n              background: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads\n                ? 'rgba(220, 53, 69, 0.1)'\n                : 'rgba(40, 167, 69, 0.1)',\n              border: `1px solid ${downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads\n                ? 'rgba(220, 53, 69, 0.3)'\n                : 'rgba(40, 167, 69, 0.3)'}`,\n              borderRadius: '10px',\n              padding: '15px',\n              marginBottom: '20px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                color: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads\n                  ? '#dc3545'\n                  : '#28a745',\n                fontWeight: '600',\n                marginBottom: '5px'\n              }}>\n                Downloads: {downloadStatus.licenses[0].downloadCount || 0} / {downloadStatus.licenses[0].maxDownloads || 1}\n              </div>\n              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>\n                {downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads\n                  ? '❌ Download limit reached for this license'\n                  : '✅ Download available'\n                }\n              </div>\n            </div>\n          )}\n\n          <DownloadButton\n            onClick={downloadSoftware}\n            disabled={downloading || (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads)}\n            style={{\n              opacity: (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads) ? 0.5 : 1,\n              cursor: (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads) ? 'not-allowed' : 'pointer'\n            }}\n          >\n            {downloading ? (\n              <>\n                <div className=\"loading-spinner\" style={{ width: '20px', height: '20px', marginRight: '10px' }}></div>\n                Preparing Download...\n              </>\n            ) : (\n              <>\n                <DownloadIcon size={24} />\n                Download Invisible Assessment Tool\n              </>\n            )}\n          </DownloadButton>\n\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', opacity: 0.8 }}>\n            <Shield size={16} style={{ marginRight: '5px' }} />\n            <small>Secure download • Hardware-bound installation</small>\n          </div>\n        </DownloadSection>\n\n        <InstructionsSection>\n          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>\n            <Monitor size={24} style={{ marginRight: '10px' }} />\n            Installation Instructions\n          </h2>\n\n          <InstructionsList>\n            <li><strong>Download</strong> the software using the button above</li>\n            <li><strong>Run</strong> the installer as administrator on your target computer</li>\n            <li><strong>Enter</strong> your license key when prompted during installation</li>\n            <li><strong>Complete</strong> the hardware binding process (one-time setup)</li>\n            <li><strong>Launch</strong> the tool - it will start completely invisible</li>\n            <li><strong>Use Ctrl+B</strong> to show/hide the interface</li>\n            <li><strong>Use Ctrl+H</strong> to take screenshots</li>\n            <li><strong>Use Ctrl+Enter</strong> to analyze screenshots with AI</li>\n          </InstructionsList>\n\n          <div style={{\n            marginTop: '20px',\n            padding: '15px',\n            background: 'rgba(255, 193, 7, 0.1)',\n            borderRadius: '10px',\n            border: '1px solid rgba(255, 193, 7, 0.3)'\n          }}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>\n              <AlertCircle size={20} style={{ marginRight: '10px', color: '#FFC107' }} />\n              <strong style={{ color: '#FFC107' }}>Important Notes:</strong>\n            </div>\n            <ul style={{ marginLeft: '30px', lineHeight: '1.6' }}>\n              <li>The software is bound to your hardware and cannot be transferred</li>\n              <li>Keep your license key safe - you'll need it for support</li>\n              <li>The tool works completely in stealth mode during assessments</li>\n              <li>Contact support if you encounter any installation issues</li>\n            </ul>\n          </div>\n        </InstructionsSection>\n      </DownloadContent>\n    </DownloadContainer>\n  );\n};\n\nexport default Download;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExG,MAAMC,iBAAiB,GAAGf,MAAM,CAACgB,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,eAAe,GAAGlB,MAAM,CAACgB,GAAG;AAClC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,eAAe;AAKrB,MAAME,aAAa,GAAGpB,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,aAAa;AASnB,MAAME,WAAW,GAAGtB,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,WAAW;AAMjB,MAAME,YAAY,GAAGxB,MAAM,CAACyB,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,cAAc,GAAG3B,MAAM,CAAC4B,CAAC;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,cAAc;AAKpB,MAAMG,WAAW,GAAG9B,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGhC,MAAM,CAACiC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,YAAY;AAWlB,MAAMG,cAAc,GAAGnC,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAGrC,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAjBID,aAAa;AAmBnB,MAAME,UAAU,GAAGvC,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAxBID,UAAU;AA0BhB,MAAME,eAAe,GAAGzC,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAG3C,MAAM,CAAC4C,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7BIF,cAAc;AA+BpB,MAAMG,mBAAmB,GAAG9C,MAAM,CAACgB,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GANID,mBAAmB;AAQzB,MAAME,gBAAgB,GAAGhD,MAAM,CAACiD,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAXIF,gBAAgB;AAatB,MAAM5C,QAAQ,GAAGA,CAAA,KAAM;EAAA+C,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAG/D,OAAO,CAAC,CAAC;EAC1B,MAAMgE,QAAQ,GAAGrE,WAAW,CAAC,CAAC;EAC9B,MAAMsE,QAAQ,GAAGrE,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsE,WAAW,EAAEC,cAAc,CAAC,GAAG1E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2E,WAAW,EAAEC,cAAc,CAAC,GAAG5E,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAAC6E,cAAc,EAAEC,iBAAiB,CAAC,GAAG9E,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IAAA,IAAA8E,eAAA;IACd;IACA,KAAAA,eAAA,GAAIR,QAAQ,CAACS,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,UAAU,EAAE;MAC9BL,cAAc,CAAC;QACbK,UAAU,EAAEV,QAAQ,CAACS,KAAK,CAACC,UAAU;QACrCC,IAAI,EAAEX,QAAQ,CAACS,KAAK,CAACE;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLC,kBAAkB,CAAC,CAAC;IACtB;;IAEA;IACAC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACb,QAAQ,CAACS,KAAK,CAAC,CAAC;EAEpB,MAAMI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAC,kBAAkB,CAAC;MACpD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBV,iBAAiB,CAACO,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMN,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAM/E,KAAK,CAACgF,GAAG,CAAC,uBAAuB,CAAC;MACzD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACnE,MAAMC,aAAa,GAAGR,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC;QACpDf,cAAc,CAAC;UACbK,UAAU,EAAEY,aAAa,CAACZ,UAAU;UACpCC,IAAI,EAAE;YACJY,KAAK,EAAED,aAAa,CAACE,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,iBAAiB;YACxEC,WAAW,EAAEH,aAAa,CAACI;UAC7B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL5F,KAAK,CAACoF,KAAK,CAAC,0DAA0D,CAAC;QACvEjB,QAAQ,CAAC,WAAW,CAAC;MACvB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdpF,KAAK,CAACoF,KAAK,CAAC,qCAAqC,CAAC;MAClDjB,QAAQ,CAAC,WAAW,CAAC;IACvB;EACF,CAAC;EAED,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEM,UAAU,EAAE;MAC3BkB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,WAAW,CAACM,UAAU,CAAC;MACrD5E,KAAK,CAACmF,OAAO,CAAC,kCAAkC,CAAC;IACnD;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF5B,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMW,QAAQ,GAAG,MAAM/E,KAAK,CAACiG,IAAI,CAAC,uBAAuB,EAAE;QACzDtB,UAAU,EAAEN,WAAW,CAACM;MAC1B,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEgB,WAAW;UAAEC;QAAS,CAAC,GAAGpB,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEpD;QACA,MAAMmB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAG,wBAAwBL,WAAW,EAAE;QACjDE,IAAI,CAACI,YAAY,CAAC,UAAU,EAAEL,QAAQ,CAAC;QACvCC,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;QAE/BrG,KAAK,CAACmF,OAAO,CAAC,gDAAgD,CAAC;MACjE,CAAC,MAAM;QACL,MAAM,IAAI6B,KAAK,CAAChC,QAAQ,CAACE,IAAI,CAAC+B,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAA,IAAA8B,eAAA,EAAAC,oBAAA;MACd9B,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvC,MAAMgC,YAAY,GAAG,EAAAF,eAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhC,IAAI,cAAAiC,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,uDAAuD;;MAE7G;MACA,IAAIG,YAAY,CAACC,QAAQ,CAAC,wBAAwB,CAAC,EAAE;QACnDrH,KAAK,CAACoF,KAAK,CAAC,iEAAiE,CAAC;QAC9E;QACAL,mBAAmB,CAAC,CAAC;MACvB,CAAC,MAAM;QACL/E,KAAK,CAACoF,KAAK,CAACgC,YAAY,CAAC;MAC3B;IACF,CAAC,SAAS;MACR/C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAI,CAACC,WAAW,EAAE;IAChB,oBACE3D,OAAA,CAACG,iBAAiB;MAAAwG,QAAA,eAChB3G,OAAA,CAACM,eAAe;QAAAqG,QAAA,eACd3G,OAAA;UAAK+F,KAAK,EAAE;YAAEa,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACrD3G,OAAA;YAAK8G,SAAS,EAAC,iBAAiB;YAACf,KAAK,EAAE;cAAEgB,MAAM,EAAE;YAAc;UAAE;YAAAtB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzElH,OAAA;YAAA2G,QAAA,EAAG;UAA8B;YAAAlB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAExB;EAEA,oBACElH,OAAA,CAACG,iBAAiB;IAAAwG,QAAA,eAChB3G,OAAA,CAACM,eAAe;MAAAqG,QAAA,gBACd3G,OAAA,CAACQ,aAAa;QAAAmG,QAAA,gBACZ3G,OAAA,CAACU,WAAW;UAAAiG,QAAA,eACV3G,OAAA,CAACH,WAAW;YAACsH,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACdlH,OAAA,CAACY,YAAY;UAAA+F,QAAA,EAAC;QAAmB;UAAAlB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAChDlH,OAAA,CAACe,cAAc;UAAA4F,QAAA,EAAC;QAEhB;UAAAlB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEhBlH,OAAA,CAACkB,WAAW;QAAAyF,QAAA,gBACV3G,OAAA,CAACoB,YAAY;UAAAuF,QAAA,gBACX3G,OAAA,CAACL,GAAG;YAACwH,IAAI,EAAE;UAAG;YAAA1B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEnB;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAEflH,OAAA,CAACuB,cAAc;UAAAoF,QAAA,gBACb3G,OAAA,CAACyB,aAAa;YAAAkF,QAAA,gBACZ3G,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAI;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjClH,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,GAAAnE,iBAAA,GAAEmB,WAAW,CAACO,IAAI,cAAA1B,iBAAA,uBAAhBA,iBAAA,CAAkBsC;YAAK;cAAAW,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAChBlH,OAAA,CAACyB,aAAa;YAAAkF,QAAA,gBACZ3G,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAW;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxClH,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,IAAAlE,kBAAA,GAAEkB,WAAW,CAACO,IAAI,cAAAzB,kBAAA,uBAAhBA,kBAAA,CAAkBuC,WAAW,EAAC,QAAM;YAAA;cAAAS,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eAChBlH,OAAA,CAACyB,aAAa;YAAAkF,QAAA,gBACZ3G,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAM;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnClH,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAM;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAChBlH,OAAA,CAACyB,aAAa;YAAAkF,QAAA,gBACZ3G,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAI;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjClH,OAAA;cAAK8G,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAErD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8D;YAAI;cAAA3B,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEjBlH,OAAA;UAAA2G,QAAA,gBACE3G,OAAA;YAAA2G,QAAA,EAAQ;UAAiB;YAAAlB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClClH,OAAA,CAAC2B,UAAU;YAAAgF,QAAA,GACRhD,WAAW,CAACM,UAAU,eACvBjE,OAAA;cAAQ8G,SAAS,EAAC,UAAU;cAACO,OAAO,EAAEnC,cAAe;cAAAyB,QAAA,EAAC;YAEtD;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdlH,OAAA,CAAC6B,eAAe;QAAA8E,QAAA,gBACd3G,OAAA;UAAI+F,KAAK,EAAE;YAAEuB,YAAY,EAAE,MAAM;YAAEtB,OAAO,EAAE,MAAM;YAAEuB,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBACzE3G,OAAA,CAACP,YAAY;YAAC0H,IAAI,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEyB,WAAW,EAAE;YAAO;UAAE;YAAA/B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAE5D;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAGJrD,cAAc,IAAIA,cAAc,CAACc,QAAQ,IAAId,cAAc,CAACc,QAAQ,CAACC,MAAM,GAAG,CAAC,iBAC9E5E,OAAA;UAAK+F,KAAK,EAAE;YACV0B,UAAU,EAAE5D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC+C,aAAa,IAAI7D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACgD,YAAY,GAC3F,wBAAwB,GACxB,wBAAwB;YAC5BC,MAAM,EAAE,aAAa/D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC+C,aAAa,IAAI7D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACgD,YAAY,GACpG,wBAAwB,GACxB,wBAAwB,EAAE;YAC9BE,YAAY,EAAE,MAAM;YACpBhB,OAAO,EAAE,MAAM;YACfS,YAAY,EAAE,MAAM;YACpBV,SAAS,EAAE;UACb,CAAE;UAAAD,QAAA,gBACA3G,OAAA;YAAK+F,KAAK,EAAE;cACV+B,KAAK,EAAEjE,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC+C,aAAa,IAAI7D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACgD,YAAY,GACtF,SAAS,GACT,SAAS;cACbI,UAAU,EAAE,KAAK;cACjBT,YAAY,EAAE;YAChB,CAAE;YAAAX,QAAA,GAAC,aACU,EAAC9C,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC+C,aAAa,IAAI,CAAC,EAAC,KAAG,EAAC7D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACgD,YAAY,IAAI,CAAC;UAAA;YAAAlC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvG,CAAC,eACNlH,OAAA;YAAK+F,KAAK,EAAE;cAAEiC,QAAQ,EAAE,QAAQ;cAAEC,OAAO,EAAE;YAAI,CAAE;YAAAtB,QAAA,EAC9C9C,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC+C,aAAa,IAAI7D,cAAc,CAACc,QAAQ,CAAC,CAAC,CAAC,CAACgD,YAAY,GAChF,2CAA2C,GAC3C;UAAsB;YAAAlC,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEvB,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDlH,OAAA,CAAC+B,cAAc;UACbsF,OAAO,EAAE/B,gBAAiB;UAC1B4C,QAAQ,EAAEzE,WAAW,IAAK,CAAAI,cAAc,aAAdA,cAAc,wBAAAnB,qBAAA,GAAdmB,cAAc,CAAEc,QAAQ,cAAAjC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+B+E,aAAa,MAAI7D,cAAc,aAAdA,cAAc,wBAAAjB,sBAAA,GAAdiB,cAAc,CAAEc,QAAQ,cAAA/B,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+B8E,YAAY,CAAE;UACvH5B,KAAK,EAAE;YACLkC,OAAO,EAAG,CAAApE,cAAc,aAAdA,cAAc,wBAAAf,sBAAA,GAAde,cAAc,CAAEc,QAAQ,cAAA7B,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+B2E,aAAa,MAAI7D,cAAc,aAAdA,cAAc,wBAAAb,sBAAA,GAAda,cAAc,CAAEc,QAAQ,cAAA3B,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+B0E,YAAY,IAAI,GAAG,GAAG,CAAC;YAChHQ,MAAM,EAAG,CAAAtE,cAAc,aAAdA,cAAc,wBAAAX,sBAAA,GAAdW,cAAc,CAAEc,QAAQ,cAAAzB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA2B,CAAC,CAAC,cAAAC,sBAAA,uBAA7BA,sBAAA,CAA+BuE,aAAa,MAAI7D,cAAc,aAAdA,cAAc,wBAAAT,sBAAA,GAAdS,cAAc,CAAEc,QAAQ,cAAAvB,sBAAA,wBAAAC,uBAAA,GAAxBD,sBAAA,CAA2B,CAAC,CAAC,cAAAC,uBAAA,uBAA7BA,uBAAA,CAA+BsE,YAAY,IAAI,aAAa,GAAG;UAC1H,CAAE;UAAAhB,QAAA,EAEDlD,WAAW,gBACVzD,OAAA,CAAAE,SAAA;YAAAyG,QAAA,gBACE3G,OAAA;cAAK8G,SAAS,EAAC,iBAAiB;cAACf,KAAK,EAAE;gBAAEqC,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE,MAAM;gBAAEb,WAAW,EAAE;cAAO;YAAE;cAAA/B,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAExG;UAAA,eAAE,CAAC,gBAEHlH,OAAA,CAAAE,SAAA;YAAAyG,QAAA,gBACE3G,OAAA,CAACP,YAAY;cAAC0H,IAAI,EAAE;YAAG;cAAA1B,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sCAE5B;UAAA,eAAE;QACH;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,eAEjBlH,OAAA;UAAK+F,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEuB,UAAU,EAAE,QAAQ;YAAEe,cAAc,EAAE,QAAQ;YAAEL,OAAO,EAAE;UAAI,CAAE;UAAAtB,QAAA,gBAC5F3G,OAAA,CAACN,MAAM;YAACyH,IAAI,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEyB,WAAW,EAAE;YAAM;UAAE;YAAA/B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDlH,OAAA;YAAA2G,QAAA,EAAO;UAA6C;YAAAlB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAElBlH,OAAA,CAACkC,mBAAmB;QAAAyE,QAAA,gBAClB3G,OAAA;UAAI+F,KAAK,EAAE;YAAEuB,YAAY,EAAE,MAAM;YAAEtB,OAAO,EAAE,MAAM;YAAEuB,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBACzE3G,OAAA,CAACJ,OAAO;YAACuH,IAAI,EAAE,EAAG;YAACpB,KAAK,EAAE;cAAEyB,WAAW,EAAE;YAAO;UAAE;YAAA/B,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEvD;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELlH,OAAA,CAACoC,gBAAgB;UAAAuE,QAAA,gBACf3G,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAQ;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wCAAoC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtElH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAG;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAAuD;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpFlH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAK;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uDAAmD;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFlH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAQ;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kDAA8C;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFlH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAM;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kDAA8C;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9ElH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAU;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+BAA2B;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DlH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAU;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wBAAoB;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDlH,OAAA;YAAA2G,QAAA,gBAAI3G,OAAA;cAAA2G,QAAA,EAAQ;YAAc;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mCAA+B;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEnBlH,OAAA;UAAK+F,KAAK,EAAE;YACVwC,SAAS,EAAE,MAAM;YACjB1B,OAAO,EAAE,MAAM;YACfY,UAAU,EAAE,wBAAwB;YACpCI,YAAY,EAAE,MAAM;YACpBD,MAAM,EAAE;UACV,CAAE;UAAAjB,QAAA,gBACA3G,OAAA;YAAK+F,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEuB,UAAU,EAAE,QAAQ;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,gBAC1E3G,OAAA,CAACF,WAAW;cAACqH,IAAI,EAAE,EAAG;cAACpB,KAAK,EAAE;gBAAEyB,WAAW,EAAE,MAAM;gBAAEM,KAAK,EAAE;cAAU;YAAE;cAAArC,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3ElH,OAAA;cAAQ+F,KAAK,EAAE;gBAAE+B,KAAK,EAAE;cAAU,CAAE;cAAAnB,QAAA,EAAC;YAAgB;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNlH,OAAA;YAAI+F,KAAK,EAAE;cAAEyC,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAA9B,QAAA,gBACnD3G,OAAA;cAAA2G,QAAA,EAAI;YAAgE;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzElH,OAAA;cAAA2G,QAAA,EAAI;YAAuD;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChElH,OAAA;cAAA2G,QAAA,EAAI;YAA4D;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrElH,OAAA;cAAA2G,QAAA,EAAI;YAAwD;cAAAlB,QAAA,EAAAuB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAzB,QAAA,EAAAuB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAzB,QAAA,EAAAuB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAzB,QAAA,EAAAuB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC;IAAA;MAAAzB,QAAA,EAAAuB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAzB,QAAA,EAAAuB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAExB,CAAC;AAAC3E,EAAA,CA/QI/C,QAAQ;EAAA,QACKD,OAAO,EACPL,WAAW,EACXC,WAAW;AAAA;AAAAuJ,IAAA,GAHxBlJ,QAAQ;AAiRd,eAAeA,QAAQ;AAAC,IAAAa,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAoG,IAAA;AAAAC,YAAA,CAAAtI,EAAA;AAAAsI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAlI,GAAA;AAAAkI,YAAA,CAAAhI,GAAA;AAAAgI,YAAA,CAAA7H,GAAA;AAAA6H,YAAA,CAAA1H,GAAA;AAAA0H,YAAA,CAAAxH,GAAA;AAAAwH,YAAA,CAAArH,GAAA;AAAAqH,YAAA,CAAAnH,GAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,IAAA;AAAA6G,YAAA,CAAA1G,IAAA;AAAA0G,YAAA,CAAAxG,IAAA;AAAAwG,YAAA,CAAArG,IAAA;AAAAqG,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}