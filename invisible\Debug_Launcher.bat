@echo off
title Invisible Assessment Tool - Debug Mode
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🔍 INVISIBLE ASSESSMENT TOOL - DEBUG MODE 🔍            ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🎯 Starting in debug mode to check for errors...
echo 💡 This will show console output to help diagnose issues
echo.

REM Try different installation paths
set "INSTALL_PATH1=C:\Program Files\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"
set "INSTALL_PATH2=C:\Program Files (x86)\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"
set "INSTALL_PATH3=%USERPROFILE%\AppData\Local\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"

echo 🔍 Checking installation paths...
echo.

if exist "%INSTALL_PATH1%" (
    echo ✅ Found installation at: %INSTALL_PATH1%
    echo 🚀 Starting with console output...
    echo.
    "%INSTALL_PATH1%"
    goto END
)

if exist "%INSTALL_PATH2%" (
    echo ✅ Found installation at: %INSTALL_PATH2%
    echo 🚀 Starting with console output...
    echo.
    "%INSTALL_PATH2%"
    goto END
)

if exist "%INSTALL_PATH3%" (
    echo ✅ Found installation at: %INSTALL_PATH3%
    echo 🚀 Starting with console output...
    echo.
    "%INSTALL_PATH3%"
    goto END
)

echo ❌ Error: Invisible Assessment Tool not found in any standard location!
echo.
echo 📁 Checked locations:
echo    - %INSTALL_PATH1%
echo    - %INSTALL_PATH2%
echo    - %INSTALL_PATH3%
echo.
echo 💡 Please reinstall the software or check installation path.
echo.

:END
echo.
echo 📋 After the software starts:
echo    - Wait 5 seconds
echo    - Press Ctrl+B to show interface
echo    - If nothing happens, check the console output above for errors
echo.
pause
