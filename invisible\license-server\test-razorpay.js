const Razorpay = require('razorpay');
require('dotenv').config();

console.log('🔧 TESTING RAZORPAY CONFIGURATION');
console.log('================================');

// Check environment variables
console.log('\n📋 Environment Variables:');
console.log('RAZORPAY_KEY_ID:', process.env.RAZORPAY_KEY_ID ? 'SET' : 'NOT SET');
console.log('RAZORPAY_KEY_SECRET:', process.env.RAZORPAY_KEY_SECRET ? 'SET' : 'NOT SET');

if (process.env.RAZORPAY_KEY_ID) {
    console.log('Key ID:', process.env.RAZORPAY_KEY_ID);
}

// Test Razorpay initialization
try {
    const razorpay = new Razorpay({
        key_id: process.env.RAZORPAY_KEY_ID,
        key_secret: process.env.RAZORPAY_KEY_SECRET
    });
    
    console.log('\n✅ Razorpay initialized successfully!');
    
    // Test creating an order
    const orderOptions = {
        amount: 1000, // ₹10 in paise
        currency: 'INR',
        receipt: `test_receipt_${Date.now()}`,
        notes: {
            test: 'true'
        }
    };
    
    console.log('\n🧪 Testing order creation...');
    razorpay.orders.create(orderOptions)
        .then(order => {
            console.log('✅ Test order created successfully!');
            console.log('Order ID:', order.id);
            console.log('Amount:', order.amount / 100, 'INR');
            console.log('\n🎉 RAZORPAY IS WORKING CORRECTLY!');
        })
        .catch(error => {
            console.error('❌ Order creation failed:', error.message);
            if (error.statusCode === 401) {
                console.log('\n🔑 SOLUTION: Check your Razorpay credentials');
                console.log('1. Verify RAZORPAY_KEY_ID is correct');
                console.log('2. Verify RAZORPAY_KEY_SECRET is correct');
                console.log('3. Make sure keys are from the same Razorpay account');
            }
        });
        
} catch (error) {
    console.error('❌ Razorpay initialization failed:', error.message);
    console.log('\n🔧 SOLUTION: Check your .env file configuration');
}
