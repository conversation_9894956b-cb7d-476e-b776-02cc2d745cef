{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Purchase.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PayPalButtons } from '@paypal/react-paypal-js';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CreditCard, Shield, Download, CheckCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PurchaseContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = PurchaseContainer;\nconst PurchaseContent = styled.div`\n  max-width: 1000px;\n  margin: 0 auto;\n`;\n_c2 = PurchaseContent;\nconst PurchaseHeader = styled.div`\n  text-align: center;\n  margin-bottom: 60px;\n`;\n_c3 = PurchaseHeader;\nconst PurchaseTitle = styled.h1`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n_c4 = PurchaseTitle;\nconst PurchaseSubtitle = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n_c5 = PurchaseSubtitle;\nconst PurchaseGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 40px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c6 = PurchaseGrid;\nconst PlanCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  &.recommended {\n    border: 2px solid #4CAF50;\n  }\n`;\n_c7 = PlanCard;\nconst RecommendedBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n_c8 = RecommendedBadge;\nconst PlanTitle = styled.h2`\n  font-size: 1.8rem;\n  margin-bottom: 10px;\n  color: #4CAF50;\n`;\n_c9 = PlanTitle;\nconst PlanPrice = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n_c0 = PlanPrice;\nconst PlanFeatures = styled.ul`\n  list-style: none;\n  margin-bottom: 30px;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    display: flex;\n    align-items: center;\n    \n    svg {\n      margin-right: 10px;\n      color: #4CAF50;\n    }\n  }\n`;\n_c1 = PlanFeatures;\nconst PaymentSection = styled.div`\n  margin-top: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n`;\n_c10 = PaymentSection;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40px;\n  \n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n    border-top: 4px solid #4CAF50;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n_c11 = LoadingSpinner;\nconst Purchase = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [userLicenses, setUserLicenses] = useState([]);\n  useEffect(() => {\n    fetchUserLicenses();\n  }, []);\n  const fetchUserLicenses = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success) {\n        setUserLicenses(response.data.data.licenses);\n      }\n    } catch (error) {\n      console.error('Error fetching licenses:', error);\n    }\n  };\n  const hasActiveLicense = userLicenses.some(license => license.isValid);\n  const isFirstPurchase = (user === null || user === void 0 ? void 0 : user.totalPurchases) === 0;\n  const plans = [{\n    id: 'initial',\n    title: 'Initial Purchase',\n    price: '₹5,000',\n    amount: 5000,\n    screenshots: 50,\n    description: 'Perfect for getting started',\n    features: ['50 Screenshots per month', 'AI-powered problem analysis', 'Complete stealth integration', 'Hardware binding security', 'Global keyboard shortcuts', 'All programming languages', 'Lifetime software access', 'Priority support'],\n    recommended: isFirstPurchase,\n    available: isFirstPurchase\n  }, {\n    id: 'renewal',\n    title: 'Monthly Renewal',\n    price: '₹250',\n    amount: 250,\n    screenshots: 45,\n    description: 'Continue your subscription',\n    features: ['45 Screenshots per month', 'AI-powered problem analysis', 'Complete stealth integration', 'Hardware binding security', 'Global keyboard shortcuts', 'All programming languages', 'Continued access', 'Regular updates'],\n    recommended: !isFirstPurchase,\n    available: !isFirstPurchase || hasActiveLicense\n  }];\n  const createTestPurchase = async planId => {\n    try {\n      setLoading(true);\n      const plan = plans.find(p => p.id === planId);\n      const response = await axios.post('/payments/test-purchase', {\n        tier: planId === 'initial' ? 1 : 2\n      });\n      if (response.data.success) {\n        toast.success('Purchase successful! License created.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to complete purchase');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const onPayPalApprove = async (data, actions, planId) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/payments/execute', {\n        paymentId: data.orderID,\n        payerId: data.payerID\n      });\n      if (response.data.success) {\n        toast.success('Payment successful! Your license has been activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plans.find(p => p.id === planId)\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Payment processing failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const onPayPalError = error => {\n    console.error('PayPal error:', error);\n    toast.error('Payment failed. Please try again.');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(PurchaseContainer, {\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '20px'\n          },\n          children: \"Processing payment...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(PurchaseContainer, {\n    children: /*#__PURE__*/_jsxDEV(PurchaseContent, {\n      children: [/*#__PURE__*/_jsxDEV(PurchaseHeader, {\n        children: [/*#__PURE__*/_jsxDEV(PurchaseTitle, {\n          children: \"\\uD83D\\uDCB3 Choose Your Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PurchaseSubtitle, {\n          children: \"Secure your access to the Invisible Assessment Tool\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PurchaseGrid, {\n        children: plans.map(plan => /*#__PURE__*/_jsxDEV(PlanCard, {\n          className: plan.recommended ? 'recommended' : '',\n          children: [plan.recommended && /*#__PURE__*/_jsxDEV(RecommendedBadge, {\n            children: \"RECOMMENDED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 36\n          }, this), /*#__PURE__*/_jsxDEV(PlanTitle, {\n            children: plan.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PlanPrice, {\n            children: plan.price\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '20px'\n            },\n            children: plan.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PlanFeatures, {\n            children: plan.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 297,\n                columnNumber: 21\n              }, this), feature]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 15\n          }, this), plan.available ? /*#__PURE__*/_jsxDEV(PaymentSection, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                marginBottom: '15px',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                size: 20,\n                style: {\n                  marginRight: '10px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this), \"Secure Payment with PayPal\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                width: '100%',\n                background: 'linear-gradient(45deg, #0070ba, #003087)',\n                color: 'white',\n                border: 'none',\n                padding: '15px',\n                borderRadius: '10px',\n                fontSize: '1.1rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onClick: () => createTestPurchase(plan.id),\n              disabled: loading,\n              children: loading ? 'Processing...' : `Pay ${plan.price} with PayPal`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '20px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '10px',\n              textAlign: 'center',\n              opacity: 0.7\n            },\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 24,\n              style: {\n                marginBottom: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Not available for your account type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 17\n          }, this)]\n        }, plan.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 274,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 273,\n    columnNumber: 5\n  }, this);\n};\n_s(Purchase, \"d/KFbHmNnLvZRXuS1L4OKdgJn+I=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c12 = Purchase;\nexport default Purchase;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"PurchaseContainer\");\n$RefreshReg$(_c2, \"PurchaseContent\");\n$RefreshReg$(_c3, \"PurchaseHeader\");\n$RefreshReg$(_c4, \"PurchaseTitle\");\n$RefreshReg$(_c5, \"PurchaseSubtitle\");\n$RefreshReg$(_c6, \"PurchaseGrid\");\n$RefreshReg$(_c7, \"PlanCard\");\n$RefreshReg$(_c8, \"RecommendedBadge\");\n$RefreshReg$(_c9, \"PlanTitle\");\n$RefreshReg$(_c0, \"PlanPrice\");\n$RefreshReg$(_c1, \"PlanFeatures\");\n$RefreshReg$(_c10, \"PaymentSection\");\n$RefreshReg$(_c11, \"LoadingSpinner\");\n$RefreshReg$(_c12, \"Purchase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "PayPalButtons", "styled", "toast", "axios", "useAuth", "CreditCard", "Shield", "Download", "CheckCircle", "jsxDEV", "_jsxDEV", "Pur<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c3", "PurchaseTitle", "h1", "_c4", "PurchaseSubtitle", "p", "_c5", "PurchaseGrid", "_c6", "PlanCard", "_c7", "RecommendedBadge", "_c8", "PlanTitle", "h2", "_c9", "PlanPrice", "_c0", "PlanFeatures", "ul", "_c1", "PaymentSection", "_c10", "LoadingSpinner", "_c11", "Purchase", "_s", "user", "navigate", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "userLicenses", "setUserLicenses", "fetchUserLicenses", "response", "get", "data", "success", "licenses", "error", "console", "hasActiveLicense", "some", "license", "<PERSON><PERSON><PERSON><PERSON>", "isFirstPurchase", "totalPurchases", "plans", "id", "title", "price", "amount", "screenshots", "description", "features", "recommended", "available", "createTestPurchase", "planId", "plan", "find", "post", "tier", "state", "licenseKey", "Error", "message", "_error$response", "_error$response$data", "onPayPalApprove", "actions", "paymentId", "orderID", "payerId", "payerID", "_error$response2", "_error$response2$data", "onPayPalError", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "map", "opacity", "marginBottom", "feature", "index", "size", "display", "alignItems", "marginRight", "width", "background", "color", "border", "padding", "borderRadius", "fontSize", "fontWeight", "cursor", "transition", "onClick", "disabled", "textAlign", "_c12", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Purchase.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { PayPalButtons } from '@paypal/react-paypal-js';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CreditCard, Shield, Download, CheckCircle } from 'lucide-react';\n\nconst PurchaseContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst PurchaseContent = styled.div`\n  max-width: 1000px;\n  margin: 0 auto;\n`;\n\nconst PurchaseHeader = styled.div`\n  text-align: center;\n  margin-bottom: 60px;\n`;\n\nconst PurchaseTitle = styled.h1`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n\nconst PurchaseSubtitle = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\nconst PurchaseGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 40px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst PlanCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  &.recommended {\n    border: 2px solid #4CAF50;\n  }\n`;\n\nconst RecommendedBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n\nconst PlanTitle = styled.h2`\n  font-size: 1.8rem;\n  margin-bottom: 10px;\n  color: #4CAF50;\n`;\n\nconst PlanPrice = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n\nconst PlanFeatures = styled.ul`\n  list-style: none;\n  margin-bottom: 30px;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    display: flex;\n    align-items: center;\n    \n    svg {\n      margin-right: 10px;\n      color: #4CAF50;\n    }\n  }\n`;\n\nconst PaymentSection = styled.div`\n  margin-top: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40px;\n  \n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n    border-top: 4px solid #4CAF50;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst Purchase = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [userLicenses, setUserLicenses] = useState([]);\n\n  useEffect(() => {\n    fetchUserLicenses();\n  }, []);\n\n  const fetchUserLicenses = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success) {\n        setUserLicenses(response.data.data.licenses);\n      }\n    } catch (error) {\n      console.error('Error fetching licenses:', error);\n    }\n  };\n\n  const hasActiveLicense = userLicenses.some(license => license.isValid);\n  const isFirstPurchase = user?.totalPurchases === 0;\n\n  const plans = [\n    {\n      id: 'initial',\n      title: 'Initial Purchase',\n      price: '₹5,000',\n      amount: 5000,\n      screenshots: 50,\n      description: 'Perfect for getting started',\n      features: [\n        '50 Screenshots per month',\n        'AI-powered problem analysis',\n        'Complete stealth integration',\n        'Hardware binding security',\n        'Global keyboard shortcuts',\n        'All programming languages',\n        'Lifetime software access',\n        'Priority support'\n      ],\n      recommended: isFirstPurchase,\n      available: isFirstPurchase\n    },\n    {\n      id: 'renewal',\n      title: 'Monthly Renewal',\n      price: '₹250',\n      amount: 250,\n      screenshots: 45,\n      description: 'Continue your subscription',\n      features: [\n        '45 Screenshots per month',\n        'AI-powered problem analysis',\n        'Complete stealth integration',\n        'Hardware binding security',\n        'Global keyboard shortcuts',\n        'All programming languages',\n        'Continued access',\n        'Regular updates'\n      ],\n      recommended: !isFirstPurchase,\n      available: !isFirstPurchase || hasActiveLicense\n    }\n  ];\n\n  const createTestPurchase = async (planId) => {\n    try {\n      setLoading(true);\n      const plan = plans.find(p => p.id === planId);\n\n      const response = await axios.post('/payments/test-purchase', {\n        tier: planId === 'initial' ? 1 : 2\n      });\n\n      if (response.data.success) {\n        toast.success('Purchase successful! License created.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Failed to complete purchase');\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const onPayPalApprove = async (data, actions, planId) => {\n    try {\n      setLoading(true);\n\n      const response = await axios.post('/payments/execute', {\n        paymentId: data.orderID,\n        payerId: data.payerID\n      });\n\n      if (response.data.success) {\n        toast.success('Payment successful! Your license has been activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plans.find(p => p.id === planId)\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Payment processing failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const onPayPalError = (error) => {\n    console.error('PayPal error:', error);\n    toast.error('Payment failed. Please try again.');\n  };\n\n  if (loading) {\n    return (\n      <PurchaseContainer>\n        <LoadingSpinner>\n          <div className=\"spinner\"></div>\n          <span style={{ marginLeft: '20px' }}>Processing payment...</span>\n        </LoadingSpinner>\n      </PurchaseContainer>\n    );\n  }\n\n  return (\n    <PurchaseContainer>\n      <PurchaseContent>\n        <PurchaseHeader>\n          <PurchaseTitle>💳 Choose Your Plan</PurchaseTitle>\n          <PurchaseSubtitle>\n            Secure your access to the Invisible Assessment Tool\n          </PurchaseSubtitle>\n        </PurchaseHeader>\n\n        <PurchaseGrid>\n          {plans.map((plan) => (\n            <PlanCard\n              key={plan.id}\n              className={plan.recommended ? 'recommended' : ''}\n            >\n              {plan.recommended && <RecommendedBadge>RECOMMENDED</RecommendedBadge>}\n\n              <PlanTitle>{plan.title}</PlanTitle>\n              <PlanPrice>{plan.price}</PlanPrice>\n              <p style={{ opacity: 0.8, marginBottom: '20px' }}>{plan.description}</p>\n\n              <PlanFeatures>\n                {plan.features.map((feature, index) => (\n                  <li key={index}>\n                    <CheckCircle size={16} />\n                    {feature}\n                  </li>\n                ))}\n              </PlanFeatures>\n\n              {plan.available ? (\n                <PaymentSection>\n                  <h4 style={{ marginBottom: '15px', display: 'flex', alignItems: 'center' }}>\n                    <CreditCard size={20} style={{ marginRight: '10px' }} />\n                    Secure Payment with PayPal\n                  </h4>\n\n                  <button\n                    style={{\n                      width: '100%',\n                      background: 'linear-gradient(45deg, #0070ba, #003087)',\n                      color: 'white',\n                      border: 'none',\n                      padding: '15px',\n                      borderRadius: '10px',\n                      fontSize: '1.1rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onClick={() => createTestPurchase(plan.id)}\n                    disabled={loading}\n                  >\n                    {loading ? 'Processing...' : `Pay ${plan.price} with PayPal`}\n                  </button>\n                </PaymentSection>\n              ) : (\n                <div style={{\n                  padding: '20px',\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  borderRadius: '10px',\n                  textAlign: 'center',\n                  opacity: 0.7\n                }}>\n                  <Shield size={24} style={{ marginBottom: '10px' }} />\n                  <p>Not available for your account type</p>\n                </div>\n              )}\n            </PlanCard>\n          ))}\n        </PurchaseGrid>\n      </PurchaseContent>\n    </PurchaseContainer>\n  );\n};\n\nexport default Purchase;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,aAAa,QAAQ,yBAAyB;AACvD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,MAAMC,iBAAiB,GAAGV,MAAM,CAACW,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,eAAe,GAAGb,MAAM,CAACW,GAAG;AAClC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,eAAe;AAKrB,MAAME,cAAc,GAAGf,MAAM,CAACW,GAAG;AACjC;AACA;AACA,CAAC;AAACK,GAAA,GAHID,cAAc;AAKpB,MAAME,aAAa,GAAGjB,MAAM,CAACkB,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,gBAAgB,GAAGpB,MAAM,CAACqB,CAAC;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,gBAAgB;AAOtB,MAAMG,YAAY,GAAGvB,MAAM,CAACW,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GARID,YAAY;AAUlB,MAAME,QAAQ,GAAGzB,MAAM,CAACW,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,QAAQ;AAad,MAAME,gBAAgB,GAAG3B,MAAM,CAACW,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAXID,gBAAgB;AAatB,MAAME,SAAS,GAAG7B,MAAM,CAAC8B,EAAE;AAC3B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,SAAS,GAAGhC,MAAM,CAACW,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAGlC,MAAM,CAACmC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,YAAY;AAiBlB,MAAMG,cAAc,GAAGrC,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAGvC,MAAM,CAACW,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAnBID,cAAc;AAqBpB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGxC,OAAO,CAAC,CAAC;EAC1B,MAAMyC,QAAQ,GAAG9C,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGlD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmD,OAAO,EAAEC,UAAU,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqD,YAAY,EAAEC,eAAe,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdsD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlD,KAAK,CAACmD,GAAG,CAAC,uBAAuB,CAAC;MACzD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBL,eAAe,CAACE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAME,gBAAgB,GAAGV,YAAY,CAACW,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,OAAO,CAAC;EACtE,MAAMC,eAAe,GAAG,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,cAAc,MAAK,CAAC;EAElD,MAAMC,KAAK,GAAG,CACZ;IACEC,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,QAAQ;IACfC,MAAM,EAAE,IAAI;IACZC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,6BAA6B;IAC1CC,QAAQ,EAAE,CACR,0BAA0B,EAC1B,6BAA6B,EAC7B,8BAA8B,EAC9B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,0BAA0B,EAC1B,kBAAkB,CACnB;IACDC,WAAW,EAAEV,eAAe;IAC5BW,SAAS,EAAEX;EACb,CAAC,EACD;IACEG,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,4BAA4B;IACzCC,QAAQ,EAAE,CACR,0BAA0B,EAC1B,6BAA6B,EAC7B,8BAA8B,EAC9B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,kBAAkB,EAClB,iBAAiB,CAClB;IACDC,WAAW,EAAE,CAACV,eAAe;IAC7BW,SAAS,EAAE,CAACX,eAAe,IAAIJ;EACjC,CAAC,CACF;EAED,MAAMgB,kBAAkB,GAAG,MAAOC,MAAM,IAAK;IAC3C,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM6B,IAAI,GAAGZ,KAAK,CAACa,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC6C,EAAE,KAAKU,MAAM,CAAC;MAE7C,MAAMxB,QAAQ,GAAG,MAAMlD,KAAK,CAAC6E,IAAI,CAAC,yBAAyB,EAAE;QAC3DC,IAAI,EAAEJ,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG;MACnC,CAAC,CAAC;MAEF,IAAIxB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBtD,KAAK,CAACsD,OAAO,CAAC,uCAAuC,CAAC;QACtDX,QAAQ,CAAC,WAAW,EAAE;UACpBqC,KAAK,EAAE;YACLC,UAAU,EAAE9B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC4B,UAAU;YACzCL,IAAI,EAAEA;UACR;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIM,KAAK,CAAC/B,QAAQ,CAACE,IAAI,CAAC8B,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MAAA,IAAA4B,eAAA,EAAAC,oBAAA;MACdrF,KAAK,CAACwD,KAAK,CAAC,EAAA4B,eAAA,GAAA5B,KAAK,CAACL,QAAQ,cAAAiC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB/B,IAAI,cAAAgC,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,6BAA6B,CAAC;MAC3E,MAAM3B,KAAK;IACb,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,eAAe,GAAG,MAAAA,CAAOjC,IAAI,EAAEkC,OAAO,EAAEZ,MAAM,KAAK;IACvD,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMI,QAAQ,GAAG,MAAMlD,KAAK,CAAC6E,IAAI,CAAC,mBAAmB,EAAE;QACrDU,SAAS,EAAEnC,IAAI,CAACoC,OAAO;QACvBC,OAAO,EAAErC,IAAI,CAACsC;MAChB,CAAC,CAAC;MAEF,IAAIxC,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBtD,KAAK,CAACsD,OAAO,CAAC,sDAAsD,CAAC;QACrEX,QAAQ,CAAC,WAAW,EAAE;UACpBqC,KAAK,EAAE;YACLC,UAAU,EAAE9B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC4B,UAAU;YACzCL,IAAI,EAAEZ,KAAK,CAACa,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC6C,EAAE,KAAKU,MAAM;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIO,KAAK,CAAC/B,QAAQ,CAACE,IAAI,CAAC8B,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MAAA,IAAAoC,gBAAA,EAAAC,qBAAA;MACd7F,KAAK,CAACwD,KAAK,CAAC,EAAAoC,gBAAA,GAAApC,KAAK,CAACL,QAAQ,cAAAyC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvC,IAAI,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBV,OAAO,KAAI,2BAA2B,CAAC;IAC3E,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+C,aAAa,GAAItC,KAAK,IAAK;IAC/BC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACrCxD,KAAK,CAACwD,KAAK,CAAC,mCAAmC,CAAC;EAClD,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACEtC,OAAA,CAACC,iBAAiB;MAAAsF,QAAA,eAChBvF,OAAA,CAAC8B,cAAc;QAAAyD,QAAA,gBACbvF,OAAA;UAAKwF,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/B5F,OAAA;UAAM6F,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAExB;EAEA,oBACE5F,OAAA,CAACC,iBAAiB;IAAAsF,QAAA,eAChBvF,OAAA,CAACI,eAAe;MAAAmF,QAAA,gBACdvF,OAAA,CAACM,cAAc;QAAAiF,QAAA,gBACbvF,OAAA,CAACQ,aAAa;UAAA+E,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAClD5F,OAAA,CAACW,gBAAgB;UAAA4E,QAAA,EAAC;QAElB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEjB5F,OAAA,CAACc,YAAY;QAAAyE,QAAA,EACV/B,KAAK,CAACuC,GAAG,CAAE3B,IAAI,iBACdpE,OAAA,CAACgB,QAAQ;UAEPwE,SAAS,EAAEpB,IAAI,CAACJ,WAAW,GAAG,aAAa,GAAG,EAAG;UAAAuB,QAAA,GAEhDnB,IAAI,CAACJ,WAAW,iBAAIhE,OAAA,CAACkB,gBAAgB;YAAAqE,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC,eAErE5F,OAAA,CAACoB,SAAS;YAAAmE,QAAA,EAAEnB,IAAI,CAACV;UAAK;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnC5F,OAAA,CAACuB,SAAS;YAAAgE,QAAA,EAAEnB,IAAI,CAACT;UAAK;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnC5F,OAAA;YAAG6F,KAAK,EAAE;cAAEG,OAAO,EAAE,GAAG;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAEnB,IAAI,CAACN;UAAW;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExE5F,OAAA,CAACyB,YAAY;YAAA8D,QAAA,EACVnB,IAAI,CAACL,QAAQ,CAACgC,GAAG,CAAC,CAACG,OAAO,EAAEC,KAAK,kBAChCnG,OAAA;cAAAuF,QAAA,gBACEvF,OAAA,CAACF,WAAW;gBAACsG,IAAI,EAAE;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACxBM,OAAO;YAAA,GAFDC,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEdxB,IAAI,CAACH,SAAS,gBACbjE,OAAA,CAAC4B,cAAc;YAAA2D,QAAA,gBACbvF,OAAA;cAAI6F,KAAK,EAAE;gBAAEI,YAAY,EAAE,MAAM;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAf,QAAA,gBACzEvF,OAAA,CAACL,UAAU;gBAACyG,IAAI,EAAE,EAAG;gBAACP,KAAK,EAAE;kBAAEU,WAAW,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,8BAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAEL5F,OAAA;cACE6F,KAAK,EAAE;gBACLW,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0CAA0C;gBACtDC,KAAK,EAAE,OAAO;gBACdC,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE,MAAM;gBACpBC,QAAQ,EAAE,QAAQ;gBAClBC,UAAU,EAAE,KAAK;gBACjBC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFC,OAAO,EAAEA,CAAA,KAAMhD,kBAAkB,CAACE,IAAI,CAACX,EAAE,CAAE;cAC3C0D,QAAQ,EAAE7E,OAAQ;cAAAiD,QAAA,EAEjBjD,OAAO,GAAG,eAAe,GAAG,OAAO8B,IAAI,CAACT,KAAK;YAAc;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,gBAEjB5F,OAAA;YAAK6F,KAAK,EAAE;cACVe,OAAO,EAAE,MAAM;cACfH,UAAU,EAAE,0BAA0B;cACtCI,YAAY,EAAE,MAAM;cACpBO,SAAS,EAAE,QAAQ;cACnBpB,OAAO,EAAE;YACX,CAAE;YAAAT,QAAA,gBACAvF,OAAA,CAACJ,MAAM;cAACwG,IAAI,EAAE,EAAG;cAACP,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAO;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrD5F,OAAA;cAAAuF,QAAA,EAAG;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA,GAvDIxB,IAAI,CAACX,EAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAwDJ,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAExB,CAAC;AAAC3D,EAAA,CAtNID,QAAQ;EAAA,QACKtC,OAAO,EACPL,WAAW;AAAA;AAAAgI,IAAA,GAFxBrF,QAAQ;AAwNd,eAAeA,QAAQ;AAAC,IAAA7B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAsF,IAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA5G,GAAA;AAAA4G,YAAA,CAAAzG,GAAA;AAAAyG,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA3F,GAAA;AAAA2F,YAAA,CAAAzF,IAAA;AAAAyF,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}