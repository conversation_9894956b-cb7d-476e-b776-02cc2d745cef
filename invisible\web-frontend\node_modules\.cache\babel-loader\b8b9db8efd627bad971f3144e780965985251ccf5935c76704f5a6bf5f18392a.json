{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlignRight = createLucideIcon(\"AlignRight\", [[\"line\", {\n  x1: \"21\",\n  x2: \"3\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"1fp77t\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"9\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1uyos4\"\n}], [\"line\", {\n  x1: \"21\",\n  x2: \"7\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1g9eri\"\n}]]);\nexport { AlignRight as default };", "map": {"version": 3, "names": ["AlignRight", "createLucideIcon", "x1", "x2", "y1", "y2", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\align-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlignRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMjEiIHgyPSIzIiB5MT0iNiIgeTI9IjYiIC8+CiAgPGxpbmUgeDE9IjIxIiB4Mj0iOSIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjIxIiB4Mj0iNyIgeTE9IjE4IiB5Mj0iMTgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/align-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlignRight = createLucideIcon('AlignRight', [\n  ['line', { x1: '21', x2: '3', y1: '6', y2: '6', key: '1fp77t' }],\n  ['line', { x1: '21', x2: '9', y1: '12', y2: '12', key: '1uyos4' }],\n  ['line', { x1: '21', x2: '7', y1: '18', y2: '18', key: '1g9eri' }],\n]);\n\nexport default AlignRight;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}