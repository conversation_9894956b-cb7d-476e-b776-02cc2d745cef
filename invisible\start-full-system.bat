@echo off
echo 🚀 INVISIBLE ASSESSMENT TOOL - FULL SYSTEM STARTUP
echo =====================================================
echo.

echo 📋 SYSTEM REQUIREMENTS CHECK:
echo ✅ Node.js (required)
echo ✅ MongoDB (required - make sure it's running)
echo ✅ Environment variables in invisible.env
echo.

echo 🔧 STEP 1: Installing Backend Dependencies...
cd license-server
if not exist node_modules (
    echo Installing backend dependencies...
    npm install
) else (
    echo Backend dependencies already installed ✅
)
echo.

echo 🔧 STEP 2: Installing Frontend Dependencies...
cd ..\web-frontend
if not exist node_modules (
    echo Installing frontend dependencies...
    npm install
) else (
    echo Frontend dependencies already installed ✅
)
echo.

echo 🧪 STEP 3: Testing Backend Configuration...
cd ..
node test-backend.js
echo.

echo 🚀 STEP 4: Starting Services...
echo.
echo Starting Backend Server (Port 5002)...
start "Backend Server" cmd /k "cd license-server && npm start"

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Server (Port 3000)...
start "Frontend Server" cmd /k "cd web-frontend && npm start"

echo.
echo 🎉 SYSTEM STARTUP COMPLETE!
echo.
echo 📱 FRONTEND: http://localhost:3000
echo 🔧 BACKEND:  http://localhost:5002
echo 📊 HEALTH:   http://localhost:5002/health
echo.
echo 💡 USAGE INSTRUCTIONS:
echo 1. Open http://localhost:3000 in your browser
echo 2. Register a new account
echo 3. Go to Purchase page
echo 4. Use "Create Test License" button for testing
echo 5. Download the software after payment
echo.
echo 🔧 TROUBLESHOOTING:
echo - If MongoDB connection fails, start MongoDB service
echo - If ports are busy, close other applications using ports 3000/5002
echo - Check invisible.env file for correct environment variables
echo.
pause
