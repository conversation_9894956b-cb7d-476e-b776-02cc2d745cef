import { ParseSettings } from '../parseSettings';
import { ASTAndProgram } from './shared';
/**
 * @param parseSettings Internal settings for parsing the file
 * @returns If found, returns the source file corresponding to the code and the containing program
 */
declare function createDefaultProgram(parseSettings: ParseSettings): ASTAndProgram | undefined;
export { createDefaultProgram };
//# sourceMappingURL=createDefaultProgram.d.ts.map
