{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Backpack = createLucideIcon(\"Backpack\", [[\"path\", {\n  d: \"M4 20V10a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v10a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2Z\",\n  key: \"tunmdx\"\n}], [\"path\", {\n  d: \"M9 6V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2\",\n  key: \"donm21\"\n}], [\"path\", {\n  d: \"M8 21v-5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v5\",\n  key: \"xk3gvk\"\n}], [\"path\", {\n  d: \"M8 10h8\",\n  key: \"c7uz4u\"\n}], [\"path\", {\n  d: \"M8 18h8\",\n  key: \"1no2b1\"\n}]]);\nexport { Backpack as default };", "map": {"version": 3, "names": ["Backpack", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\backpack.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Backpack\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMFYxMGE0IDQgMCAwIDEgNC00aDhhNCA0IDAgMCAxIDQgNHYxMGEyIDIgMCAwIDEtMiAySDZhMiAyIDAgMCAxLTItMloiIC8+CiAgPHBhdGggZD0iTTkgNlY0YTIgMiAwIDAgMSAyLTJoMmEyIDIgMCAwIDEgMiAydjIiIC8+CiAgPHBhdGggZD0iTTggMjF2LTVhMiAyIDAgMCAxIDItMmg0YTIgMiAwIDAgMSAyIDJ2NSIgLz4KICA8cGF0aCBkPSJNOCAxMGg4IiAvPgogIDxwYXRoIGQ9Ik04IDE4aDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/backpack\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Backpack = createLucideIcon('Backpack', [\n  [\n    'path',\n    {\n      d: 'M4 20V10a4 4 0 0 1 4-4h8a4 4 0 0 1 4 4v10a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2Z',\n      key: 'tunmdx',\n    },\n  ],\n  ['path', { d: 'M9 6V4a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v2', key: 'donm21' }],\n  ['path', { d: 'M8 21v-5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v5', key: 'xk3gvk' }],\n  ['path', { d: 'M8 10h8', key: 'c7uz4u' }],\n  ['path', { d: 'M8 18h8', key: '1no2b1' }],\n]);\n\nexport default Backpack;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,wCAA0C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAED,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}