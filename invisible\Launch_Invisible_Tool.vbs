Set WshShell = CreateObject("WScript.Shell")
Set fso = CreateObject("Scripting.FileSystemObject")

' Get the directory where this script is located
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
exePath = scriptDir & "\portable-app\InvisibleAssessmentTool.exe"

' Check if the executable exists
If fso.FileExists(exePath) Then
    ' Launch the application silently
    WshShell.Run """" & exePath & """", 0, False
    
    ' Show a brief notification
    WshShell.Popup "🚀 Invisible Assessment Tool launched!" & vbCrLf & vbCrLf & "Press Ctrl+B to show interface", 3, "Stealth Mode Active", 64
Else
    ' Show error if file not found
    WshShell.Popup "❌ Error: InvisibleAssessmentTool.exe not found!" & vbCrLf & vbCrLf & "Expected location: " & exePath, 0, "Launch Error", 16
End If
