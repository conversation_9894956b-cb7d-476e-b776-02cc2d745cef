{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Aperture = createLucideIcon(\"Aperture\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"line\", {\n  x1: \"14.31\",\n  x2: \"20.05\",\n  y1: \"8\",\n  y2: \"17.94\",\n  key: \"jdes2e\"\n}], [\"line\", {\n  x1: \"9.69\",\n  x2: \"21.17\",\n  y1: \"8\",\n  y2: \"8\",\n  key: \"1gubuk\"\n}], [\"line\", {\n  x1: \"7.38\",\n  x2: \"13.12\",\n  y1: \"12\",\n  y2: \"2.06\",\n  key: \"1m4d1n\"\n}], [\"line\", {\n  x1: \"9.69\",\n  x2: \"3.95\",\n  y1: \"16\",\n  y2: \"6.06\",\n  key: \"1wye2p\"\n}], [\"line\", {\n  x1: \"14.31\",\n  x2: \"2.83\",\n  y1: \"16\",\n  y2: \"16\",\n  key: \"1l9f4x\"\n}], [\"line\", {\n  x1: \"16.62\",\n  x2: \"10.88\",\n  y1: \"12\",\n  y2: \"21.94\",\n  key: \"1jjvfs\"\n}]]);\nexport { Aperture as default };", "map": {"version": 3, "names": ["Aperture", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\aperture.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Aperture\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8bGluZSB4MT0iMTQuMzEiIHgyPSIyMC4wNSIgeTE9IjgiIHkyPSIxNy45NCIgLz4KICA8bGluZSB4MT0iOS42OSIgeDI9IjIxLjE3IiB5MT0iOCIgeTI9IjgiIC8+CiAgPGxpbmUgeDE9IjcuMzgiIHgyPSIxMy4xMiIgeTE9IjEyIiB5Mj0iMi4wNiIgLz4KICA8bGluZSB4MT0iOS42OSIgeDI9IjMuOTUiIHkxPSIxNiIgeTI9IjYuMDYiIC8+CiAgPGxpbmUgeDE9IjE0LjMxIiB4Mj0iMi44MyIgeTE9IjE2IiB5Mj0iMTYiIC8+CiAgPGxpbmUgeDE9IjE2LjYyIiB4Mj0iMTAuODgiIHkxPSIxMiIgeTI9IjIxLjk0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/aperture\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Aperture = createLucideIcon('Aperture', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['line', { x1: '14.31', x2: '20.05', y1: '8', y2: '17.94', key: 'jdes2e' }],\n  ['line', { x1: '9.69', x2: '21.17', y1: '8', y2: '8', key: '1gubuk' }],\n  ['line', { x1: '7.38', x2: '13.12', y1: '12', y2: '2.06', key: '1m4d1n' }],\n  ['line', { x1: '9.69', x2: '3.95', y1: '16', y2: '6.06', key: '1wye2p' }],\n  ['line', { x1: '14.31', x2: '2.83', y1: '16', y2: '16', key: '1l9f4x' }],\n  ['line', { x1: '16.62', x2: '10.88', y1: '12', y2: '21.94', key: '1jjvfs' }],\n]);\n\nexport default Aperture;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,OAAS;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACrE,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,QAAQ;EAAEC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,QAAQ;EAAEC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,OAAS;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}