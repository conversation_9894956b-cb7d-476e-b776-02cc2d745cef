@echo off
title Invisible Assessment Tool - Launcher
color 0A

echo.
echo ████████████████████████████████████████████████████████████████
echo ██                                                            ██
echo ██    🚀 INVISIBLE ASSESSMENT TOOL - LAUNCHER 🚀              ██
echo ██                                                            ██
echo ████████████████████████████████████████████████████████████████
echo.
echo 🎯 Starting Invisible Assessment Tool...
echo 💡 After launch, press Ctrl+B to show interface
echo.

REM Try different installation paths
set "INSTALL_PATH1=C:\Program Files\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"
set "INSTALL_PATH2=C:\Program Files (x86)\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"
set "INSTALL_PATH3=%USERPROFILE%\AppData\Local\InvisibleAssessmentTool\InvisibleAssessmentTool.exe"

if exist "%INSTALL_PATH1%" (
    echo ✅ Found installation at: %INSTALL_PATH1%
    start "" "%INSTALL_PATH1%"
    goto SUCCESS
)

if exist "%INSTALL_PATH2%" (
    echo ✅ Found installation at: %INSTALL_PATH2%
    start "" "%INSTALL_PATH2%"
    goto SUCCESS
)

if exist "%INSTALL_PATH3%" (
    echo ✅ Found installation at: %INSTALL_PATH3%
    start "" "%INSTALL_PATH3%"
    goto SUCCESS
)

echo ❌ Error: Invisible Assessment Tool not found!
echo 📁 Please reinstall the software
pause
exit

:SUCCESS
echo.
echo 🎉 Invisible Assessment Tool launched successfully!
echo 💻 Running in background - Press Ctrl+B to access
echo 🔥 Press Ctrl+H to take screenshots
echo ⚡ Press Ctrl+Enter to analyze problems
echo.
echo 📋 Quick Reference:
echo    Ctrl+B = Show/Hide interface
echo    Ctrl+D = DSA mode
echo    Ctrl+C = Chatbot mode  
echo    Ctrl+N = Interview mode
echo    Ctrl+Q = Quit
echo.
timeout /t 5 /nobreak >nul
