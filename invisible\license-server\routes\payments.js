const express = require('express');
const Razorpay = require('razorpay');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const { verifyToken } = require('../middleware/auth');
const User = require('../models/User');
const License = require('../models/License');
const Payment = require('../models/Payment');

const router = express.Router();

// 💳 CONFIGURE RAZORPAY (CONDITIONAL)
let razorpay = null;
const razorpayKeyId = process.env.RAZORPAY_API_KEYID || process.env.RAZORPAY_KEY_ID;
const razorpayKeySecret = process.env.RAZORPAY_API_KEYSECRET || process.env.RAZORPAY_KEY_SECRET;

if (razorpayKeyId && razorpayKeyId !== 'rzp_test_your_key_id_here') {
    razorpay = new Razorpay({
        key_id: razorpayKeyId,
        key_secret: razorpayKeySecret
    });
    console.log('✅ Razorpay initialized with real credentials');
    console.log('🔑 Key ID:', razorpayKeyId);
} else {
    console.log('🧪 Razorpay running in test mode - add real credentials to .env');
    console.log('🔍 Available env vars:', {
        RAZORPAY_API_KEYID: process.env.RAZORPAY_API_KEYID ? 'SET' : 'NOT SET',
        RAZORPAY_KEY_ID: process.env.RAZORPAY_KEY_ID ? 'SET' : 'NOT SET'
    });
}

// 💰 PRICING CONFIGURATION (TESTING WITH ₹10)
const PRICING = {
    tier1: {
        amount: 10.00, // First purchase - ₹10 (TESTING)
        screenshots: 50,
        description: 'Invisible Assessment Tool - Initial Purchase (50 Screenshots)',
        currency: 'INR'
    },
    tier2: {
        amount: 5.00, // Monthly renewal - ₹5 (TESTING)
        screenshots: 45,
        description: 'Invisible Assessment Tool - Monthly Renewal (45 Screenshots)',
        currency: 'INR'
    }
};

// 🛒 CREATE RAZORPAY ORDER (WITH FALLBACK TO TEST MODE)
router.post('/create', [
    verifyToken,
    body('tier').isIn([1, 2]).withMessage('Invalid tier'),
], async (req, res) => {
    try {
        console.log('💳 Payment creation started');
        console.log('User ID:', req.userId);
        console.log('Request body:', req.body);

        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            console.log('❌ Validation errors:', errors.array());
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { tier } = req.body;
        const user = await User.findById(req.userId);

        if (!user) {
            console.log('❌ User not found:', req.userId);
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('✅ User found:', user.email);

        // 🔒 CHECK IF USER ALREADY HAS ACTIVE LICENSE
        const existingLicense = await License.findOne({
            userId: req.userId,
            isActive: true,
            expiryDate: { $gt: new Date() }
        });

        if (existingLicense) {
            console.log('❌ User already has active license:', existingLicense.licenseKey);
            return res.status(400).json({
                success: false,
                message: 'You already have an active license. Please wait for it to expire before purchasing again.',
                data: {
                    existingLicense: existingLicense.getStatus()
                }
            });
        }

        const pricing = tier === 1 ? PRICING.tier1 : PRICING.tier2;
        console.log('💰 Pricing:', pricing);

        // Check if Razorpay credentials are configured
        if (!razorpayKeyId || razorpayKeyId === 'rzp_test_your_key_id_here') {
            console.log('🧪 Using test mode');
            // Use test mode - create mock order
            const mockOrderId = `order_test_${Date.now()}`;
            console.log('📝 Creating mock order:', mockOrderId);

            // Save payment record
            const paymentRecord = new Payment({
                paymentId: mockOrderId,
                userId: user._id,
                paymentMethod: 'razorpay',
                amount: pricing.amount,
                currency: pricing.currency,
                tier: tier,
                screenshotsIncluded: pricing.screenshots,
                status: 'pending',
                paypalDetails: {}, // Keep for compatibility
                metadata: {
                    ipAddress: req.ip,
                    userAgent: req.get('User-Agent'),
                    razorpayOrderId: mockOrderId,
                    receipt: `receipt_${Date.now()}`,
                    testMode: true
                }
            });

            console.log('💾 Saving payment record...');
            await paymentRecord.save();
            console.log('✅ Payment record saved:', paymentRecord._id);

            console.log('📤 Sending response...');
            return res.json({
                success: true,
                message: 'Test order created successfully',
                data: {
                    orderId: mockOrderId,
                    amount: pricing.amount,
                    currency: pricing.currency,
                    tier: tier,
                    screenshots: pricing.screenshots,
                    razorpayKeyId: 'rzp_test_demo',
                    testMode: true,
                    userDetails: {
                        name: user.name,
                        email: user.email
                    }
                }
            });
        }

        // Real Razorpay integration
        const orderOptions = {
            amount: pricing.amount * 100, // Amount in paise (₹10 = 1000 paise)
            currency: pricing.currency,
            receipt: `receipt_${Date.now()}`,
            notes: {
                userId: user._id.toString(),
                tier: tier,
                screenshots: pricing.screenshots,
                description: pricing.description
            }
        };

        const razorpayOrder = await razorpay.orders.create(orderOptions);

        // Save payment record
        const paymentRecord = new Payment({
            paymentId: razorpayOrder.id,
            userId: user._id,
            paymentMethod: 'razorpay',
            amount: pricing.amount,
            currency: pricing.currency,
            tier: tier,
            screenshotsIncluded: pricing.screenshots,
            status: 'pending',
            paypalDetails: {}, // Keep for compatibility, but empty
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent'),
                razorpayOrderId: razorpayOrder.id,
                receipt: orderOptions.receipt
            }
        });

        await paymentRecord.save();

        res.json({
            success: true,
            message: 'Razorpay order created successfully',
            data: {
                orderId: razorpayOrder.id,
                amount: pricing.amount,
                currency: pricing.currency,
                tier: tier,
                screenshots: pricing.screenshots,
                razorpayKeyId: razorpayKeyId,
                userDetails: {
                    name: user.name,
                    email: user.email
                }
            }
        });

    } catch (error) {
        console.error('❌ Razorpay order creation error:', error);
        console.error('❌ Error stack:', error.stack);
        res.status(500).json({
            success: false,
            message: 'Payment creation failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// ✅ VERIFY RAZORPAY PAYMENT (WITH TEST MODE SUPPORT)
router.post('/verify', [
    verifyToken,
    body('razorpay_order_id').notEmpty().withMessage('Order ID is required'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { razorpay_order_id, razorpay_payment_id, razorpay_signature } = req.body;

        // Check if this is test mode
        if (razorpay_order_id.startsWith('order_test_')) {
            // Test mode - skip signature verification
            console.log('🧪 Test mode payment verification');
        } else {
            // Real Razorpay - verify signature
            if (!razorpay_payment_id || !razorpay_signature) {
                return res.status(400).json({
                    success: false,
                    message: 'Payment ID and signature are required for real payments'
                });
            }

            const body = razorpay_order_id + "|" + razorpay_payment_id;
            const expectedSignature = crypto
                .createHmac('sha256', razorpayKeySecret)
                .update(body.toString())
                .digest('hex');

            if (expectedSignature !== razorpay_signature) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid payment signature'
                });
            }
        }

        // Find payment record
        const paymentRecord = await Payment.findOne({
            paymentId: razorpay_order_id
        }).populate('userId');

        if (!paymentRecord) {
            return res.status(404).json({
                success: false,
                message: 'Payment record not found'
            });
        }

        // Verify user owns this payment
        if (paymentRecord.userId._id.toString() !== req.userId) {
            return res.status(403).json({
                success: false,
                message: 'Unauthorized access to payment'
            });
        }

        try {
            // Mark payment as completed
            await paymentRecord.markCompleted();
            paymentRecord.metadata.razorpayPaymentId = razorpay_payment_id;
            paymentRecord.metadata.razorpaySignature = razorpay_signature;
            await paymentRecord.save();

            // Create license
            const expiryDate = new Date();
            expiryDate.setDate(expiryDate.getDate() + 30); // 30 days from now

            const license = new License({
                userId: paymentRecord.userId._id,
                tier: paymentRecord.tier,
                expiryDate,
                screenshotsLimit: paymentRecord.screenshotsIncluded,
                paymentId: paymentRecord.paymentId,
                paymentMethod: 'razorpay',
                amountPaid: paymentRecord.amount,
                currency: paymentRecord.currency
            });

            await license.save();

            // Update payment record with license ID
            paymentRecord.licenseId = license._id;
            await paymentRecord.save();

            // Update user's purchase count
            paymentRecord.userId.totalPurchases += 1;
            await paymentRecord.userId.save();

            res.json({
                success: true,
                message: 'Payment verified and license created successfully',
                data: {
                    paymentId: razorpay_payment_id,
                    orderId: razorpay_order_id,
                    licenseKey: license.licenseKey,
                    license: license.getStatus(),
                    amount: paymentRecord.amount,
                    currency: paymentRecord.currency,
                    tier: paymentRecord.tier
                }
            });

        } catch (dbError) {
            console.error('❌ Database error after payment verification:', dbError);
            res.status(500).json({
                success: false,
                message: 'Payment verified but license creation failed. Please contact support.',
                paymentId: razorpay_payment_id
            });
        }

    } catch (error) {
        console.error('❌ Payment verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Payment verification failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 💰 GET PRICING INFO
router.get('/pricing', async (req, res) => {
    try {
        res.json({
            success: true,
            data: {
                pricing: {
                    tier1: {
                        ...PRICING.tier1,
                        title: 'Initial Purchase',
                        features: [
                            '50 Screenshots per month',
                            'AI-powered analysis',
                            'All programming languages',
                            'Global keyboard shortcuts',
                            'Stealth mode',
                            'Hardware binding security',
                            'Lifetime software access'
                        ]
                    },
                    tier2: {
                        ...PRICING.tier2,
                        title: 'Monthly Renewal',
                        features: [
                            '45 Screenshots per month',
                            'AI-powered analysis',
                            'All programming languages',
                            'Global keyboard shortcuts',
                            'Stealth mode',
                            'Hardware binding security',
                            'Continued access'
                        ]
                    }
                }
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch pricing'
        });
    }
});

// 🧪 TEST PAYMENT (BYPASS PAYPAL FOR DEVELOPMENT)
router.post('/test-purchase', [
    verifyToken,
    body('tier').isIn([1, 2]).withMessage('Invalid tier'),
], async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const { tier } = req.body;
        const user = await User.findById(req.userId);
        const pricing = tier === 1 ? PRICING.tier1 : PRICING.tier2;

        // Create mock payment record
        const paymentRecord = new Payment({
            paymentId: 'TEST-' + Date.now(),
            userId: user._id,
            paymentMethod: 'paypal',
            amount: pricing.amount,
            currency: pricing.currency,
            tier: tier,
            screenshotsIncluded: pricing.screenshots,
            status: 'completed', // Auto-complete for testing
            paypalDetails: {
                payerId: 'TEST-PAYER',
                paymentId: 'TEST-' + Date.now(),
                token: 'TEST-TOKEN'
            },
            metadata: {
                ipAddress: req.ip,
                userAgent: req.get('User-Agent')
            }
        });

        await paymentRecord.save();

        // Create license immediately
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30); // 30 days from now

        const license = new License({
            userId: user._id,
            tier: tier,
            expiryDate,
            screenshotsLimit: pricing.screenshots,
            paymentId: paymentRecord.paymentId,
            paymentMethod: 'paypal',
            amountPaid: pricing.amount,
            currency: pricing.currency
        });

        await license.save();

        // Update payment record with license ID
        paymentRecord.licenseId = license._id;
        await paymentRecord.save();

        // Update user's purchase count
        user.totalPurchases += 1;
        await user.save();

        res.json({
            success: true,
            message: 'Test purchase completed successfully',
            data: {
                paymentId: paymentRecord.paymentId,
                licenseKey: license.licenseKey,
                license: license.getStatus(),
                amount: pricing.amount,
                currency: pricing.currency,
                tier: tier
            }
        });

    } catch (error) {
        console.error('❌ Test purchase error:', error);
        res.status(500).json({
            success: false,
            message: 'Test purchase failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

// 🧪 TEST PAYPAL CONFIGURATION
router.get('/test-paypal', (req, res) => {
    try {
        // Test PayPal configuration
        const config = {
            mode: process.env.PAYPAL_MODE,
            client_id: process.env.PAYPAL_CLIENT_ID ? 'CONFIGURED' : 'MISSING',
            client_secret: process.env.PAYPAL_CLIENT_SECRET ? 'CONFIGURED' : 'MISSING'
        };

        res.json({
            success: true,
            message: 'PayPal configuration test',
            data: {
                paypalConfig: config,
                pricing: PRICING
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'PayPal test failed',
            error: error.message
        });
    }
});

// 🧪 TEST PAYMENT CREATION (DEBUG)
router.post('/test-create', [
    verifyToken,
    body('tier').isIn([1, 2]).withMessage('Invalid tier'),
], async (req, res) => {
    try {
        console.log('🧪 Test payment creation started');
        console.log('User ID:', req.userId);
        console.log('Request body:', req.body);

        const { tier } = req.body;
        const user = await User.findById(req.userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        console.log('User found:', user.email);

        const pricing = tier === 1 ? PRICING.tier1 : PRICING.tier2;
        console.log('Pricing:', pricing);

        // Create test license directly
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);

        const license = new License({
            userId: user._id,
            tier: tier,
            expiryDate,
            screenshotsLimit: pricing.screenshots,
            paymentId: `test_${Date.now()}`,
            paymentMethod: 'razorpay',
            amountPaid: pricing.amount,
            currency: pricing.currency
        });

        await license.save();
        console.log('License created:', license.licenseKey);

        res.json({
            success: true,
            message: 'Test license created successfully',
            data: {
                licenseKey: license.licenseKey,
                license: license.getStatus()
            }
        });

    } catch (error) {
        console.error('❌ Test payment creation error:', error);
        res.status(500).json({
            success: false,
            message: 'Test payment creation failed',
            error: error.message
        });
    }
});

// 🧪 DIRECT LICENSE CREATION (BYPASS FRONTEND)
router.get('/create-test-license/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const user = await User.findById(userId);

        if (!user) {
            return res.status(404).json({
                success: false,
                message: 'User not found'
            });
        }

        // 🔒 CHECK IF USER ALREADY HAS ACTIVE LICENSE
        const existingLicense = await License.findOne({
            userId: userId,
            isActive: true,
            expiryDate: { $gt: new Date() }
        });

        if (existingLicense) {
            return res.status(400).json({
                success: false,
                message: 'User already has an active license. Cannot create another one.',
                data: {
                    existingLicense: existingLicense.getStatus()
                }
            });
        }

        // Create test license directly
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);

        const license = new License({
            userId: user._id,
            tier: 1,
            expiryDate,
            screenshotsLimit: 50,
            paymentId: `direct_test_${Date.now()}`,
            paymentMethod: 'razorpay',
            amountPaid: 10,
            currency: 'INR'
        });

        await license.save();

        res.json({
            success: true,
            message: 'Test license created successfully!',
            data: {
                licenseKey: license.licenseKey,
                downloadUrl: `http://localhost:5002/api/download/create-link`,
                user: user.email
            }
        });

    } catch (error) {
        console.error('❌ Direct license creation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create test license',
            error: error.message
        });
    }
});

// Simple test route
router.get('/test', (req, res) => {
    res.json({
        success: true,
        message: 'Payments route working!'
    });
});

module.exports = router;
