{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Type = createLucideIcon(\"Type\", [[\"polyline\", {\n  points: \"4 7 4 4 20 4 20 7\",\n  key: \"1nosan\"\n}], [\"line\", {\n  x1: \"9\",\n  x2: \"15\",\n  y1: \"20\",\n  y2: \"20\",\n  key: \"swin9y\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"4\",\n  y2: \"20\",\n  key: \"1tx1rr\"\n}]]);\nexport { Type as default };", "map": {"version": 3, "names": ["Type", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\type.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Type\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWxpbmUgcG9pbnRzPSI0IDcgNCA0IDIwIDQgMjAgNyIgLz4KICA8bGluZSB4MT0iOSIgeDI9IjE1IiB5MT0iMjAiIHkyPSIyMCIgLz4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjQiIHkyPSIyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/type\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Type = createLucideIcon('Type', [\n  ['polyline', { points: '4 7 4 4 20 4 20 7', key: '1nosan' }],\n  ['line', { x1: '9', x2: '15', y1: '20', y2: '20', key: 'swin9y' }],\n  ['line', { x1: '12', x2: '12', y1: '4', y2: '20', key: '1tx1rr' }],\n]);\n\nexport default Type;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,UAAY;EAAEC,MAAA,EAAQ,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}