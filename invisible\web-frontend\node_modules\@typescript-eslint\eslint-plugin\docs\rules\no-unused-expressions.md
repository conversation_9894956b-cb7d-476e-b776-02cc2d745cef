---
description: 'Disallow unused expressions.'
---

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-unused-expressions** for documentation.

## Examples

This rule extends the base [`eslint/no-unused-expressions`](https://eslint.org/docs/rules/no-unused-expressions) rule.
It adds support for optional call expressions `x?.()`, and directive in module declarations.
