{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\contexts\\\\AuthContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext();\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Configure axios defaults\naxios.defaults.baseURL = API_BASE_URL;\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(null);\n\n  // Initialize auth state\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n  const initializeAuth = async () => {\n    try {\n      const savedToken = Cookies.get('auth_token');\n      if (savedToken) {\n        setToken(savedToken);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`;\n\n        // Verify token and get user data\n        const response = await axios.get('/auth/profile');\n        if (response.data.success) {\n          setUser(response.data.data.user);\n        } else {\n          // Token is invalid, remove it\n          logout();\n        }\n      }\n    } catch (error) {\n      console.error('Auth initialization error:', error);\n      logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n  const login = async (email, password) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/auth/login', {\n        email,\n        password\n      });\n      if (response.data.success) {\n        const {\n          user: userData,\n          token: userToken\n        } = response.data.data;\n\n        // Save token to cookie (expires in 30 days)\n        Cookies.set('auth_token', userToken, {\n          expires: 30\n        });\n        setToken(userToken);\n        setUser(userData);\n        toast.success('Login successful! Welcome back!');\n        return {\n          success: true\n        };\n      } else {\n        toast.error(response.data.message || 'Login failed');\n        return {\n          success: false,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const message = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed. Please try again.';\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const register = async (name, email, password) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/auth/register', {\n        name,\n        email,\n        password\n      });\n      if (response.data.success) {\n        const {\n          user: userData,\n          token: userToken\n        } = response.data.data;\n\n        // Save token to cookie\n        Cookies.set('auth_token', userToken, {\n          expires: 30\n        });\n        setToken(userToken);\n        setUser(userData);\n        toast.success('Registration successful! Welcome to Invisible Assessment Tool!');\n        return {\n          success: true\n        };\n      } else {\n        toast.error(response.data.message || 'Registration failed');\n        return {\n          success: false,\n          message: response.data.message\n        };\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      const message = ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Registration failed. Please try again.';\n      toast.error(message);\n      return {\n        success: false,\n        message\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    // Remove token from cookie\n    Cookies.remove('auth_token');\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Remove axios header\n    delete axios.defaults.headers.common['Authorization'];\n    toast.success('Logged out successfully');\n  };\n  const updateUser = userData => {\n    setUser(userData);\n  };\n  const refreshToken = async () => {\n    try {\n      const response = await axios.post('/auth/refresh');\n      if (response.data.success) {\n        const newToken = response.data.data.token;\n        Cookies.set('auth_token', newToken, {\n          expires: 30\n        });\n        setToken(newToken);\n        return true;\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      logout();\n      return false;\n    }\n  };\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    refreshToken,\n    isAuthenticated: !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"Gn6YPsYxft12DVBjBG8NzIcX2EU=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "Cookies", "toast", "jsxDEV", "_jsxDEV", "AuthContext", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "defaults", "baseURL", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "loading", "setLoading", "token", "setToken", "initializeAuth", "headers", "common", "savedToken", "get", "response", "data", "success", "logout", "error", "console", "login", "email", "password", "post", "userData", "userToken", "set", "expires", "message", "_error$response", "_error$response$data", "register", "name", "_error$response2", "_error$response2$data", "remove", "updateUser", "refreshToken", "newToken", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/contexts/AuthContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport Cookies from 'js-cookie';\nimport toast from 'react-hot-toast';\n\nconst AuthContext = createContext();\n\n// API base URL\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001/api';\n\n// Configure axios defaults\naxios.defaults.baseURL = API_BASE_URL;\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (!context) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [token, setToken] = useState(null);\n\n  // Initialize auth state\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // Set up axios interceptor for token\n  useEffect(() => {\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    } else {\n      delete axios.defaults.headers.common['Authorization'];\n    }\n  }, [token]);\n\n  const initializeAuth = async () => {\n    try {\n      const savedToken = Cookies.get('auth_token');\n      if (savedToken) {\n        setToken(savedToken);\n        axios.defaults.headers.common['Authorization'] = `Bearer ${savedToken}`;\n\n        // Verify token and get user data\n        const response = await axios.get('/auth/profile');\n        if (response.data.success) {\n          setUser(response.data.data.user);\n        } else {\n          // Token is invalid, remove it\n          logout();\n        }\n      }\n    } catch (error) {\n      console.error('Auth initialization error:', error);\n      logout();\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email, password) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/auth/login', {\n        email,\n        password\n      });\n\n      if (response.data.success) {\n        const { user: userData, token: userToken } = response.data.data;\n\n        // Save token to cookie (expires in 30 days)\n        Cookies.set('auth_token', userToken, { expires: 30 });\n\n        setToken(userToken);\n        setUser(userData);\n\n        toast.success('Login successful! Welcome back!');\n        return { success: true };\n      } else {\n        toast.error(response.data.message || 'Login failed');\n        return { success: false, message: response.data.message };\n      }\n    } catch (error) {\n      const message = error.response?.data?.message || 'Login failed. Please try again.';\n      toast.error(message);\n      return { success: false, message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (name, email, password) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/auth/register', {\n        name,\n        email,\n        password\n      });\n\n      if (response.data.success) {\n        const { user: userData, token: userToken } = response.data.data;\n\n        // Save token to cookie\n        Cookies.set('auth_token', userToken, { expires: 30 });\n\n        setToken(userToken);\n        setUser(userData);\n\n        toast.success('Registration successful! Welcome to Invisible Assessment Tool!');\n        return { success: true };\n      } else {\n        toast.error(response.data.message || 'Registration failed');\n        return { success: false, message: response.data.message };\n      }\n    } catch (error) {\n      const message = error.response?.data?.message || 'Registration failed. Please try again.';\n      toast.error(message);\n      return { success: false, message };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    // Remove token from cookie\n    Cookies.remove('auth_token');\n\n    // Clear state\n    setToken(null);\n    setUser(null);\n\n    // Remove axios header\n    delete axios.defaults.headers.common['Authorization'];\n\n    toast.success('Logged out successfully');\n  };\n\n  const updateUser = (userData) => {\n    setUser(userData);\n  };\n\n  const refreshToken = async () => {\n    try {\n      const response = await axios.post('/auth/refresh');\n      if (response.data.success) {\n        const newToken = response.data.data.token;\n        Cookies.set('auth_token', newToken, { expires: 30 });\n        setToken(newToken);\n        return true;\n      }\n    } catch (error) {\n      console.error('Token refresh failed:', error);\n      logout();\n      return false;\n    }\n  };\n\n  const value = {\n    user,\n    token,\n    loading,\n    login,\n    register,\n    logout,\n    updateUser,\n    refreshToken,\n    isAuthenticated: !!user\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,KAAK,MAAM,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,WAAW,gBAAGT,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMU,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACAT,KAAK,CAACU,QAAQ,CAACC,OAAO,GAAGL,YAAY;AAErC,OAAO,MAAMM,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGjB,UAAU,CAACQ,WAAW,CAAC;EACvC,IAAI,CAACS,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACuB,OAAO,EAAEC,UAAU,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyB,KAAK,EAAEC,QAAQ,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd0B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA1B,SAAS,CAAC,MAAM;IACd,IAAIwB,KAAK,EAAE;MACTvB,KAAK,CAACU,QAAQ,CAACgB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUJ,KAAK,EAAE;IACpE,CAAC,MAAM;MACL,OAAOvB,KAAK,CAACU,QAAQ,CAACgB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IACvD;EACF,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;EAEX,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACF,MAAMG,UAAU,GAAG3B,OAAO,CAAC4B,GAAG,CAAC,YAAY,CAAC;MAC5C,IAAID,UAAU,EAAE;QACdJ,QAAQ,CAACI,UAAU,CAAC;QACpB5B,KAAK,CAACU,QAAQ,CAACgB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,GAAG,UAAUC,UAAU,EAAE;;QAEvE;QACA,MAAME,QAAQ,GAAG,MAAM9B,KAAK,CAAC6B,GAAG,CAAC,eAAe,CAAC;QACjD,IAAIC,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;UACzBZ,OAAO,CAACU,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACZ,IAAI,CAAC;QAClC,CAAC,MAAM;UACL;UACAc,MAAM,CAAC,CAAC;QACV;MACF;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDD,MAAM,CAAC,CAAC;IACV,CAAC,SAAS;MACRX,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,KAAK,GAAG,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,KAAK;IACvC,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAM9B,KAAK,CAACuC,IAAI,CAAC,aAAa,EAAE;QAC/CF,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIR,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEb,IAAI,EAAEqB,QAAQ;UAAEjB,KAAK,EAAEkB;QAAU,CAAC,GAAGX,QAAQ,CAACC,IAAI,CAACA,IAAI;;QAE/D;QACA9B,OAAO,CAACyC,GAAG,CAAC,YAAY,EAAED,SAAS,EAAE;UAAEE,OAAO,EAAE;QAAG,CAAC,CAAC;QAErDnB,QAAQ,CAACiB,SAAS,CAAC;QACnBrB,OAAO,CAACoB,QAAQ,CAAC;QAEjBtC,KAAK,CAAC8B,OAAO,CAAC,iCAAiC,CAAC;QAChD,OAAO;UAAEA,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL9B,KAAK,CAACgC,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAACa,OAAO,IAAI,cAAc,CAAC;QACpD,OAAO;UAAEZ,OAAO,EAAE,KAAK;UAAEY,OAAO,EAAEd,QAAQ,CAACC,IAAI,CAACa;QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAW,eAAA,EAAAC,oBAAA;MACd,MAAMF,OAAO,GAAG,EAAAC,eAAA,GAAAX,KAAK,CAACJ,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBd,IAAI,cAAAe,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,iCAAiC;MAClF1C,KAAK,CAACgC,KAAK,CAACU,OAAO,CAAC;MACpB,OAAO;QAAEZ,OAAO,EAAE,KAAK;QAAEY;MAAQ,CAAC;IACpC,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMyB,QAAQ,GAAG,MAAAA,CAAOC,IAAI,EAAEX,KAAK,EAAEC,QAAQ,KAAK;IAChD,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMQ,QAAQ,GAAG,MAAM9B,KAAK,CAACuC,IAAI,CAAC,gBAAgB,EAAE;QAClDS,IAAI;QACJX,KAAK;QACLC;MACF,CAAC,CAAC;MAEF,IAAIR,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEb,IAAI,EAAEqB,QAAQ;UAAEjB,KAAK,EAAEkB;QAAU,CAAC,GAAGX,QAAQ,CAACC,IAAI,CAACA,IAAI;;QAE/D;QACA9B,OAAO,CAACyC,GAAG,CAAC,YAAY,EAAED,SAAS,EAAE;UAAEE,OAAO,EAAE;QAAG,CAAC,CAAC;QAErDnB,QAAQ,CAACiB,SAAS,CAAC;QACnBrB,OAAO,CAACoB,QAAQ,CAAC;QAEjBtC,KAAK,CAAC8B,OAAO,CAAC,gEAAgE,CAAC;QAC/E,OAAO;UAAEA,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACL9B,KAAK,CAACgC,KAAK,CAACJ,QAAQ,CAACC,IAAI,CAACa,OAAO,IAAI,qBAAqB,CAAC;QAC3D,OAAO;UAAEZ,OAAO,EAAE,KAAK;UAAEY,OAAO,EAAEd,QAAQ,CAACC,IAAI,CAACa;QAAQ,CAAC;MAC3D;IACF,CAAC,CAAC,OAAOV,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACd,MAAMN,OAAO,GAAG,EAAAK,gBAAA,GAAAf,KAAK,CAACJ,QAAQ,cAAAmB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlB,IAAI,cAAAmB,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,wCAAwC;MACzF1C,KAAK,CAACgC,KAAK,CAACU,OAAO,CAAC;MACpB,OAAO;QAAEZ,OAAO,EAAE,KAAK;QAAEY;MAAQ,CAAC;IACpC,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMW,MAAM,GAAGA,CAAA,KAAM;IACnB;IACAhC,OAAO,CAACkD,MAAM,CAAC,YAAY,CAAC;;IAE5B;IACA3B,QAAQ,CAAC,IAAI,CAAC;IACdJ,OAAO,CAAC,IAAI,CAAC;;IAEb;IACA,OAAOpB,KAAK,CAACU,QAAQ,CAACgB,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC;IAErDzB,KAAK,CAAC8B,OAAO,CAAC,yBAAyB,CAAC;EAC1C,CAAC;EAED,MAAMoB,UAAU,GAAIZ,QAAQ,IAAK;IAC/BpB,OAAO,CAACoB,QAAQ,CAAC;EACnB,CAAC;EAED,MAAMa,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAM9B,KAAK,CAACuC,IAAI,CAAC,eAAe,CAAC;MAClD,IAAIT,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB,MAAMsB,QAAQ,GAAGxB,QAAQ,CAACC,IAAI,CAACA,IAAI,CAACR,KAAK;QACzCtB,OAAO,CAACyC,GAAG,CAAC,YAAY,EAAEY,QAAQ,EAAE;UAAEX,OAAO,EAAE;QAAG,CAAC,CAAC;QACpDnB,QAAQ,CAAC8B,QAAQ,CAAC;QAClB,OAAO,IAAI;MACb;IACF,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CD,MAAM,CAAC,CAAC;MACR,OAAO,KAAK;IACd;EACF,CAAC;EAED,MAAMsB,KAAK,GAAG;IACZpC,IAAI;IACJI,KAAK;IACLF,OAAO;IACPe,KAAK;IACLW,QAAQ;IACRd,MAAM;IACNmB,UAAU;IACVC,YAAY;IACZG,eAAe,EAAE,CAAC,CAACrC;EACrB,CAAC;EAED,oBACEf,OAAA,CAACC,WAAW,CAACoD,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAtC,QAAA,EAChCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3C,GAAA,CA/JWF,YAAY;AAAA8C,EAAA,GAAZ9C,YAAY;AAAA,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}