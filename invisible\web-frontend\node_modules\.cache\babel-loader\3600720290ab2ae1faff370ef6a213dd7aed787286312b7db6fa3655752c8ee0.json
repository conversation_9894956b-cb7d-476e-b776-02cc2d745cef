{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst AlarmClock = createLucideIcon(\"AlarmClock\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"13\",\n  r: \"8\",\n  key: \"3y4lt7\"\n}], [\"path\", {\n  d: \"M12 9v4l2 2\",\n  key: \"1c63tq\"\n}], [\"path\", {\n  d: \"M5 3 2 6\",\n  key: \"18tl5t\"\n}], [\"path\", {\n  d: \"m22 6-3-3\",\n  key: \"1opdir\"\n}], [\"path\", {\n  d: \"M6.38 18.7 4 21\",\n  key: \"17xu3x\"\n}], [\"path\", {\n  d: \"M17.64 18.67 20 21\",\n  key: \"kv2oe2\"\n}]]);\nexport { AlarmClock as default };", "map": {"version": 3, "names": ["AlarmClock", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\alarm-clock.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name AlarmClock\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEzIiByPSI4IiAvPgogIDxwYXRoIGQ9Ik0xMiA5djRsMiAyIiAvPgogIDxwYXRoIGQ9Ik01IDMgMiA2IiAvPgogIDxwYXRoIGQ9Im0yMiA2LTMtMyIgLz4KICA8cGF0aCBkPSJNNi4zOCAxOC43IDQgMjEiIC8+CiAgPHBhdGggZD0iTTE3LjY0IDE4LjY3IDIwIDIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/alarm-clock\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst AlarmClock = createLucideIcon('AlarmClock', [\n  ['circle', { cx: '12', cy: '13', r: '8', key: '3y4lt7' }],\n  ['path', { d: 'M12 9v4l2 2', key: '1c63tq' }],\n  ['path', { d: 'M5 3 2 6', key: '18tl5t' }],\n  ['path', { d: 'm22 6-3-3', key: '1opdir' }],\n  ['path', { d: 'M6.38 18.7 4 21', key: '17xu3x' }],\n  ['path', { d: 'M17.64 18.67 20 21', key: 'kv2oe2' }],\n]);\n\nexport default AlarmClock;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,iBAAmB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAD,GAAA,EAAK;AAAA,CAAU,EACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}