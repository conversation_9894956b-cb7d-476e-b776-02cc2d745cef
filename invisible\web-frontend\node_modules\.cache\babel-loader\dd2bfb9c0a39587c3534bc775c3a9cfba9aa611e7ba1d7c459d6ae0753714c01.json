{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowBigLeftDash = createLucideIcon(\"ArrowBigLeftDash\", [[\"path\", {\n  d: \"M19 15V9\",\n  key: \"1hci5f\"\n}], [\"path\", {\n  d: \"M15 15h-3v4l-7-7 7-7v4h3v6z\",\n  key: \"16tjna\"\n}]]);\nexport { ArrowBigLeftDash as default };", "map": {"version": 3, "names": ["ArrowBigLeftDash", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\arrow-big-left-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowBigLeftDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkgMTVWOSIgLz4KICA8cGF0aCBkPSJNMTUgMTVoLTN2NGwtNy03IDctN3Y0aDN2NnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/arrow-big-left-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigLeftDash = createLucideIcon('ArrowBigLeftDash', [\n  ['path', { d: 'M19 15V9', key: '1hci5f' }],\n  ['path', { d: 'M15 15h-3v4l-7-7 7-7v4h3v6z', key: '16tjna' }],\n]);\n\nexport default ArrowBigLeftDash;\n"], "mappings": ";;;;;AAaM,MAAAA,gBAAA,GAAmBC,gBAAA,CAAiB,kBAAoB,GAC5D,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}