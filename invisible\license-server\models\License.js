const mongoose = require('mongoose');
const crypto = require('crypto');

const licenseSchema = new mongoose.Schema({
    // 🔑 LICENSE IDENTIFICATION
    licenseKey: {
        type: String,
        required: true,
        unique: true,
        default: () => generateLicenseKey()
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },

    // 📅 SUBSCRIPTION DETAILS
    tier: {
        type: Number,
        required: true,
        enum: [1, 2], // 1 = First purchase (50 screenshots), 2 = Renewal (45 screenshots)
        default: 1
    },
    purchaseDate: {
        type: Date,
        default: Date.now
    },
    expiryDate: {
        type: Date,
        required: true
    },
    isActive: {
        type: Boolean,
        default: true
    },

    // 📊 USAGE TRACKING
    screenshotsLimit: {
        type: Number,
        required: true,
        default: function () {
            return this.tier === 1 ? 50 : 45; // Updated pricing
        }
    },
    screenshotsUsed: {
        type: Number,
        default: 0
    },

    // 🔐 SECURITY & ANTI-PIRACY
    hardwareId: {
        type: String,
        required: false // Set on first activation
    },
    activationDate: {
        type: Date,
        required: false
    },
    lastUsed: {
        type: Date,
        default: Date.now
    },

    // 💳 PAYMENT DETAILS
    paymentId: {
        type: String,
        required: false
    },
    paymentMethod: {
        type: String,
        enum: ['paypal', 'stripe', 'crypto', 'razorpay'],
        default: 'razorpay'
    },
    amountPaid: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'INR'
    }
}, {
    timestamps: true
});

// 🔑 GENERATE UNIQUE LICENSE KEY
function generateLicenseKey() {
    const prefix = 'IAT'; // Invisible Assessment Tool
    const timestamp = Date.now().toString(36).toUpperCase();
    const random = crypto.randomBytes(8).toString('hex').toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
}

// 🔍 CHECK IF LICENSE IS VALID
licenseSchema.methods.isValid = function () {
    const now = new Date();
    return this.isActive &&
        this.expiryDate > now &&
        this.screenshotsUsed < this.screenshotsLimit;
};

// 📊 GET REMAINING SCREENSHOTS
licenseSchema.methods.getRemainingScreenshots = function () {
    return Math.max(0, this.screenshotsLimit - this.screenshotsUsed);
};

// 📅 GET DAYS REMAINING
licenseSchema.methods.getDaysRemaining = function () {
    const now = new Date();
    const diffTime = this.expiryDate - now;
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

// 📊 GET LICENSE STATUS
licenseSchema.methods.getStatus = function () {
    return {
        licenseKey: this.licenseKey,
        tier: this.tier,
        isActive: this.isActive,
        isValid: this.isValid(),
        screenshotsUsed: this.screenshotsUsed,
        screenshotsLimit: this.screenshotsLimit,
        screenshotsRemaining: this.getRemainingScreenshots(),
        daysRemaining: this.getDaysRemaining(),
        expiryDate: this.expiryDate,
        lastUsed: this.lastUsed,
        amountPaid: this.amountPaid,
        currency: this.currency
    };
};

module.exports = mongoose.model('License', licenseSchema);
