import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { CreditCard, Shield, CheckCircle } from 'lucide-react';

const PurchaseContainer = styled.div`
  min-height: 100vh;
  padding: 120px 20px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const PurchaseContent = styled.div`
  max-width: 1000px;
  margin: 0 auto;
`;

const PurchaseHeader = styled.div`
  text-align: center;
  margin-bottom: 60px;
`;

const PurchaseTitle = styled.h1`
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
`;

const PurchaseSubtitle = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
`;

const PurchaseGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const PlanCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  
  &.recommended {
    border: 2px solid #4CAF50;
  }
`;

const RecommendedBadge = styled.div`
  position: absolute;
  top: -15px;
  left: 50%;
  transform: translateX(-50%);
  background: #4CAF50;
  color: white;
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
`;

const PlanTitle = styled.h2`
  font-size: 1.8rem;
  margin-bottom: 10px;
  color: #4CAF50;
`;

const PlanPrice = styled.div`
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 20px;
`;

const PlanFeatures = styled.ul`
  list-style: none;
  margin-bottom: 30px;
  
  li {
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    
    svg {
      margin-right: 10px;
      color: #4CAF50;
    }
  }
`;

const PaymentSection = styled.div`
  margin-top: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
`;

const LoadingSpinner = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  
  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Purchase = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(false);
  const [userLicenses, setUserLicenses] = useState([]);

  useEffect(() => {
    fetchUserLicenses();
  }, []);

  const fetchUserLicenses = async () => {
    try {
      const response = await axios.get('/licenses/my-licenses');
      if (response.data.success) {
        setUserLicenses(response.data.data.licenses);
      }
    } catch (error) {
      console.error('Error fetching licenses:', error);
    }
  };

  const hasActiveLicense = userLicenses.some(license => license.isValid);
  const isFirstPurchase = user?.totalPurchases === 0;

  const plans = [
    {
      id: 'initial',
      title: 'Initial Purchase',
      price: '₹10',
      amount: 10,
      screenshots: 50,
      description: 'Perfect for getting started (TESTING PRICE)',
      features: [
        '50 Screenshots per month',
        'AI-powered problem analysis',
        'Complete stealth integration',
        'Hardware binding security',
        'Global keyboard shortcuts',
        'All programming languages',
        'Lifetime software access',
        'Priority support'
      ],
      recommended: isFirstPurchase,
      available: isFirstPurchase
    },
    {
      id: 'renewal',
      title: 'Monthly Renewal',
      price: '₹5',
      amount: 5,
      screenshots: 45,
      description: 'Continue your subscription (TESTING PRICE)',
      features: [
        '45 Screenshots per month',
        'AI-powered problem analysis',
        'Complete stealth integration',
        'Hardware binding security',
        'Global keyboard shortcuts',
        'All programming languages',
        'Continued access',
        'Regular updates'
      ],
      recommended: !isFirstPurchase,
      available: !isFirstPurchase || hasActiveLicense
    }
  ];

  const createRazorpayOrder = async (planId) => {
    try {
      setLoading(true);

      // 🔒 CHECK IF USER ALREADY HAS VALID LICENSE
      if (hasActiveLicense) {
        toast.error('You already have an active license! Please use your existing license or wait for it to expire.');
        setLoading(false);
        return;
      }

      const plan = plans.find(p => p.id === planId);

      console.log('🚀 Creating Razorpay order for plan:', planId);
      console.log('📊 Axios base URL:', axios.defaults.baseURL);
      console.log('🔑 Auth header:', axios.defaults.headers.common['Authorization']);

      // Create Razorpay order
      const response = await axios.post('/payments/create', {
        tier: planId === 'initial' ? 1 : 2
      });

      console.log('✅ Response received:', response.data);

      if (response.data.success) {
        const { orderId, amount, currency, razorpayKeyId, userDetails, testMode } = response.data.data;

        // Check if this is test mode
        if (testMode) {
          // Test mode - simulate payment success
          toast.info('Test mode: Simulating payment...');
          setTimeout(async () => {
            await verifyRazorpayPayment({
              razorpay_order_id: orderId,
              razorpay_payment_id: `pay_test_${Date.now()}`,
              razorpay_signature: 'test_signature'
            }, plan);
          }, 2000);
          return;
        }

        // Real Razorpay integration
        const options = {
          key: razorpayKeyId,
          amount: amount * 100, // Amount in paise
          currency: currency,
          name: 'Invisible Assessment Tool',
          description: plan.description,
          order_id: orderId,
          prefill: {
            name: userDetails.name,
            email: userDetails.email
          },
          theme: {
            color: '#4CAF50'
          },
          handler: async (response) => {
            await verifyRazorpayPayment(response, plan);
          },
          modal: {
            ondismiss: () => {
              setLoading(false);
              toast.info('Payment cancelled');
            }
          }
        };

        // Open Razorpay checkout
        const razorpay = new window.Razorpay(options);
        razorpay.open();
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('❌ Frontend payment creation error:', error);
      console.error('❌ Error response:', error.response);
      console.error('❌ Error message:', error.message);
      toast.error(error.response?.data?.message || 'Failed to create payment');
      setLoading(false);
    }
  };

  const verifyRazorpayPayment = async (paymentResponse, plan) => {
    try {
      // For test mode, create license directly
      if (paymentResponse.razorpay_order_id.startsWith('order_test_')) {
        console.log('🧪 Test mode - creating license directly');
        const response = await axios.get(`/payments/create-test-license/${user.id}`);

        if (response.data.success) {
          toast.success('Test payment successful! License created.');
          navigate('/download', {
            state: {
              licenseKey: response.data.data.licenseKey,
              plan: plan
            }
          });
          return;
        }
      }

      // Real payment verification
      const response = await axios.post('/payments/verify', {
        razorpay_order_id: paymentResponse.razorpay_order_id,
        razorpay_payment_id: paymentResponse.razorpay_payment_id,
        razorpay_signature: paymentResponse.razorpay_signature
      });

      if (response.data.success) {
        toast.success('Payment successful! License activated.');
        navigate('/download', {
          state: {
            licenseKey: response.data.data.licenseKey,
            plan: plan
          }
        });
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('❌ Payment verification error:', error);
      toast.error(error.response?.data?.message || 'Payment verification failed');
    } finally {
      setLoading(false);
    }
  };





  if (loading) {
    return (
      <PurchaseContainer>
        <LoadingSpinner>
          <div className="spinner"></div>
          <span style={{ marginLeft: '20px' }}>Processing payment...</span>
        </LoadingSpinner>
      </PurchaseContainer>
    );
  }

  return (
    <PurchaseContainer>
      <PurchaseContent>
        <PurchaseHeader>
          <PurchaseTitle>💳 Choose Your Plan</PurchaseTitle>
          <PurchaseSubtitle>
            Secure your access to the Invisible Assessment Tool
          </PurchaseSubtitle>
        </PurchaseHeader>

        {/* 🔒 ACTIVE LICENSE STATUS */}
        {hasActiveLicense && (
          <div style={{
            background: 'linear-gradient(45deg, #4CAF50, #45a049)',
            padding: '20px',
            borderRadius: '10px',
            marginBottom: '2rem',
            textAlign: 'center',
            color: 'white',
            border: '2px solid rgba(255, 255, 255, 0.2)'
          }}>
            <CheckCircle size={24} style={{ marginBottom: '10px' }} />
            <h3 style={{ margin: '0 0 10px 0' }}>✅ You have an active license!</h3>
            <p style={{ margin: '0 0 15px 0', opacity: 0.9 }}>
              You already have access to the software. Purchase is disabled until your current license expires.
            </p>
            <div style={{ display: 'flex', gap: '10px', justifyContent: 'center', flexWrap: 'wrap' }}>
              <button
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  fontWeight: '600'
                }}
                onClick={() => navigate('/download')}
              >
                📥 Go to Download
              </button>
              <button
                style={{
                  background: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  fontWeight: '600'
                }}
                onClick={() => navigate('/dashboard')}
              >
                📊 View Dashboard
              </button>
            </div>
          </div>
        )}

        <PurchaseGrid>
          {plans.map((plan) => (
            <PlanCard
              key={plan.id}
              className={plan.recommended ? 'recommended' : ''}
            >
              {plan.recommended && <RecommendedBadge>RECOMMENDED</RecommendedBadge>}

              <PlanTitle>{plan.title}</PlanTitle>
              <PlanPrice>{plan.price}</PlanPrice>
              <p style={{ opacity: 0.8, marginBottom: '20px' }}>{plan.description}</p>

              <PlanFeatures>
                {plan.features.map((feature, index) => (
                  <li key={index}>
                    <CheckCircle size={16} />
                    {feature}
                  </li>
                ))}
              </PlanFeatures>

              {plan.available ? (
                <PaymentSection>
                  <h4 style={{ marginBottom: '15px', display: 'flex', alignItems: 'center' }}>
                    <CreditCard size={20} style={{ marginRight: '10px' }} />
                    Secure Payment with Razorpay
                  </h4>
                  <p style={{ fontSize: '0.9rem', opacity: '0.8', marginBottom: '15px' }}>
                    💳 Cards • 📱 UPI • 🏦 Net Banking • 💰 Wallets
                  </p>

                  <button
                    style={{
                      width: '100%',
                      background: hasActiveLicense
                        ? 'linear-gradient(45deg, #666, #555)'
                        : 'linear-gradient(45deg, #3395ff, #1976d2)',
                      color: 'white',
                      border: 'none',
                      padding: '15px',
                      borderRadius: '10px',
                      fontSize: '1.1rem',
                      fontWeight: '600',
                      cursor: hasActiveLicense ? 'not-allowed' : 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '10px',
                      marginBottom: '10px',
                      opacity: hasActiveLicense ? 0.5 : 1
                    }}
                    onClick={() => createRazorpayOrder(plan.id)}
                    disabled={loading || hasActiveLicense}
                  >
                    {loading ? (
                      <>
                        <div className="loading-spinner"></div>
                        Processing...
                      </>
                    ) : hasActiveLicense ? (
                      <>
                        <Shield size={20} />
                        Already Licensed
                      </>
                    ) : (
                      <>
                        <CreditCard size={20} />
                        Pay {plan.price} with Razorpay
                      </>
                    )}
                  </button>


                </PaymentSection>
              ) : (
                <div style={{
                  padding: '20px',
                  background: 'rgba(255, 255, 255, 0.1)',
                  borderRadius: '10px',
                  textAlign: 'center',
                  opacity: 0.7
                }}>
                  <Shield size={24} style={{ marginBottom: '10px' }} />
                  <p>Not available for your account type</p>
                </div>
              )}
            </PlanCard>
          ))}
        </PurchaseGrid>
      </PurchaseContent>
    </PurchaseContainer>
  );
};

export default Purchase;
