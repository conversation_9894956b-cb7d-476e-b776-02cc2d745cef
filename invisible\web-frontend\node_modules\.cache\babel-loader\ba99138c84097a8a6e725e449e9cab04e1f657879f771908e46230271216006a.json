{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Dashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, CreditCard, Download, BarChart3, Calendar, Camera } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DashboardContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = DashboardContainer;\nconst DashboardContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n_c2 = DashboardContent;\nconst DashboardHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n`;\n_c3 = DashboardHeader;\nconst DashboardTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n`;\n_c4 = DashboardTitle;\nconst WelcomeMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n_c5 = WelcomeMessage;\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n`;\n_c6 = StatsGrid;\nconst StatCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 25px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  text-align: center;\n`;\n_c7 = StatCard;\nconst StatIcon = styled.div`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n_c8 = StatIcon;\nconst StatValue = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 5px;\n  color: #4CAF50;\n`;\n_c9 = StatValue;\nconst StatLabel = styled.div`\n  font-size: 0.9rem;\n  opacity: 0.8;\n`;\n_c0 = StatLabel;\nconst ActionsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 30px;\n  margin-bottom: 40px;\n`;\n_c1 = ActionsGrid;\nconst ActionCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c10 = ActionCard;\nconst ActionTitle = styled.h3`\n  font-size: 1.3rem;\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n_c11 = ActionTitle;\nconst ActionDescription = styled.p`\n  margin-bottom: 20px;\n  opacity: 0.8;\n  line-height: 1.6;\n`;\n_c12 = ActionDescription;\nconst LicenseSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c13 = LicenseSection;\nconst LicenseGrid = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c14 = LicenseGrid;\nconst LicenseCard = styled.div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 20px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n_c15 = LicenseCard;\nconst LicenseHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n`;\n_c16 = LicenseHeader;\nconst LicenseStatus = styled.span`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  \n  &.active {\n    background: rgba(76, 175, 80, 0.2);\n    color: #4CAF50;\n    border: 1px solid rgba(76, 175, 80, 0.3);\n  }\n  \n  &.expired {\n    background: rgba(244, 67, 54, 0.2);\n    color: #F44336;\n    border: 1px solid rgba(244, 67, 54, 0.3);\n  }\n`;\n_c17 = LicenseStatus;\nconst Dashboard = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const [licenses, setLicenses] = useState([]);\n  const [stats, setStats] = useState({\n    totalScreenshots: 0,\n    remainingScreenshots: 0,\n    activeLicenses: 0,\n    totalSpent: 0\n  });\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n  const fetchDashboardData = async () => {\n    try {\n      const [licensesResponse, usageResponse] = await Promise.all([axios.get('/licenses/my-licenses'), axios.get('/usage/dashboard')]);\n      if (licensesResponse.data.success) {\n        setLicenses(licensesResponse.data.data.licenses);\n      }\n      if (usageResponse.data.success) {\n        const data = usageResponse.data.data;\n        setStats({\n          totalScreenshots: data.totalScreenshotsUsed || 0,\n          remainingScreenshots: data.activeLicenses.reduce((sum, license) => sum + license.screenshotsRemaining, 0),\n          activeLicenses: data.activeLicenses.length,\n          totalSpent: data.recentPayments.reduce((sum, payment) => sum + payment.amount, 0)\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n      children: /*#__PURE__*/_jsxDEV(DashboardContent, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '60px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\",\n            style: {\n              margin: '0 auto 20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading dashboard...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DashboardContainer, {\n    children: /*#__PURE__*/_jsxDEV(DashboardContent, {\n      children: [/*#__PURE__*/_jsxDEV(DashboardHeader, {\n        children: [/*#__PURE__*/_jsxDEV(DashboardTitle, {\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(WelcomeMessage, {\n          children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name, \"!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(StatsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(Camera, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.remainingScreenshots\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Screenshots Remaining\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(BarChart3, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 215,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.totalScreenshots\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Screenshots Used\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(Calendar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 221,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: stats.activeLicenses\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Active Licenses\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 220,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(StatCard, {\n          children: [/*#__PURE__*/_jsxDEV(StatIcon, {\n            children: /*#__PURE__*/_jsxDEV(CreditCard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatValue, {\n            children: [\"\\u20B9\", stats.totalSpent]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(StatLabel, {\n            children: \"Total Spent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ActionsGrid, {\n        children: [/*#__PURE__*/_jsxDEV(ActionCard, {\n          children: [/*#__PURE__*/_jsxDEV(ActionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), \"Purchase License\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n            children: \"Get more screenshots or renew your existing license. Choose between initial purchase (\\u20B95,000) or monthly renewal (\\u20B9250).\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/purchase\",\n            className: \"btn-primary\",\n            children: \"Purchase Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ActionCard, {\n          children: [/*#__PURE__*/_jsxDEV(ActionTitle, {\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 15\n            }, this), \"Download Software\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ActionDescription, {\n            children: \"Download the latest version of the Invisible Assessment Tool. Secure, hardware-bound installation.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/download\",\n            className: \"btn-secondary\",\n            children: \"Download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LicenseSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            marginBottom: '20px',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '10px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(User, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 263,\n            columnNumber: 13\n          }, this), \"Your Licenses\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 262,\n          columnNumber: 11\n        }, this), licenses.length > 0 ? /*#__PURE__*/_jsxDEV(LicenseGrid, {\n          children: licenses.map((license, index) => /*#__PURE__*/_jsxDEV(LicenseCard, {\n            children: [/*#__PURE__*/_jsxDEV(LicenseHeader, {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                  children: [\"License #\", license.licenseKey.slice(-8)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.9rem',\n                    opacity: '0.8',\n                    marginTop: '5px'\n                  },\n                  children: [\"Tier \", license.tier, \" - \", license.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal']\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(LicenseStatus, {\n                className: license.isValid ? 'active' : 'expired',\n                children: license.isValid ? 'Active' : 'Expired'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 278,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',\n                gap: '15px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.8rem',\n                    opacity: '0.7'\n                  },\n                  children: \"Screenshots\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: '600'\n                  },\n                  children: [license.screenshotsUsed, \"/\", license.screenshotsLimit]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.8rem',\n                    opacity: '0.7'\n                  },\n                  children: \"Remaining\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: '600',\n                    color: '#4CAF50'\n                  },\n                  children: license.screenshotsRemaining\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 290,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontSize: '0.8rem',\n                    opacity: '0.7'\n                  },\n                  children: \"Days Left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: '600'\n                  },\n                  children: license.daysRemaining\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 294,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 19\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '40px',\n            opacity: '0.8'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"No licenses found. Purchase your first license to get started!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/purchase\",\n            className: \"btn-primary\",\n            style: {\n              marginTop: '20px'\n            },\n            children: \"Purchase License\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 261,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 200,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"QJg+Lv7uc1D/GNlSFu+CSWklW40=\", false, function () {\n  return [useAuth];\n});\n_c18 = Dashboard;\nexport default Dashboard;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18;\n$RefreshReg$(_c, \"DashboardContainer\");\n$RefreshReg$(_c2, \"DashboardContent\");\n$RefreshReg$(_c3, \"DashboardHeader\");\n$RefreshReg$(_c4, \"DashboardTitle\");\n$RefreshReg$(_c5, \"WelcomeMessage\");\n$RefreshReg$(_c6, \"StatsGrid\");\n$RefreshReg$(_c7, \"StatCard\");\n$RefreshReg$(_c8, \"StatIcon\");\n$RefreshReg$(_c9, \"StatValue\");\n$RefreshReg$(_c0, \"StatLabel\");\n$RefreshReg$(_c1, \"ActionsGrid\");\n$RefreshReg$(_c10, \"ActionCard\");\n$RefreshReg$(_c11, \"ActionTitle\");\n$RefreshReg$(_c12, \"ActionDescription\");\n$RefreshReg$(_c13, \"LicenseSection\");\n$RefreshReg$(_c14, \"LicenseGrid\");\n$RefreshReg$(_c15, \"LicenseCard\");\n$RefreshReg$(_c16, \"LicenseHeader\");\n$RefreshReg$(_c17, \"LicenseStatus\");\n$RefreshReg$(_c18, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "styled", "axios", "useAuth", "User", "CreditCard", "Download", "BarChart3", "Calendar", "Camera", "jsxDEV", "_jsxDEV", "DashboardContainer", "div", "_c", "DashboardContent", "_c2", "DashboardHeader", "_c3", "DashboardTitle", "h1", "_c4", "WelcomeMessage", "p", "_c5", "StatsGrid", "_c6", "StatCard", "_c7", "StatIcon", "_c8", "StatValue", "_c9", "StatLabel", "_c0", "ActionsGrid", "_c1", "ActionCard", "_c10", "ActionTitle", "h3", "_c11", "ActionDescription", "_c12", "LicenseSection", "_c13", "LicenseGrid", "_c14", "LicenseCard", "_c15", "LicenseHeader", "_c16", "LicenseStatus", "span", "_c17", "Dashboard", "_s", "user", "licenses", "setLicenses", "stats", "setStats", "totalScreenshots", "remainingScreenshots", "activeLicenses", "totalSpent", "loading", "setLoading", "fetchDashboardData", "licensesResponse", "usageResponse", "Promise", "all", "get", "data", "success", "totalScreenshotsUsed", "reduce", "sum", "license", "screenshotsRemaining", "length", "recentPayments", "payment", "amount", "error", "console", "children", "style", "textAlign", "padding", "className", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "size", "to", "marginBottom", "display", "alignItems", "gap", "map", "index", "licenseKey", "slice", "fontSize", "opacity", "marginTop", "tier", "<PERSON><PERSON><PERSON><PERSON>", "gridTemplateColumns", "fontWeight", "screenshotsUsed", "screenshotsLimit", "color", "daysRemaining", "_c18", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Dashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, CreditCard, Download, BarChart3, Calendar, Camera } from 'lucide-react';\n\nconst DashboardContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst DashboardContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst DashboardHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n`;\n\nconst DashboardTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n`;\n\nconst WelcomeMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n\nconst StatsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n  margin-bottom: 40px;\n`;\n\nconst StatCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 25px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  text-align: center;\n`;\n\nconst StatIcon = styled.div`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n\nconst StatValue = styled.div`\n  font-size: 2rem;\n  font-weight: bold;\n  margin-bottom: 5px;\n  color: #4CAF50;\n`;\n\nconst StatLabel = styled.div`\n  font-size: 0.9rem;\n  opacity: 0.8;\n`;\n\nconst ActionsGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: 30px;\n  margin-bottom: 40px;\n`;\n\nconst ActionCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst ActionTitle = styled.h3`\n  font-size: 1.3rem;\n  margin-bottom: 15px;\n  display: flex;\n  align-items: center;\n  gap: 10px;\n`;\n\nconst ActionDescription = styled.p`\n  margin-bottom: 20px;\n  opacity: 0.8;\n  line-height: 1.6;\n`;\n\nconst LicenseSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst LicenseGrid = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst LicenseCard = styled.div`\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  padding: 20px;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n`;\n\nconst LicenseHeader = styled.div`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n`;\n\nconst LicenseStatus = styled.span`\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 0.8rem;\n  font-weight: 600;\n  \n  &.active {\n    background: rgba(76, 175, 80, 0.2);\n    color: #4CAF50;\n    border: 1px solid rgba(76, 175, 80, 0.3);\n  }\n  \n  &.expired {\n    background: rgba(244, 67, 54, 0.2);\n    color: #F44336;\n    border: 1px solid rgba(244, 67, 54, 0.3);\n  }\n`;\n\nconst Dashboard = () => {\n  const { user } = useAuth();\n  const [licenses, setLicenses] = useState([]);\n  const [stats, setStats] = useState({\n    totalScreenshots: 0,\n    remainingScreenshots: 0,\n    activeLicenses: 0,\n    totalSpent: 0\n  });\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchDashboardData();\n  }, []);\n\n  const fetchDashboardData = async () => {\n    try {\n      const [licensesResponse, usageResponse] = await Promise.all([\n        axios.get('/licenses/my-licenses'),\n        axios.get('/usage/dashboard')\n      ]);\n\n      if (licensesResponse.data.success) {\n        setLicenses(licensesResponse.data.data.licenses);\n      }\n\n      if (usageResponse.data.success) {\n        const data = usageResponse.data.data;\n        setStats({\n          totalScreenshots: data.totalScreenshotsUsed || 0,\n          remainingScreenshots: data.activeLicenses.reduce((sum, license) => sum + license.screenshotsRemaining, 0),\n          activeLicenses: data.activeLicenses.length,\n          totalSpent: data.recentPayments.reduce((sum, payment) => sum + payment.amount, 0)\n        });\n      }\n    } catch (error) {\n      console.error('Error fetching dashboard data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <DashboardContainer>\n        <DashboardContent>\n          <div style={{ textAlign: 'center', padding: '60px 0' }}>\n            <div className=\"loading-spinner\" style={{ margin: '0 auto 20px' }}></div>\n            <p>Loading dashboard...</p>\n          </div>\n        </DashboardContent>\n      </DashboardContainer>\n    );\n  }\n\n  return (\n    <DashboardContainer>\n      <DashboardContent>\n        <DashboardHeader>\n          <DashboardTitle>Dashboard</DashboardTitle>\n          <WelcomeMessage>Welcome back, {user?.name}!</WelcomeMessage>\n        </DashboardHeader>\n\n        <StatsGrid>\n          <StatCard>\n            <StatIcon><Camera /></StatIcon>\n            <StatValue>{stats.remainingScreenshots}</StatValue>\n            <StatLabel>Screenshots Remaining</StatLabel>\n          </StatCard>\n          \n          <StatCard>\n            <StatIcon><BarChart3 /></StatIcon>\n            <StatValue>{stats.totalScreenshots}</StatValue>\n            <StatLabel>Total Screenshots Used</StatLabel>\n          </StatCard>\n          \n          <StatCard>\n            <StatIcon><Calendar /></StatIcon>\n            <StatValue>{stats.activeLicenses}</StatValue>\n            <StatLabel>Active Licenses</StatLabel>\n          </StatCard>\n          \n          <StatCard>\n            <StatIcon><CreditCard /></StatIcon>\n            <StatValue>₹{stats.totalSpent}</StatValue>\n            <StatLabel>Total Spent</StatLabel>\n          </StatCard>\n        </StatsGrid>\n\n        <ActionsGrid>\n          <ActionCard>\n            <ActionTitle>\n              <CreditCard size={24} />\n              Purchase License\n            </ActionTitle>\n            <ActionDescription>\n              Get more screenshots or renew your existing license. Choose between initial purchase (₹5,000) or monthly renewal (₹250).\n            </ActionDescription>\n            <Link to=\"/purchase\" className=\"btn-primary\">\n              Purchase Now\n            </Link>\n          </ActionCard>\n\n          <ActionCard>\n            <ActionTitle>\n              <Download size={24} />\n              Download Software\n            </ActionTitle>\n            <ActionDescription>\n              Download the latest version of the Invisible Assessment Tool. Secure, hardware-bound installation.\n            </ActionDescription>\n            <Link to=\"/download\" className=\"btn-secondary\">\n              Download\n            </Link>\n          </ActionCard>\n        </ActionsGrid>\n\n        <LicenseSection>\n          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center', gap: '10px' }}>\n            <User size={24} />\n            Your Licenses\n          </h2>\n          \n          {licenses.length > 0 ? (\n            <LicenseGrid>\n              {licenses.map((license, index) => (\n                <LicenseCard key={index}>\n                  <LicenseHeader>\n                    <div>\n                      <strong>License #{license.licenseKey.slice(-8)}</strong>\n                      <div style={{ fontSize: '0.9rem', opacity: '0.8', marginTop: '5px' }}>\n                        Tier {license.tier} - {license.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal'}\n                      </div>\n                    </div>\n                    <LicenseStatus className={license.isValid ? 'active' : 'expired'}>\n                      {license.isValid ? 'Active' : 'Expired'}\n                    </LicenseStatus>\n                  </LicenseHeader>\n                  \n                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '15px' }}>\n                    <div>\n                      <div style={{ fontSize: '0.8rem', opacity: '0.7' }}>Screenshots</div>\n                      <div style={{ fontWeight: '600' }}>{license.screenshotsUsed}/{license.screenshotsLimit}</div>\n                    </div>\n                    <div>\n                      <div style={{ fontSize: '0.8rem', opacity: '0.7' }}>Remaining</div>\n                      <div style={{ fontWeight: '600', color: '#4CAF50' }}>{license.screenshotsRemaining}</div>\n                    </div>\n                    <div>\n                      <div style={{ fontSize: '0.8rem', opacity: '0.7' }}>Days Left</div>\n                      <div style={{ fontWeight: '600' }}>{license.daysRemaining}</div>\n                    </div>\n                  </div>\n                </LicenseCard>\n              ))}\n            </LicenseGrid>\n          ) : (\n            <div style={{ textAlign: 'center', padding: '40px', opacity: '0.8' }}>\n              <p>No licenses found. Purchase your first license to get started!</p>\n              <Link to=\"/purchase\" className=\"btn-primary\" style={{ marginTop: '20px' }}>\n                Purchase License\n              </Link>\n            </div>\n          )}\n        </LicenseSection>\n      </DashboardContent>\n    </DashboardContainer>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvF,MAAMC,kBAAkB,GAAGX,MAAM,CAACY,GAAG;AACrC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,kBAAkB;AAOxB,MAAMG,gBAAgB,GAAGd,MAAM,CAACY,GAAG;AACnC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,gBAAgB;AAKtB,MAAME,eAAe,GAAGhB,MAAM,CAACY,GAAG;AAClC;AACA;AACA,CAAC;AAACK,GAAA,GAHID,eAAe;AAKrB,MAAME,cAAc,GAAGlB,MAAM,CAACmB,EAAE;AAChC;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,cAAc;AAKpB,MAAMG,cAAc,GAAGrB,MAAM,CAACsB,CAAC;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,cAAc;AAKpB,MAAMG,SAAS,GAAGxB,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GALID,SAAS;AAOf,MAAME,QAAQ,GAAG1B,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAPID,QAAQ;AASd,MAAME,QAAQ,GAAG5B,MAAM,CAACY,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAJID,QAAQ;AAMd,MAAME,SAAS,GAAG9B,MAAM,CAACY,GAAG;AAC5B;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GALID,SAAS;AAOf,MAAME,SAAS,GAAGhC,MAAM,CAACY,GAAG;AAC5B;AACA;AACA,CAAC;AAACqB,GAAA,GAHID,SAAS;AAKf,MAAME,WAAW,GAAGlC,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GALID,WAAW;AAOjB,MAAME,UAAU,GAAGpC,MAAM,CAACY,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,IAAA,GANID,UAAU;AAQhB,MAAME,WAAW,GAAGtC,MAAM,CAACuC,EAAE;AAC7B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GANIF,WAAW;AAQjB,MAAMG,iBAAiB,GAAGzC,MAAM,CAACsB,CAAC;AAClC;AACA;AACA;AACA,CAAC;AAACoB,IAAA,GAJID,iBAAiB;AAMvB,MAAME,cAAc,GAAG3C,MAAM,CAACY,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACgC,IAAA,GANID,cAAc;AAQpB,MAAME,WAAW,GAAG7C,MAAM,CAACY,GAAG;AAC9B;AACA;AACA,CAAC;AAACkC,IAAA,GAHID,WAAW;AAKjB,MAAME,WAAW,GAAG/C,MAAM,CAACY,GAAG;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GALID,WAAW;AAOjB,MAAME,aAAa,GAAGjD,MAAM,CAACY,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GALID,aAAa;AAOnB,MAAME,aAAa,GAAGnD,MAAM,CAACoD,IAAI;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAjBIF,aAAa;AAmBnB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtB,MAAM;IAAEC;EAAK,CAAC,GAAGtD,OAAO,CAAC,CAAC;EAC1B,MAAM,CAACuD,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC8D,KAAK,EAAEC,QAAQ,CAAC,GAAG/D,QAAQ,CAAC;IACjCgE,gBAAgB,EAAE,CAAC;IACnBC,oBAAoB,EAAE,CAAC;IACvBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrE,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACdqE,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAM,CAACC,gBAAgB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DtE,KAAK,CAACuE,GAAG,CAAC,uBAAuB,CAAC,EAClCvE,KAAK,CAACuE,GAAG,CAAC,kBAAkB,CAAC,CAC9B,CAAC;MAEF,IAAIJ,gBAAgB,CAACK,IAAI,CAACC,OAAO,EAAE;QACjChB,WAAW,CAACU,gBAAgB,CAACK,IAAI,CAACA,IAAI,CAAChB,QAAQ,CAAC;MAClD;MAEA,IAAIY,aAAa,CAACI,IAAI,CAACC,OAAO,EAAE;QAC9B,MAAMD,IAAI,GAAGJ,aAAa,CAACI,IAAI,CAACA,IAAI;QACpCb,QAAQ,CAAC;UACPC,gBAAgB,EAAEY,IAAI,CAACE,oBAAoB,IAAI,CAAC;UAChDb,oBAAoB,EAAEW,IAAI,CAACV,cAAc,CAACa,MAAM,CAAC,CAACC,GAAG,EAAEC,OAAO,KAAKD,GAAG,GAAGC,OAAO,CAACC,oBAAoB,EAAE,CAAC,CAAC;UACzGhB,cAAc,EAAEU,IAAI,CAACV,cAAc,CAACiB,MAAM;UAC1ChB,UAAU,EAAES,IAAI,CAACQ,cAAc,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEK,OAAO,KAAKL,GAAG,GAAGK,OAAO,CAACC,MAAM,EAAE,CAAC;QAClF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACRlB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEvD,OAAA,CAACC,kBAAkB;MAAA2E,QAAA,eACjB5E,OAAA,CAACI,gBAAgB;QAAAwE,QAAA,eACf5E,OAAA;UAAK6E,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACrD5E,OAAA;YAAKgF,SAAS,EAAC,iBAAiB;YAACH,KAAK,EAAE;cAAEI,MAAM,EAAE;YAAc;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzErF,OAAA;YAAA4E,QAAA,EAAG;UAAoB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAEzB;EAEA,oBACErF,OAAA,CAACC,kBAAkB;IAAA2E,QAAA,eACjB5E,OAAA,CAACI,gBAAgB;MAAAwE,QAAA,gBACf5E,OAAA,CAACM,eAAe;QAAAsE,QAAA,gBACd5E,OAAA,CAACQ,cAAc;UAAAoE,QAAA,EAAC;QAAS;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC,eAC1CrF,OAAA,CAACW,cAAc;UAAAiE,QAAA,GAAC,gBAAc,EAAC9B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEwC,IAAI,EAAC,GAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7C,CAAC,eAElBrF,OAAA,CAACc,SAAS;QAAA8D,QAAA,gBACR5E,OAAA,CAACgB,QAAQ;UAAA4D,QAAA,gBACP5E,OAAA,CAACkB,QAAQ;YAAA0D,QAAA,eAAC5E,OAAA,CAACF,MAAM;cAAAoF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAC/BrF,OAAA,CAACoB,SAAS;YAAAwD,QAAA,EAAE3B,KAAK,CAACG;UAAoB;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnDrF,OAAA,CAACsB,SAAS;YAAAsD,QAAA,EAAC;UAAqB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC,CAAC,eAEXrF,OAAA,CAACgB,QAAQ;UAAA4D,QAAA,gBACP5E,OAAA,CAACkB,QAAQ;YAAA0D,QAAA,eAAC5E,OAAA,CAACJ,SAAS;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAClCrF,OAAA,CAACoB,SAAS;YAAAwD,QAAA,EAAE3B,KAAK,CAACE;UAAgB;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC/CrF,OAAA,CAACsB,SAAS;YAAAsD,QAAA,EAAC;UAAsB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEXrF,OAAA,CAACgB,QAAQ;UAAA4D,QAAA,gBACP5E,OAAA,CAACkB,QAAQ;YAAA0D,QAAA,eAAC5E,OAAA,CAACH,QAAQ;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACjCrF,OAAA,CAACoB,SAAS;YAAAwD,QAAA,EAAE3B,KAAK,CAACI;UAAc;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC7CrF,OAAA,CAACsB,SAAS;YAAAsD,QAAA,EAAC;UAAe;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAEXrF,OAAA,CAACgB,QAAQ;UAAA4D,QAAA,gBACP5E,OAAA,CAACkB,QAAQ;YAAA0D,QAAA,eAAC5E,OAAA,CAACN,UAAU;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eACnCrF,OAAA,CAACoB,SAAS;YAAAwD,QAAA,GAAC,QAAC,EAAC3B,KAAK,CAACK,UAAU;UAAA;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAC1CrF,OAAA,CAACsB,SAAS;YAAAsD,QAAA,EAAC;UAAW;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAW,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEZrF,OAAA,CAACwB,WAAW;QAAAoD,QAAA,gBACV5E,OAAA,CAAC0B,UAAU;UAAAkD,QAAA,gBACT5E,OAAA,CAAC4B,WAAW;YAAAgD,QAAA,gBACV5E,OAAA,CAACN,UAAU;cAAC6F,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,oBAE1B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdrF,OAAA,CAAC+B,iBAAiB;YAAA6C,QAAA,EAAC;UAEnB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACpBrF,OAAA,CAACX,IAAI;YAACmG,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,aAAa;YAAAJ,QAAA,EAAC;UAE7C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,eAEbrF,OAAA,CAAC0B,UAAU;UAAAkD,QAAA,gBACT5E,OAAA,CAAC4B,WAAW;YAAAgD,QAAA,gBACV5E,OAAA,CAACL,QAAQ;cAAC4F,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,qBAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACdrF,OAAA,CAAC+B,iBAAiB;YAAA6C,QAAA,EAAC;UAEnB;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAmB,CAAC,eACpBrF,OAAA,CAACX,IAAI;YAACmG,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,eAAe;YAAAJ,QAAA,EAAC;UAE/C;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEdrF,OAAA,CAACiC,cAAc;QAAA2C,QAAA,gBACb5E,OAAA;UAAI6E,KAAK,EAAE;YAAEY,YAAY,EAAE,MAAM;YAAEC,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAO,CAAE;UAAAhB,QAAA,gBACtF5E,OAAA,CAACP,IAAI;YAAC8F,IAAI,EAAE;UAAG;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAEpB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,EAEJtC,QAAQ,CAACuB,MAAM,GAAG,CAAC,gBAClBtE,OAAA,CAACmC,WAAW;UAAAyC,QAAA,EACT7B,QAAQ,CAAC8C,GAAG,CAAC,CAACzB,OAAO,EAAE0B,KAAK,kBAC3B9F,OAAA,CAACqC,WAAW;YAAAuC,QAAA,gBACV5E,OAAA,CAACuC,aAAa;cAAAqC,QAAA,gBACZ5E,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAA4E,QAAA,GAAQ,WAAS,EAACR,OAAO,CAAC2B,UAAU,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACxDrF,OAAA;kBAAK6E,KAAK,EAAE;oBAAEoB,QAAQ,EAAE,QAAQ;oBAAEC,OAAO,EAAE,KAAK;oBAAEC,SAAS,EAAE;kBAAM,CAAE;kBAAAvB,QAAA,GAAC,OAC/D,EAACR,OAAO,CAACgC,IAAI,EAAC,KAAG,EAAChC,OAAO,CAACgC,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,iBAAiB;gBAAA;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrF,OAAA,CAACyC,aAAa;gBAACuC,SAAS,EAAEZ,OAAO,CAACiC,OAAO,GAAG,QAAQ,GAAG,SAAU;gBAAAzB,QAAA,EAC9DR,OAAO,CAACiC,OAAO,GAAG,QAAQ,GAAG;cAAS;gBAAAnB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEhBrF,OAAA;cAAK6E,KAAK,EAAE;gBAAEa,OAAO,EAAE,MAAM;gBAAEY,mBAAmB,EAAE,sCAAsC;gBAAEV,GAAG,EAAE;cAAO,CAAE;cAAAhB,QAAA,gBACxG5E,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAK6E,KAAK,EAAE;oBAAEoB,QAAQ,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAM,CAAE;kBAAAtB,QAAA,EAAC;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrErF,OAAA;kBAAK6E,KAAK,EAAE;oBAAE0B,UAAU,EAAE;kBAAM,CAAE;kBAAA3B,QAAA,GAAER,OAAO,CAACoC,eAAe,EAAC,GAAC,EAACpC,OAAO,CAACqC,gBAAgB;gBAAA;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1F,CAAC,eACNrF,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAK6E,KAAK,EAAE;oBAAEoB,QAAQ,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAM,CAAE;kBAAAtB,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErF,OAAA;kBAAK6E,KAAK,EAAE;oBAAE0B,UAAU,EAAE,KAAK;oBAAEG,KAAK,EAAE;kBAAU,CAAE;kBAAA9B,QAAA,EAAER,OAAO,CAACC;gBAAoB;kBAAAa,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC,eACNrF,OAAA;gBAAA4E,QAAA,gBACE5E,OAAA;kBAAK6E,KAAK,EAAE;oBAAEoB,QAAQ,EAAE,QAAQ;oBAAEC,OAAO,EAAE;kBAAM,CAAE;kBAAAtB,QAAA,EAAC;gBAAS;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnErF,OAAA;kBAAK6E,KAAK,EAAE;oBAAE0B,UAAU,EAAE;kBAAM,CAAE;kBAAA3B,QAAA,EAAER,OAAO,CAACuC;gBAAa;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GA1BUS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA2BV,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,gBAEdrF,OAAA;UAAK6E,KAAK,EAAE;YAAEC,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE,MAAM;YAAEmB,OAAO,EAAE;UAAM,CAAE;UAAAtB,QAAA,gBACnE5E,OAAA;YAAA4E,QAAA,EAAG;UAA8D;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrErF,OAAA,CAACX,IAAI;YAACmG,EAAE,EAAC,WAAW;YAACR,SAAS,EAAC,aAAa;YAACH,KAAK,EAAE;cAAEsB,SAAS,EAAE;YAAO,CAAE;YAAAvB,QAAA,EAAC;UAE3E;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEzB,CAAC;AAACxC,EAAA,CAxKID,SAAS;EAAA,QACIpD,OAAO;AAAA;AAAAoH,IAAA,GADpBhE,SAAS;AA0Kf,eAAeA,SAAS;AAAC,IAAAzC,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAiE,IAAA;AAAAC,YAAA,CAAA1G,EAAA;AAAA0G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAAtG,GAAA;AAAAsG,YAAA,CAAAnG,GAAA;AAAAmG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,GAAA;AAAA0F,YAAA,CAAAxF,GAAA;AAAAwF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAA/E,IAAA;AAAA+E,YAAA,CAAA7E,IAAA;AAAA6E,YAAA,CAAA3E,IAAA;AAAA2E,YAAA,CAAAzE,IAAA;AAAAyE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}