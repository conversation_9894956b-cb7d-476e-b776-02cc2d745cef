import { TypeScriptReporterOptions } from './typescript-reporter/TypeScriptReporterOptions';
import { EsLintReporterOptions } from './eslint-reporter/EsLintReporterOptions';
import { IssueOptions } from './issue/IssueOptions';
import { FormatterOptions } from './formatter';
import LoggerOptions from './logger/LoggerOptions';
interface ForkTsCheckerWebpackPluginOptions {
    async?: boolean;
    typescript?: TypeScriptReporterOptions;
    eslint?: EsLintReporterOptions;
    formatter?: FormatterOptions;
    issue?: IssueOptions;
    logger?: LoggerOptions;
}
export { ForkTsCheckerWebpackPluginOptions };
