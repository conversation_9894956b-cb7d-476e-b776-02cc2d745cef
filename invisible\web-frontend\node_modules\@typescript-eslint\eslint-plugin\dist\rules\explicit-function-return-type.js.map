{"version": 3, "file": "explicit-function-return-type.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-function-return-type.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAChC,6EAIyC;AAgBzC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;YAChE,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,kCAAkC;SACtD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,oDAAoD,EAAE;wBACpD,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,WAAW,EACT,yFAAyF;wBAC3F,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,WAAW,EACT,gFAAgF;wBAClF,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,yCAAyC,EAAE;wBACzC,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;oBACD,mCAAmC,EAAE;wBACnC,WAAW,EACT,sEAAsE;wBACxE,IAAI,EAAE,SAAS;qBAChB;oBACD,YAAY,EAAE;wBACZ,WAAW,EACT,gGAAgG;wBAClG,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;wBACD,IAAI,EAAE,OAAO;qBACd;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,qEAAqE;wBACvE,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;YACvB,6BAA6B,EAAE,IAAI;YACnC,yBAAyB,EAAE,IAAI;YAC/B,yCAAyC,EAAE,IAAI;YAC/C,oDAAoD,EAAE,KAAK;YAC3D,mCAAmC,EAAE,KAAK;YAC1C,YAAY,EAAE,EAAE;YAChB,UAAU,EAAE,KAAK;SAClB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,SAAS,iBAAiB,CACxB,IAGgC;;YAEhC,IAAI,OAAO,CAAC,mCAAmC,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;gBACvE,OAAO,IAAI,CAAC;aACb;YAED,IAAI,OAAO,CAAC,UAAU,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE;gBACtC,OAAO,IAAI,CAAC;aACb;YAED,IAAI,CAAC,CAAA,MAAA,OAAO,CAAC,YAAY,0CAAE,MAAM,CAAA,EAAE;gBACjC,OAAO,KAAK,CAAC;aACd;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C;gBACA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC3B,IAAI,QAAQ,CAAC;gBACb,IAAI,MAAA,IAAI,CAAC,EAAE,0CAAE,IAAI,EAAE;oBACjB,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC;iBACzB;qBAAM,IAAI,MAAM,EAAE;oBACjB,QAAQ,MAAM,CAAC,IAAI,EAAE;wBACnB,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC;4BACtC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE;gCAChD,QAAQ,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;6BAC3B;4BACD,MAAM;yBACP;wBACD,KAAK,sBAAc,CAAC,gBAAgB,CAAC;wBACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;wBACvC,KAAK,sBAAc,CAAC,QAAQ,CAAC,CAAC;4BAC5B,IACE,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gCAC7C,MAAM,CAAC,QAAQ,KAAK,KAAK,EACzB;gCACA,QAAQ,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC;6BAC5B;4BACD,MAAM;yBACP;qBACF;iBACF;gBACD,IAAI,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;oBAC3D,OAAO,IAAI,CAAC;iBACb;aACF;YACD,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;gBAChD,IAAI,CAAC,EAAE;gBACP,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC1C,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAC7C;gBACA,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,MAAM,CACb,IAGgC;YAEhC,OAAO,IAAI,CAAC,MAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,6CAA6C,CAC3C,IAAoE;gBAEpE,IACE,OAAO,CAAC,oDAAoD;oBAC5D,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;oBACpD,IAAI,CAAC,UAAU;oBACf,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACjD,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,EAC7B;oBACA,OAAO;iBACR;gBAED,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAC3B,OAAO;iBACR;gBAED,IACE,OAAO,CAAC,6BAA6B;oBACrC,CAAC,IAAA,6DAAmC,EAAC,IAAI,EAAE,OAAO,CAAC;wBACjD,IAAA,+CAAqB,EAAC,IAAI,CAAC,CAAC,EAC9B;oBACA,OAAO;iBACR;gBAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CACvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CACH,CAAC;YACJ,CAAC;YACD,mBAAmB,CAAC,IAAI;gBACtB,IAAI,iBAAiB,CAAC,IAAI,CAAC,EAAE;oBAC3B,OAAO;iBACR;gBACD,IAAI,OAAO,CAAC,6BAA6B,IAAI,IAAI,CAAC,UAAU,EAAE;oBAC5D,OAAO;iBACR;gBAED,IAAA,iDAAuB,EAAC,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,CACvD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG;oBACH,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CACH,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}