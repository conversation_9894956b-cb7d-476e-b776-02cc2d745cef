{"version": 3, "file": "no-confusing-non-null-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-confusing-non-null-assertion.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAE3E,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,gEAAgE;YAClE,WAAW,EAAE,QAAQ;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,cAAc,EACZ,8HAA8H;YAChI,eAAe,EACb,4HAA4H;YAC9H,kBAAkB,EAAE,mDAAmD;YACvE,eAAe,EACb,6DAA6D;YAC/D,UAAU,EACR,6EAA6E;SAChF;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO;YACL,wCAAwC,CACtC,IAA+D;gBAE/D,SAAS,2BAA2B,CAClC,IAAsD;oBAEtD,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBAC1D,CAAC;gBAED,IACE,IAAI,CAAC,QAAQ,KAAK,IAAI;oBACtB,IAAI,CAAC,QAAQ,KAAK,KAAK;oBACvB,IAAI,CAAC,QAAQ,KAAK,GAAG,EACrB;oBACA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,KAAK,GAAG,CAAC;oBACvC,MAAM,kBAAkB,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC9D,MAAM,cAAc,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAC3D,IACE,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,IAAI,MAAK,uBAAe,CAAC,UAAU;wBACvD,CAAA,kBAAkB,aAAlB,kBAAkB,uBAAlB,kBAAkB,CAAE,KAAK,MAAK,GAAG;wBACjC,CAAA,cAAc,aAAd,cAAc,uBAAd,cAAc,CAAE,KAAK,MAAK,GAAG,EAC7B;wBACA,IAAI,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;4BAC1C,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB;gCAC1D,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,QAAQ;4CACjB,CAAC,CAAC,iBAAiB;4CACnB,CAAC,CAAC,oBAAoB;wCACxB,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;4CAClC,KAAK,CAAC,MAAM,CAAC,kBAAkB,CAAC;yCACjC;qCACF;iCACF;6BACF,CAAC,CAAC;yBACJ;6BAAM;4BACL,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,QAAQ,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,gBAAgB;gCAC1D,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,YAAY;wCACvB,GAAG,EAAE,CAAC,KAAK,EAAsB,EAAE,CAAC;4CAClC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;4CACtC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC;yCACtC;qCACF;iCACF;6BACF,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}