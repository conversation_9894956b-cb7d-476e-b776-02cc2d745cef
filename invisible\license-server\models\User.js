const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true
    },
    password: {
        type: String,
        required: true,
        minlength: 6
    },
    name: {
        type: String,
        required: true,
        trim: true
    },
    isActive: {
        type: Boolean,
        default: true
    },
    totalPurchases: {
        type: Number,
        default: 0
    },
    registrationDate: {
        type: Date,
        default: Date.now
    },
    lastLogin: {
        type: Date,
        default: Date.now
    },
    // 🔐 SECURITY FIELDS
    hardwareId: {
        type: String,
        required: false // Will be set on first license activation
    },
    ipAddress: {
        type: String,
        required: false
    },
    userAgent: {
        type: String,
        required: false
    },
    // 📊 ANALYTICS
    totalScreenshotsUsed: {
        type: Number,
        default: 0
    },
    averageUsagePerMonth: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true
});

// 🔒 PASSWORD HASHING
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    
    try {
        const salt = await bcrypt.genSalt(12);
        this.password = await bcrypt.hash(this.password, salt);
        next();
    } catch (error) {
        next(error);
    }
});

// 🔑 PASSWORD COMPARISON
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

// 📊 USER STATS
userSchema.methods.getStats = function() {
    return {
        totalPurchases: this.totalPurchases,
        totalScreenshotsUsed: this.totalScreenshotsUsed,
        averageUsagePerMonth: this.averageUsagePerMonth,
        memberSince: this.registrationDate,
        lastActive: this.lastLogin
    };
};

// 🎯 GET USER TIER
userSchema.methods.getCurrentTier = function() {
    return this.totalPurchases === 0 ? 1 : 2; // First purchase = Tier 1, Renewals = Tier 2
};

module.exports = mongoose.model('User', userSchema);
