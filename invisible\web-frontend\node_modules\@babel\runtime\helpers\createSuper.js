var getPrototypeOf = require("./getPrototypeOf.js");
var isNativeReflectConstruct = require("./isNativeReflectConstruct.js");
var possibleConstructorReturn = require("./possibleConstructorReturn.js");
function _createSuper(t) {
  var r = isNativeReflectConstruct();
  return function () {
    var e,
      o = getPrototypeOf(t);
    if (r) {
      var s = getPrototypeOf(this).constructor;
      e = Reflect.construct(o, arguments, s);
    } else e = o.apply(this, arguments);
    return possibleConstructorReturn(this, e);
  };
}
module.exports = _createSuper, module.exports.__esModule = true, module.exports["default"] = module.exports;