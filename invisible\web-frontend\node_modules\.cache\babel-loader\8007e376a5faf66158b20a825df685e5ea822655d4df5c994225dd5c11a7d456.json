{"ast": null, "code": "/*!\n * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)\n * Copyright 2020-present, PayPal, Inc. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport React, { createContext, useContext, useRef, useState, useEffect, useReducer } from 'react';\n\n/**\n * Enum for the SDK script resolve status,\n *\n * @enum {string}\n */\nvar SCRIPT_LOADING_STATE;\n(function (SCRIPT_LOADING_STATE) {\n  SCRIPT_LOADING_STATE[\"INITIAL\"] = \"initial\";\n  SCRIPT_LOADING_STATE[\"PENDING\"] = \"pending\";\n  SCRIPT_LOADING_STATE[\"REJECTED\"] = \"rejected\";\n  SCRIPT_LOADING_STATE[\"RESOLVED\"] = \"resolved\";\n})(SCRIPT_LOADING_STATE || (SCRIPT_LOADING_STATE = {}));\n/**\n * Enum for the PayPalScriptProvider context dispatch actions\n *\n * @enum {string}\n */\nvar DISPATCH_ACTION;\n(function (DISPATCH_ACTION) {\n  DISPATCH_ACTION[\"LOADING_STATUS\"] = \"setLoadingStatus\";\n  DISPATCH_ACTION[\"RESET_OPTIONS\"] = \"resetOptions\";\n  DISPATCH_ACTION[\"SET_BRAINTREE_INSTANCE\"] = \"braintreeInstance\";\n})(DISPATCH_ACTION || (DISPATCH_ACTION = {}));\n/**\n * Enum for all the available hosted fields\n *\n * @enum {string}\n */\nvar PAYPAL_HOSTED_FIELDS_TYPES;\n(function (PAYPAL_HOSTED_FIELDS_TYPES) {\n  PAYPAL_HOSTED_FIELDS_TYPES[\"NUMBER\"] = \"number\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"CVV\"] = \"cvv\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_DATE\"] = \"expirationDate\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_MONTH\"] = \"expirationMonth\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_YEAR\"] = \"expirationYear\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"POSTAL_CODE\"] = \"postalCode\";\n})(PAYPAL_HOSTED_FIELDS_TYPES || (PAYPAL_HOSTED_FIELDS_TYPES = {}));\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest$1(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*********************************************\n * Common reference to the script identifier *\n *********************************************/\n// keep this script id value in kebab-case format\nvar SCRIPT_ID = \"data-react-paypal-script-id\";\nvar SDK_SETTINGS = {\n  DATA_CLIENT_TOKEN: \"dataClientToken\",\n  DATA_JS_SDK_LIBRARY: \"dataJsSdkLibrary\",\n  DATA_LIBRARY_VALUE: \"react-paypal-js\",\n  DATA_NAMESPACE: \"dataNamespace\",\n  DATA_SDK_INTEGRATION_SOURCE: \"dataSdkIntegrationSource\",\n  DATA_USER_ID_TOKEN: \"dataUserIdToken\"\n};\nvar LOAD_SCRIPT_ERROR = \"Failed to load the PayPal JS SDK script.\";\n/****************************\n * Braintree error messages *\n ****************************/\nvar EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = \"Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.\";\nvar braintreeVersion = \"3.117.0\";\nvar BRAINTREE_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/client.min.js\");\nvar BRAINTREE_PAYPAL_CHECKOUT_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/paypal-checkout.min.js\");\n/*********************\n * PayPal namespaces *\n *********************/\nvar DEFAULT_PAYPAL_NAMESPACE = \"paypal\";\nvar DEFAULT_BRAINTREE_NAMESPACE = \"braintree\";\n/*****************\n * Hosted Fields *\n *****************/\nvar HOSTED_FIELDS_CHILDREN_ERROR = \"To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes\";\nvar HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate HostedFields as children\";\n/*******************\n * Script Provider *\n *******************/\nvar SCRIPT_PROVIDER_REDUCER_ERROR = \"usePayPalScriptReducer must be used within a PayPalScriptProvider\";\nvar CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate CardFields as children\";\nvar CARD_FIELDS_CONTEXT_ERROR = \"Individual CardFields must be rendered inside the PayPalCardFieldsProvider\";\n\n/**\n * Get the namespace from the window in the browser\n * this is useful to get the paypal object from window\n * after load PayPal SDK script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getPayPalWindowNamespace$1(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_PAYPAL_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Get a namespace from the window in the browser\n * this is useful to get the braintree from window\n * after load Braintree script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getBraintreeWindowNamespace(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_BRAINTREE_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Creates a string hash code based on the string argument\n *\n * @param str the source input string to hash\n * @returns string hash code\n */\nfunction hashStr(str) {\n  var hash = \"\";\n  for (var i = 0; i < str.length; i++) {\n    var total = str[i].charCodeAt(0) * i;\n    if (str[i + 1]) {\n      total += str[i + 1].charCodeAt(0) * (i - 1);\n    }\n    hash += String.fromCharCode(97 + Math.abs(total) % 26);\n  }\n  return hash;\n}\nfunction generateErrorMessage(_a) {\n  var reactComponentName = _a.reactComponentName,\n    sdkComponentKey = _a.sdkComponentKey,\n    _b = _a.sdkRequestedComponents,\n    sdkRequestedComponents = _b === void 0 ? \"\" : _b,\n    _c = _a.sdkDataNamespace,\n    sdkDataNamespace = _c === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _c;\n  var requiredOptionCapitalized = sdkComponentKey.charAt(0).toUpperCase().concat(sdkComponentKey.substring(1));\n  var errorMessage = \"Unable to render <\".concat(reactComponentName, \" /> because window.\").concat(sdkDataNamespace, \".\").concat(requiredOptionCapitalized, \" is undefined.\");\n  // The JS SDK only loads the buttons component by default.\n  // All other components like messages and marks must be requested using the \"components\" query parameter\n  var requestedComponents = typeof sdkRequestedComponents === \"string\" ? sdkRequestedComponents : sdkRequestedComponents.join(\",\");\n  if (!requestedComponents.includes(sdkComponentKey)) {\n    var expectedComponents = [requestedComponents, sdkComponentKey].filter(Boolean).join();\n    errorMessage += \"\\nTo fix the issue, add '\".concat(sdkComponentKey, \"' to the list of components passed to the parent PayPalScriptProvider:\") + \"\\n`<PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>`.\");\n  }\n  return errorMessage;\n}\n\n/**\n * Generate a new random identifier for react-paypal-js\n *\n * @returns the {@code string} containing the random library name\n */\nfunction getScriptID(options) {\n  // exclude the data-react-paypal-script-id value from the options hash\n  var _a = options,\n    _b = SCRIPT_ID;\n  _a[_b];\n  var paypalScriptOptions = __rest$1(_a, [_b + \"\"]);\n  return \"react-paypal-js-\".concat(hashStr(JSON.stringify(paypalScriptOptions)));\n}\n/**\n * Destroy the PayPal SDK from the document page\n *\n * @param reactPayPalScriptID the script identifier\n */\nfunction destroySDKScript(reactPayPalScriptID) {\n  var scriptNode = self.document.querySelector(\"script[\".concat(SCRIPT_ID, \"=\\\"\").concat(reactPayPalScriptID, \"\\\"]\"));\n  if (scriptNode === null || scriptNode === void 0 ? void 0 : scriptNode.parentNode) {\n    scriptNode.parentNode.removeChild(scriptNode);\n  }\n}\n/**\n * Reducer function to handle complex state changes on the context\n *\n * @param state  the current state on the context object\n * @param action the action to be executed on the previous state\n * @returns a the same state if the action wasn't found, or a new state otherwise\n */\nfunction scriptReducer(state, action) {\n  var _a, _b;\n  switch (action.type) {\n    case DISPATCH_ACTION.LOADING_STATUS:\n      if (typeof action.value === \"object\") {\n        return __assign(__assign({}, state), {\n          loadingStatus: action.value.state,\n          loadingStatusErrorMessage: action.value.message\n        });\n      }\n      return __assign(__assign({}, state), {\n        loadingStatus: action.value\n      });\n    case DISPATCH_ACTION.RESET_OPTIONS:\n      // destroy existing script to make sure only one script loads at a time\n      destroySDKScript(state.options[SCRIPT_ID]);\n      return __assign(__assign({}, state), {\n        loadingStatus: SCRIPT_LOADING_STATE.PENDING,\n        options: __assign(__assign((_a = {}, _a[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _a), action.value), (_b = {}, _b[SCRIPT_ID] = \"\".concat(getScriptID(action.value)), _b))\n      });\n    case DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:\n      return __assign(__assign({}, state), {\n        braintreePayPalCheckoutInstance: action.value\n      });\n    default:\n      {\n        return state;\n      }\n  }\n}\n// Create the React context to use in the script provider component\nvar ScriptContext = createContext(null);\n\n/**\n * Check if the context is valid and ready to dispatch actions.\n *\n * @param scriptContext the result of connecting to the context provider\n * @returns strict context avoiding null values in the type\n */\nfunction validateReducer(scriptContext) {\n  if (typeof (scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.dispatch) === \"function\" && scriptContext.dispatch.length !== 0) {\n    return scriptContext;\n  }\n  throw new Error(SCRIPT_PROVIDER_REDUCER_ERROR);\n}\n/**\n * Check if the dataClientToken or the dataUserIdToken are\n * set in the options of the context.\n * @type dataClientToken is use to pass a client token\n * @type dataUserIdToken is use to pass a client tokenization key\n *\n * @param scriptContext the result of connecting to the context provider\n * @throws an {@link Error} if both dataClientToken and the dataUserIdToken keys are null or undefined\n * @returns strict context if one of the keys are defined\n */\nvar validateBraintreeAuthorizationData = function (scriptContext) {\n  var _a, _b;\n  if (!((_a = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _a === void 0 ? void 0 : _a[SDK_SETTINGS.DATA_CLIENT_TOKEN]) && !((_b = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _b === void 0 ? void 0 : _b[SDK_SETTINGS.DATA_USER_ID_TOKEN])) {\n    throw new Error(EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE);\n  }\n  return scriptContext;\n};\n\n/**\n * Custom hook to get access to the Script context and\n * dispatch actions to modify the state on the {@link ScriptProvider} component\n *\n * @returns a tuple containing the state of the context and\n * a dispatch function to modify the state\n */\nfunction usePayPalScriptReducer() {\n  var scriptContext = validateReducer(useContext(ScriptContext));\n  var derivedStatusContext = __assign(__assign({}, scriptContext), {\n    isInitial: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.INITIAL,\n    isPending: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.PENDING,\n    isResolved: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.RESOLVED,\n    isRejected: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.REJECTED\n  });\n  return [derivedStatusContext, scriptContext.dispatch];\n}\n/**\n * Custom hook to get access to the ScriptProvider context\n *\n * @returns the latest state of the context\n */\nfunction useScriptProviderContext() {\n  var scriptContext = validateBraintreeAuthorizationData(validateReducer(useContext(ScriptContext)));\n  return [scriptContext, scriptContext.dispatch];\n}\n\n// Create the React context to use in the PayPal hosted fields provider\nvar PayPalHostedFieldsContext = createContext({});\n\n/**\n * Custom hook to get access to the PayPal Hosted Fields instance.\n * The instance represent the returned object after the render process\n * With this object a user can submit the fields and dynamically modify the cards\n *\n * @returns the hosted fields instance if is available in the component\n */\nfunction usePayPalHostedFields() {\n  return useContext(PayPalHostedFieldsContext);\n}\nfunction useProxyProps(props) {\n  var proxyRef = useRef(new Proxy({}, {\n    get: function (target, prop, receiver) {\n      /**\n       *\n       * If target[prop] is a function, return a function that accesses\n       * this function off the target object. We can mutate the target with\n       * new copies of this function without having to re-render the\n       * SDK components to pass new callbacks.\n       *\n       * */\n      if (typeof target[prop] === \"function\") {\n        return function () {\n          var args = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n          }\n          // eslint-disable-next-line @typescript-eslint/ban-types\n          return target[prop].apply(target, args);\n        };\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  }));\n  proxyRef.current = Object.assign(proxyRef.current, props);\n  return proxyRef.current;\n}\n\n/**\nThis `<PayPalButtons />` component supports rendering [buttons](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/#buttons) for PayPal, Venmo, and alternative payment methods.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalButtons = function (_a) {\n  var _b;\n  var _c = _a.className,\n    className = _c === void 0 ? \"\" : _c,\n    _d = _a.disabled,\n    disabled = _d === void 0 ? false : _d,\n    children = _a.children,\n    _e = _a.forceReRender,\n    forceReRender = _e === void 0 ? [] : _e,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\"]);\n  var isDisabledStyle = disabled ? {\n    opacity: 0.38\n  } : {};\n  var classNames = \"\".concat(className, \" \").concat(disabled ? \"paypal-buttons-disabled\" : \"\").trim();\n  var buttonsContainerRef = useRef(null);\n  var buttons = useRef(null);\n  var proxyProps = useProxyProps(buttonProps);\n  var _f = usePayPalScriptReducer()[0],\n    isResolved = _f.isResolved,\n    options = _f.options;\n  var _g = useState(null),\n    initActions = _g[0],\n    setInitActions = _g[1];\n  var _h = useState(true),\n    isEligible = _h[0],\n    setIsEligible = _h[1];\n  var _j = useState(null),\n    setErrorState = _j[1];\n  function closeButtonsComponent() {\n    if (buttons.current !== null) {\n      buttons.current.close().catch(function () {\n        // ignore errors when closing the component\n      });\n    }\n  }\n  if ((_b = buttons.current) === null || _b === void 0 ? void 0 : _b.updateProps) {\n    buttons.current.updateProps({\n      message: buttonProps.message\n    });\n  }\n  // useEffect hook for rendering the buttons\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return closeButtonsComponent;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options.dataNamespace);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Buttons === undefined) {\n      setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalButtons.displayName,\n          sdkComponentKey: \"buttons\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n      return closeButtonsComponent;\n    }\n    var decoratedOnInit = function (data, actions) {\n      setInitActions(actions);\n      if (typeof buttonProps.onInit === \"function\") {\n        buttonProps.onInit(data, actions);\n      }\n    };\n    try {\n      buttons.current = paypalWindowNamespace.Buttons(__assign(__assign({}, proxyProps), {\n        onInit: decoratedOnInit\n      }));\n    } catch (err) {\n      return setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. Failed to initialize:  \".concat(err));\n      });\n    }\n    // only render the button when eligible\n    if (buttons.current.isEligible() === false) {\n      setIsEligible(false);\n      return closeButtonsComponent;\n    }\n    if (!buttonsContainerRef.current) {\n      return closeButtonsComponent;\n    }\n    buttons.current.render(buttonsContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (buttonsContainerRef.current === null || buttonsContainerRef.current.children.length === 0) {\n        // paypal buttons container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal buttons container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. \".concat(err));\n      });\n    });\n    return closeButtonsComponent;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray(__spreadArray([isResolved], forceReRender, true), [buttonProps.fundingSource], false));\n  // useEffect hook for managing disabled state\n  useEffect(function () {\n    if (initActions === null) {\n      return;\n    }\n    if (disabled === true) {\n      initActions.disable().catch(function () {\n        // ignore errors when disabling the component\n      });\n    } else {\n      initActions.enable().catch(function () {\n        // ignore errors when enabling the component\n      });\n    }\n  }, [disabled, initActions]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: buttonsContainerRef,\n    style: isDisabledStyle,\n    className: classNames\n  }) : children);\n};\nPayPalButtons.displayName = \"PayPalButtons\";\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction findScript(url, attributes) {\n  var currentScript = document.querySelector(\"script[src=\\\"\".concat(url, \"\\\"]\"));\n  if (currentScript === null) return null;\n  var nextScript = createScriptElement(url, attributes);\n  var currentScriptClone = currentScript.cloneNode();\n  delete currentScriptClone.dataset.uidAuto;\n  if (Object.keys(currentScriptClone.dataset).length !== Object.keys(nextScript.dataset).length) {\n    return null;\n  }\n  var isExactMatch = true;\n  Object.keys(currentScriptClone.dataset).forEach(function (key) {\n    if (currentScriptClone.dataset[key] !== nextScript.dataset[key]) {\n      isExactMatch = false;\n    }\n  });\n  return isExactMatch ? currentScript : null;\n}\nfunction insertScriptElement(_a) {\n  var url = _a.url,\n    attributes = _a.attributes,\n    onSuccess = _a.onSuccess,\n    onError = _a.onError;\n  var newScript = createScriptElement(url, attributes);\n  newScript.onerror = onError;\n  newScript.onload = onSuccess;\n  document.head.insertBefore(newScript, document.head.firstElementChild);\n}\nfunction processOptions(_a) {\n  var customSdkBaseUrl = _a.sdkBaseUrl,\n    environment = _a.environment,\n    options = __rest(_a, [\"sdkBaseUrl\", \"environment\"]);\n  var sdkBaseUrl = customSdkBaseUrl || processSdkBaseUrl(environment);\n  var optionsWithStringIndex = options;\n  var _b = Object.keys(optionsWithStringIndex).filter(function (key) {\n      return typeof optionsWithStringIndex[key] !== \"undefined\" && optionsWithStringIndex[key] !== null && optionsWithStringIndex[key] !== \"\";\n    }).reduce(function (accumulator, key) {\n      var value = optionsWithStringIndex[key].toString();\n      key = camelCaseToKebabCase(key);\n      if (key.substring(0, 4) === \"data\" || key === \"crossorigin\") {\n        accumulator.attributes[key] = value;\n      } else {\n        accumulator.queryParams[key] = value;\n      }\n      return accumulator;\n    }, {\n      queryParams: {},\n      attributes: {}\n    }),\n    queryParams = _b.queryParams,\n    attributes = _b.attributes;\n  if (queryParams[\"merchant-id\"] && queryParams[\"merchant-id\"].indexOf(\",\") !== -1) {\n    attributes[\"data-merchant-id\"] = queryParams[\"merchant-id\"];\n    queryParams[\"merchant-id\"] = \"*\";\n  }\n  return {\n    url: \"\".concat(sdkBaseUrl, \"?\").concat(objectToQueryString(queryParams)),\n    attributes: attributes\n  };\n}\nfunction camelCaseToKebabCase(str) {\n  var replacer = function (match, indexOfMatch) {\n    return (indexOfMatch ? \"-\" : \"\") + match.toLowerCase();\n  };\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, replacer);\n}\nfunction objectToQueryString(params) {\n  var queryString = \"\";\n  Object.keys(params).forEach(function (key) {\n    if (queryString.length !== 0) queryString += \"&\";\n    queryString += key + \"=\" + params[key];\n  });\n  return queryString;\n}\nfunction processSdkBaseUrl(environment) {\n  return environment === \"sandbox\" ? \"https://www.sandbox.paypal.com/sdk/js\" : \"https://www.paypal.com/sdk/js\";\n}\nfunction createScriptElement(url, attributes) {\n  if (attributes === void 0) {\n    attributes = {};\n  }\n  var newScript = document.createElement(\"script\");\n  newScript.src = url;\n  Object.keys(attributes).forEach(function (key) {\n    newScript.setAttribute(key, attributes[key]);\n    if (key === \"data-csp-nonce\") {\n      newScript.setAttribute(\"nonce\", attributes[\"data-csp-nonce\"]);\n    }\n  });\n  return newScript;\n}\nfunction loadScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  if (typeof document === \"undefined\") return PromisePonyfill.resolve(null);\n  var _a = processOptions(options),\n    url = _a.url,\n    attributes = _a.attributes;\n  var namespace = attributes[\"data-namespace\"] || \"paypal\";\n  var existingWindowNamespace = getPayPalWindowNamespace(namespace);\n  if (!attributes[\"data-js-sdk-library\"]) {\n    attributes[\"data-js-sdk-library\"] = \"paypal-js\";\n  }\n  if (findScript(url, attributes) && existingWindowNamespace) {\n    return PromisePonyfill.resolve(existingWindowNamespace);\n  }\n  return loadCustomScript({\n    url: url,\n    attributes: attributes\n  }, PromisePonyfill).then(function () {\n    var newWindowNamespace = getPayPalWindowNamespace(namespace);\n    if (newWindowNamespace) {\n      return newWindowNamespace;\n    }\n    throw new Error(\"The window.\".concat(namespace, \" global variable is not available.\"));\n  });\n}\nfunction loadCustomScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  var url = options.url,\n    attributes = options.attributes;\n  if (typeof url !== \"string\" || url.length === 0) {\n    throw new Error(\"Invalid url.\");\n  }\n  if (typeof attributes !== \"undefined\" && typeof attributes !== \"object\") {\n    throw new Error(\"Expected attributes to be an object.\");\n  }\n  return new PromisePonyfill(function (resolve, reject) {\n    if (typeof document === \"undefined\") return resolve();\n    insertScriptElement({\n      url: url,\n      attributes: attributes,\n      onSuccess: function () {\n        return resolve();\n      },\n      onError: function () {\n        var defaultError = new Error(\"The script \\\"\".concat(url, \"\\\" failed to load. Check the HTTP status code and response body in DevTools to learn more.\"));\n        return reject(defaultError);\n      }\n    });\n  });\n}\nfunction getPayPalWindowNamespace(namespace) {\n  return window[namespace];\n}\nfunction validateArguments(options, PromisePonyfill) {\n  if (typeof options !== \"object\" || options === null) {\n    throw new Error(\"Expected an options object.\");\n  }\n  var environment = options.environment;\n  if (environment && environment !== \"production\" && environment !== \"sandbox\") {\n    throw new Error('The `environment` option must be either \"production\" or \"sandbox\".');\n  }\n  if (typeof PromisePonyfill !== \"undefined\" && typeof PromisePonyfill !== \"function\") {\n    throw new Error(\"Expected PromisePonyfill to be a function.\");\n  }\n}\n\n/**\n * Simple check to determine if the Braintree is a valid namespace.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns a boolean representing if the namespace is valid.\n */\nvar isValidBraintreeNamespace = function (braintreeSource) {\n  var _a, _b;\n  if (typeof ((_a = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.client) === null || _a === void 0 ? void 0 : _a.create) !== \"function\" && typeof ((_b = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.paypalCheckout) === null || _b === void 0 ? void 0 : _b.create) !== \"function\") {\n    throw new Error(\"The braintreeNamespace property is not a valid BraintreeNamespace type.\");\n  }\n  return true;\n};\n/**\n * Use `actions.braintree` to provide an interface for the paypalCheckoutInstance\n * through the createOrder, createBillingAgreement and onApprove callbacks\n *\n * @param braintreeButtonProps the component button options\n * @returns a new copy of the component button options casted as {@link PayPalButtonsComponentProps}\n */\nvar decorateActions = function (buttonProps, payPalCheckoutInstance) {\n  var createOrderRef = buttonProps.createOrder;\n  var createBillingAgreementRef = buttonProps.createBillingAgreement;\n  var onApproveRef = buttonProps.onApprove;\n  if (typeof createOrderRef === \"function\") {\n    buttonProps.createOrder = function (data, actions) {\n      return createOrderRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof createBillingAgreementRef === \"function\") {\n    buttonProps.createBillingAgreement = function (data, actions) {\n      return createBillingAgreementRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof onApproveRef === \"function\") {\n    buttonProps.onApprove = function (data, actions) {\n      return onApproveRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  return __assign({}, buttonProps);\n};\n/**\n * Get the Braintree namespace from the component props.\n * If the prop `braintreeNamespace` is undefined will try to load it from the CDN.\n * This function allows users to set the braintree manually on the `BraintreePayPalButtons` component.\n *\n * Use case can be for example legacy sites using AMD/UMD modules,\n * trying to integrate the `BraintreePayPalButtons` component.\n * If we attempt to load the Braintree from the CDN won't define the braintree namespace.\n * This happens because the braintree script is an UMD module.\n * After detecting the AMD on the global scope will create an anonymous module using `define`\n * and the `BraintreePayPalButtons` won't be able to get access to the `window.braintree` namespace\n * from the global context.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns the {@link BraintreeNamespace}\n */\nvar getBraintreeNamespace = function (braintreeSource) {\n  if (braintreeSource && isValidBraintreeNamespace(braintreeSource)) {\n    return Promise.resolve(braintreeSource);\n  }\n  return Promise.all([loadCustomScript({\n    url: BRAINTREE_SOURCE\n  }), loadCustomScript({\n    url: BRAINTREE_PAYPAL_CHECKOUT_SOURCE\n  })]).then(function () {\n    return getBraintreeWindowNamespace();\n  });\n};\n\n/**\nThis `<BraintreePayPalButtons />` component renders the [Braintree PayPal Buttons](https://developer.paypal.com/braintree/docs/guides/paypal/overview) for Braintree Merchants.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nNote: You are able to make your integration using the client token or using the tokenization key.\n\n- To use the client token integration set the key `dataClientToken` in the `PayPayScriptProvider` component's options.\n- To use the tokenization key integration set the key `dataUserIdToken` in the `PayPayScriptProvider` component's options.\n*/\nvar BraintreePayPalButtons = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.disabled,\n    disabled = _c === void 0 ? false : _c,\n    children = _a.children,\n    _d = _a.forceReRender,\n    forceReRender = _d === void 0 ? [] : _d,\n    braintreeNamespace = _a.braintreeNamespace,\n    merchantAccountId = _a.merchantAccountId,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\", \"braintreeNamespace\", \"merchantAccountId\"]);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var _f = useScriptProviderContext(),\n    providerContext = _f[0],\n    dispatch = _f[1];\n  useEffect(function () {\n    getBraintreeNamespace(braintreeNamespace).then(function (braintree) {\n      var clientTokenizationKey = providerContext.options[SDK_SETTINGS.DATA_USER_ID_TOKEN];\n      var clientToken = providerContext.options[SDK_SETTINGS.DATA_CLIENT_TOKEN];\n      return braintree.client.create({\n        authorization: clientTokenizationKey || clientToken\n      }).then(function (clientInstance) {\n        var merchantProp = merchantAccountId ? {\n          merchantAccountId: merchantAccountId\n        } : {};\n        return braintree.paypalCheckout.create(__assign(__assign({}, merchantProp), {\n          client: clientInstance\n        }));\n      }).then(function (paypalCheckoutInstance) {\n        dispatch({\n          type: DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,\n          value: paypalCheckoutInstance\n        });\n      });\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [providerContext.options]);\n  return React.createElement(React.Fragment, null, providerContext.braintreePayPalCheckoutInstance && React.createElement(PayPalButtons, __assign({\n    className: className,\n    disabled: disabled,\n    forceReRender: forceReRender\n  }, decorateActions(buttonProps, providerContext.braintreePayPalCheckoutInstance)), children));\n};\n\n/**\nThe `<PayPalMarks />` component is used for conditionally rendering different payment options using radio buttons.\nThe [Display PayPal Buttons with other Payment Methods guide](https://developer.paypal.com/docs/business/checkout/add-capabilities/buyer-experience/#display-paypal-buttons-with-other-payment-methods) describes this style of integration in detail.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nThis component can also be configured to use a single funding source similar to the [standalone buttons](https://developer.paypal.com/docs/business/checkout/configure-payments/standalone-buttons/) approach.\nA `FUNDING` object is exported by this library which has a key for every available funding source option.\n*/\nvar PayPalMarks = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    children = _a.children,\n    markProps = __rest$1(_a, [\"className\", \"children\"]);\n  var _c = usePayPalScriptReducer()[0],\n    isResolved = _c.isResolved,\n    options = _c.options;\n  var markContainerRef = useRef(null);\n  var _d = useState(true),\n    isEligible = _d[0],\n    setIsEligible = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  /**\n   * Render PayPal Mark into the DOM\n   */\n  var renderPayPalMark = function (mark) {\n    var current = markContainerRef.current;\n    // only render the mark when eligible\n    if (!current || !mark.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Remove any children before render it again\n    if (current.firstChild) {\n      current.removeChild(current.firstChild);\n    }\n    mark.render(current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (current === null || current.children.length === 0) {\n        // paypal marks container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal marks container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMarks /> component. \".concat(err));\n      });\n    });\n  };\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Marks === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMarks.displayName,\n          sdkComponentKey: \"marks\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    renderPayPalMark(paypalWindowNamespace.Marks(__assign({}, markProps)));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isResolved, markProps.fundingSource]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: markContainerRef,\n    className: className\n  }) : children);\n};\nPayPalMarks.displayName = \"PayPalMarks\";\n\n/**\nThis `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalMessages = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.forceReRender,\n    forceReRender = _c === void 0 ? [] : _c,\n    messageProps = __rest$1(_a, [\"className\", \"forceReRender\"]);\n  var _d = usePayPalScriptReducer()[0],\n    isResolved = _d.isResolved,\n    options = _d.options;\n  var messagesContainerRef = useRef(null);\n  var messages = useRef(null);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Messages === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMessages.displayName,\n          sdkComponentKey: \"messages\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    messages.current = paypalWindowNamespace.Messages(__assign({}, messageProps));\n    messages.current.render(messagesContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (messagesContainerRef.current === null || messagesContainerRef.current.children.length === 0) {\n        // paypal messages container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal messages container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMessages /> component. \".concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray([isResolved], forceReRender, true));\n  return React.createElement(\"div\", {\n    ref: messagesContainerRef,\n    className: className\n  });\n};\nPayPalMessages.displayName = \"PayPalMessages\";\n\n/**\nThis `<PayPalScriptProvider />` component takes care of loading the JS SDK `<script>`.\nIt manages state for script loading so children components like `<PayPalButtons />` know when it's safe to use the `window.paypal` global namespace.\n\nNote: You always should use this component as a wrapper for  `PayPalButtons`, `PayPalMarks`, `PayPalMessages` and `BraintreePayPalButtons` components.\n */\nvar PayPalScriptProvider = function (_a) {\n  var _b;\n  var _c = _a.options,\n    options = _c === void 0 ? {\n      clientId: \"test\"\n    } : _c,\n    children = _a.children,\n    _d = _a.deferLoading,\n    deferLoading = _d === void 0 ? false : _d;\n  var _e = useReducer(scriptReducer, {\n      options: __assign(__assign({}, options), (_b = {}, _b[SDK_SETTINGS.DATA_JS_SDK_LIBRARY] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SCRIPT_ID] = \"\".concat(getScriptID(options)), _b)),\n      loadingStatus: deferLoading ? SCRIPT_LOADING_STATE.INITIAL : SCRIPT_LOADING_STATE.PENDING\n    }),\n    state = _e[0],\n    dispatch = _e[1];\n  useEffect(function () {\n    if (deferLoading === false && state.loadingStatus === SCRIPT_LOADING_STATE.INITIAL) {\n      return dispatch({\n        type: DISPATCH_ACTION.LOADING_STATUS,\n        value: SCRIPT_LOADING_STATE.PENDING\n      });\n    }\n    if (state.loadingStatus !== SCRIPT_LOADING_STATE.PENDING) {\n      return;\n    }\n    var isSubscribed = true;\n    loadScript(state.options).then(function () {\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: SCRIPT_LOADING_STATE.RESOLVED\n        });\n      }\n    }).catch(function (err) {\n      console.error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: {\n            state: SCRIPT_LOADING_STATE.REJECTED,\n            message: String(err)\n          }\n        });\n      }\n    });\n    return function () {\n      isSubscribed = false;\n    };\n  }, [state.options, deferLoading, state.loadingStatus]);\n  return React.createElement(ScriptContext.Provider, {\n    value: __assign(__assign({}, state), {\n      dispatch: dispatch\n    })\n  }, children);\n};\n\n/**\n * Custom hook to store registered hosted fields children\n * Each `PayPalHostedField` component should be registered on the parent provider\n *\n * @param initialValue the initially registered components\n * @returns at first, an {@link Object} containing the registered hosted fields,\n * and at the second a function handler to register the hosted fields components\n */\nvar useHostedFieldsRegister = function (initialValue) {\n  if (initialValue === void 0) {\n    initialValue = {};\n  }\n  var registeredFields = useRef(initialValue);\n  var registerHostedField = function (component) {\n    registeredFields.current = __assign(__assign({}, registeredFields.current), component);\n  };\n  return [registeredFields, registerHostedField];\n};\n\n/**\n * Throw an exception if the HostedFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the hosted-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'hosted-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingHostedFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",hosted-fields\") : \"hosted-fields\";\n  var errorMessage = \"Unable to render <PayPalHostedFieldsProvider /> because window.\".concat(dataNamespace, \".HostedFields is undefined.\");\n  if (!components.includes(\"hosted-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\n/**\n * Validate the expiration date component. Valid combinations are:\n * 1- Only the `expirationDate` field exists.\n * 2- Only the `expirationMonth` and `expirationYear` fields exist. Cannot be used with the `expirationDate` field.\n *\n * @param registerTypes\n * @returns @type {true} when the children are valid\n */\nvar validateExpirationDate = function (registerTypes) {\n  return !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR);\n};\n/**\n * Check if we find the [number, expiration, cvv] in children\n *\n * @param requiredChildren the list with required children [number, expiration, cvv]\n * @param registerTypes    the list of all the children types pass to the parent\n * @throw an @type {Error} when not find the default children\n */\nvar hasDefaultChildren = function (registerTypes) {\n  if (!registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.NUMBER) || !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.CVV) || validateExpirationDate(registerTypes)) {\n    throw new Error(HOSTED_FIELDS_CHILDREN_ERROR);\n  }\n};\n/**\n * Check if we don't have duplicate children types\n *\n * @param registerTypes the list of all the children types pass to the parent\n * @throw an @type {Error} when duplicate types was found\n */\nvar noDuplicateChildren = function (registerTypes) {\n  if (registerTypes.length !== new Set(registerTypes).size) {\n    throw new Error(HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR);\n  }\n};\n/**\n * Validate the hosted field children in the PayPalHostedFieldsProvider component.\n * These are the rules:\n * 1- We need to find 3 default children for number, expiration, cvv\n * 2- No duplicate children are allowed\n * 3- No invalid combinations of `expirationDate`, `expirationMonth`, and `expirationYear`\n *\n * @param childrenList     the list of children\n * @param requiredChildren the list with required children [number, expiration, cvv]\n */\nvar validateHostedFieldChildren = function (registeredFields) {\n  hasDefaultChildren(registeredFields);\n  noDuplicateChildren(registeredFields);\n};\n\n/**\nThis `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.\n\nThis provider component is designed to be used with the `<PayPalHostedField />` component.\n\nWarning: If you don't see anything in the screen probably your client is ineligible.\nTo handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.\nTake a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/\n*/\nvar PayPalHostedFieldsProvider = function (_a) {\n  var styles = _a.styles,\n    createOrder = _a.createOrder,\n    notEligibleError = _a.notEligibleError,\n    children = _a.children,\n    installments = _a.installments;\n  var _b = useScriptProviderContext()[0],\n    options = _b.options,\n    loadingStatus = _b.loadingStatus;\n  var _c = useState(true),\n    isEligible = _c[0],\n    setIsEligible = _c[1];\n  var _d = useState(),\n    cardFields = _d[0],\n    setCardFields = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var hostedFieldsContainerRef = useRef(null);\n  var hostedFields = useRef();\n  var _f = useHostedFieldsRegister(),\n    registeredFields = _f[0],\n    registerHostedField = _f[1];\n  useEffect(function () {\n    var _a;\n    validateHostedFieldChildren(Object.keys(registeredFields.current));\n    // Only render the hosted fields when script is loaded and hostedFields is eligible\n    if (!(loadingStatus === SCRIPT_LOADING_STATE.RESOLVED)) {\n      return;\n    }\n    // Get the hosted fields from the [window.paypal.HostedFields] SDK\n    hostedFields.current = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]).HostedFields;\n    if (!hostedFields.current) {\n      throw new Error(generateMissingHostedFieldsError((_a = {\n        components: options.components\n      }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n    }\n    if (!hostedFields.current.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Clean all the fields before the rerender\n    if (cardFields) {\n      cardFields.teardown();\n    }\n    hostedFields.current.render({\n      // Call your server to set up the transaction\n      createOrder: createOrder,\n      fields: registeredFields.current,\n      installments: installments,\n      styles: styles\n    }).then(function (cardFieldsInstance) {\n      if (hostedFieldsContainerRef.current) {\n        setCardFields(cardFieldsInstance);\n      }\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalHostedFieldsProvider /> component. \".concat(err));\n      });\n    });\n  }, [loadingStatus, styles]); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: hostedFieldsContainerRef\n  }, isEligible ? React.createElement(PayPalHostedFieldsContext.Provider, {\n    value: {\n      cardFields: cardFields,\n      registerHostedField: registerHostedField\n    }\n  }, children) : notEligibleError);\n};\n\n/**\nThis `<PayPalHostedField />` component renders individual fields for [Hosted Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nIt relies on the `<PayPalHostedFieldsProvider />` parent component for managing state related to loading the JS SDK script\nand execute some validations before the rendering the fields.\n\nTo use the PayPal hosted fields you need to define at least three fields:\n\n- A card number field\n- The CVV code from the client card\n- The expiration date\n\nYou can define the expiration date as a single field similar to the example below,\nor you are able to define it in [two separate fields](https://paypal.github.io/react-paypal-js//?path=/docs/paypal-paypalhostedfields--expiration-date). One for the month and second for year.\n\nNote: Take care when using multiple instances of the PayPal Hosted Fields on the same page.\nThe component will fail to render when any of the selectors return more than one element.\n*/\nvar PayPalHostedField = function (_a) {\n  var hostedFieldType = _a.hostedFieldType,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    options = _a.options,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    props = __rest$1(_a, [\"hostedFieldType\", \"options\"]);\n  var hostedFieldContext = useContext(PayPalHostedFieldsContext);\n  useEffect(function () {\n    var _a;\n    if (!(hostedFieldContext === null || hostedFieldContext === void 0 ? void 0 : hostedFieldContext.registerHostedField)) {\n      throw new Error(\"The HostedField cannot be register in the PayPalHostedFieldsProvider parent component\");\n    }\n    // Register in the parent provider\n    hostedFieldContext.registerHostedField((_a = {}, _a[hostedFieldType] = {\n      selector: options.selector,\n      placeholder: options.placeholder,\n      type: options.type,\n      formatInput: options.formatInput,\n      maskInput: options.maskInput,\n      select: options.select,\n      maxlength: options.maxlength,\n      minlength: options.minlength,\n      prefill: options.prefill,\n      rejectUnsupportedCards: options.rejectUnsupportedCards\n    }, _a));\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", __assign({}, props));\n};\n\n/**\n * Throw an exception if the CardFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the card-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingCardFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",card-fields\") : \"card-fields\";\n  var errorMessage = \"Unable to render <PayPalCardFieldsProvider /> because window.\".concat(dataNamespace, \".CardFields is undefined.\");\n  if (!components.includes(\"card-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\nfunction ignore() {\n  return;\n}\nfunction hasChildren(container) {\n  var _a;\n  return !!((_a = container.current) === null || _a === void 0 ? void 0 : _a.children.length);\n}\nvar PayPalCardFieldsContext = createContext({\n  cardFieldsForm: null,\n  fields: {},\n  registerField: ignore,\n  unregisterField: ignore // implementation is inside hook and passed through the provider\n});\nvar usePayPalCardFields = function () {\n  return useContext(PayPalCardFieldsContext);\n};\nvar usePayPalCardFieldsRegistry = function () {\n  var _a = useState(null),\n    setError = _a[1];\n  var registeredFields = useRef({});\n  var registerField = function () {\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    var fieldName = props[0],\n      options = props[1],\n      cardFields = props[2];\n    if (registeredFields.current[fieldName]) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_DUPLICATE_CHILDREN_ERROR);\n      });\n    }\n    registeredFields.current[fieldName] = cardFields === null || cardFields === void 0 ? void 0 : cardFields[fieldName](options);\n    return registeredFields.current[fieldName];\n  };\n  var unregisterField = function (fieldName) {\n    var field = registeredFields.current[fieldName];\n    if (field) {\n      field.close().catch(ignore);\n      delete registeredFields.current[fieldName];\n    }\n  };\n  return {\n    fields: registeredFields.current,\n    registerField: registerField,\n    unregisterField: unregisterField\n  };\n};\nvar FullWidthContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThe `<PayPalCardFieldsProvider />` is a context provider that is designed to support the rendering and state management of PayPal CardFields in your application.\n\nThe context provider will initialize the `CardFields` instance from the JS SDK and determine eligibility to render the CardField components. Once the `CardFields` are initialized, the context provider will manage the state of the `CardFields` instance as well as the reference to each individual card field.\n\nPassing the `inputEvents` and `style` props to the context provider will apply them to each of the individual field components.\n\nThe state managed by the provider is accessible through our custom hook `usePayPalCardFields`.\n\n*/\nvar PayPalCardFieldsProvider = function (_a) {\n  var children = _a.children,\n    props = __rest$1(_a, [\"children\"]);\n  var _b = usePayPalScriptReducer()[0],\n    isResolved = _b.isResolved,\n    options = _b.options;\n  var _c = usePayPalCardFieldsRegistry(),\n    fields = _c.fields,\n    registerField = _c.registerField,\n    unregisterField = _c.unregisterField;\n  var _d = useState(null),\n    cardFieldsForm = _d[0],\n    setCardFieldsForm = _d[1];\n  var cardFieldsInstance = useRef(null);\n  var _e = useState(false),\n    isEligible = _e[0],\n    setIsEligible = _e[1];\n  // We set the error inside state so that it can be caught by React's error boundary\n  var _f = useState(null),\n    setError = _f[1];\n  useEffect(function () {\n    var _a, _b, _c;\n    if (!isResolved) {\n      return;\n    }\n    try {\n      cardFieldsInstance.current = (_c = (_b = (_a = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE])).CardFields) === null || _b === void 0 ? void 0 : _b.call(_a, __assign({}, props))) !== null && _c !== void 0 ? _c : null;\n    } catch (error) {\n      setError(function () {\n        throw new Error(\"Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  \".concat(error));\n      });\n      return;\n    }\n    if (!cardFieldsInstance.current) {\n      setError(function () {\n        var _a;\n        throw new Error(generateMissingCardFieldsError((_a = {\n          components: options.components\n        }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n      });\n      return;\n    }\n    setIsEligible(cardFieldsInstance.current.isEligible());\n    setCardFieldsForm(cardFieldsInstance.current);\n    return function () {\n      setCardFieldsForm(null);\n      cardFieldsInstance.current = null;\n    };\n  }, [isResolved]); // eslint-disable-line react-hooks/exhaustive-deps\n  if (!isEligible) {\n    // TODO: What should be returned here?\n    return React.createElement(\"div\", null);\n  }\n  return React.createElement(FullWidthContainer, null, React.createElement(PayPalCardFieldsContext.Provider, {\n    value: {\n      cardFieldsForm: cardFieldsForm,\n      fields: fields,\n      registerField: registerField,\n      unregisterField: unregisterField\n    }\n  }, children));\n};\nvar PayPalCardField = function (_a) {\n  var className = _a.className,\n    fieldName = _a.fieldName,\n    options = __rest$1(_a, [\"className\", \"fieldName\"]);\n  var _b = usePayPalCardFields(),\n    cardFieldsForm = _b.cardFieldsForm,\n    registerField = _b.registerField,\n    unregisterField = _b.unregisterField;\n  var containerRef = useRef(null);\n  // Set errors is state so that they can be caught by React's error boundary\n  var _c = useState(null),\n    setError = _c[1];\n  function closeComponent() {\n    unregisterField(fieldName);\n  }\n  useEffect(function () {\n    if (!cardFieldsForm) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_CONTEXT_ERROR);\n      });\n      return closeComponent;\n    }\n    if (!containerRef.current) {\n      return closeComponent;\n    }\n    var registeredField = registerField(fieldName, options, cardFieldsForm);\n    registeredField === null || registeredField === void 0 ? void 0 : registeredField.render(containerRef.current).catch(function (err) {\n      if (!hasChildren(containerRef)) {\n        // Component no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // Component is still in the DOM\n      setError(function () {\n        throw new Error(\"Failed to render <PayPal\".concat(fieldName, \" /> component. \").concat(err));\n      });\n    });\n    return closeComponent;\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: containerRef,\n    className: className\n  });\n};\nvar PayPalNameField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NameField\"\n  }, options));\n};\nvar PayPalNumberField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NumberField\"\n  }, options));\n};\nvar PayPalExpiryField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"ExpiryField\"\n  }, options));\n};\nvar PayPalCVVField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"CVVField\"\n  }, options));\n};\nvar FlexContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      display: \"flex\",\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThis `<PayPalCardFieldsForm />` component renders the 4 individual fields for [Card Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nThis setup relies on the `<PayPalCardFieldsProvider />` parent component, which manages the state related to loading the JS SDK script and performs certain validations before rendering the fields.\n\n\n\nNote: If you want to have more granular control over the layout of how the fields are rendered, you can alternatively use our Individual Fields.\n*/\nvar PayPalCardFieldsForm = function (_a) {\n  var className = _a.className;\n  return React.createElement(\"div\", {\n    className: className\n  }, React.createElement(PayPalCardField, {\n    fieldName: \"NameField\"\n  }), React.createElement(PayPalCardField, {\n    fieldName: \"NumberField\"\n  }), React.createElement(FlexContainer, null, React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"ExpiryField\"\n  })), React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"CVVField\"\n  }))));\n};\nvar FUNDING$1 = {\n  PAYPAL: \"paypal\",\n  VENMO: \"venmo\",\n  APPLEPAY: \"applepay\",\n  ITAU: \"itau\",\n  CREDIT: \"credit\",\n  PAYLATER: \"paylater\",\n  CARD: \"card\",\n  IDEAL: \"ideal\",\n  SEPA: \"sepa\",\n  BANCONTACT: \"bancontact\",\n  GIROPAY: \"giropay\",\n  SOFORT: \"sofort\",\n  EPS: \"eps\",\n  MYBANK: \"mybank\",\n  P24: \"p24\",\n  PAYU: \"payu\",\n  BLIK: \"blik\",\n  TRUSTLY: \"trustly\",\n  OXXO: \"oxxo\",\n  BOLETO: \"boleto\",\n  BOLETOBANCARIO: \"boletobancario\",\n  WECHATPAY: \"wechatpay\",\n  MERCADOPAGO: \"mercadopago\",\n  MULTIBANCO: \"multibanco\",\n  SATISPAY: \"satispay\",\n  PAIDY: \"paidy\",\n  ZIMPLER: \"zimpler\",\n  MAXIMA: \"maxima\"\n};\n[FUNDING$1.IDEAL, FUNDING$1.BANCONTACT, FUNDING$1.GIROPAY, FUNDING$1.SOFORT, FUNDING$1.EPS, FUNDING$1.MYBANK, FUNDING$1.P24, FUNDING$1.PAYU, FUNDING$1.BLIK, FUNDING$1.TRUSTLY, FUNDING$1.OXXO, FUNDING$1.BOLETO, FUNDING$1.BOLETOBANCARIO, FUNDING$1.WECHATPAY, FUNDING$1.MERCADOPAGO, FUNDING$1.MULTIBANCO, FUNDING$1.SATISPAY, FUNDING$1.PAIDY, FUNDING$1.MAXIMA, FUNDING$1.ZIMPLER];\n\n// We do not re-export `FUNDING` from the `sdk-constants` module\n// directly because it has no type definitions.\n//\n// See https://github.com/paypal/react-paypal-js/issues/125\nvar FUNDING = FUNDING$1;\nexport { BraintreePayPalButtons, DISPATCH_ACTION, FUNDING, PAYPAL_HOSTED_FIELDS_TYPES, PayPalButtons, PayPalCVVField, PayPalCardFieldsContext, PayPalCardFieldsForm, PayPalCardFieldsProvider, PayPalExpiryField, PayPalHostedField, PayPalHostedFieldsProvider, PayPalMarks, PayPalMessages, PayPalNameField, PayPalNumberField, PayPalScriptProvider, SCRIPT_LOADING_STATE, ScriptContext, destroySDKScript, getScriptID, scriptReducer, usePayPalCardFields, usePayPalHostedFields, usePayPalScriptReducer, useScriptProviderContext };", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useRef", "useState", "useEffect", "useReducer", "SCRIPT_LOADING_STATE", "DISPATCH_ACTION", "PAYPAL_HOSTED_FIELDS_TYPES", "__assign", "Object", "assign", "t", "s", "i", "n", "arguments", "length", "p", "prototype", "hasOwnProperty", "call", "apply", "__rest$1", "e", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__spread<PERSON><PERSON>y", "to", "from", "pack", "l", "ar", "Array", "slice", "concat", "SuppressedError", "error", "suppressed", "message", "Error", "name", "SCRIPT_ID", "SDK_SETTINGS", "DATA_CLIENT_TOKEN", "DATA_JS_SDK_LIBRARY", "DATA_LIBRARY_VALUE", "DATA_NAMESPACE", "DATA_SDK_INTEGRATION_SOURCE", "DATA_USER_ID_TOKEN", "LOAD_SCRIPT_ERROR", "EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE", "braintreeVersion", "BRAINTREE_SOURCE", "BRAINTREE_PAYPAL_CHECKOUT_SOURCE", "DEFAULT_PAYPAL_NAMESPACE", "DEFAULT_BRAINTREE_NAMESPACE", "HOSTED_FIELDS_CHILDREN_ERROR", "HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR", "SCRIPT_PROVIDER_REDUCER_ERROR", "CARD_FIELDS_DUPLICATE_CHILDREN_ERROR", "CARD_FIELDS_CONTEXT_ERROR", "getPayPalWindowNamespace$1", "namespace", "window", "getBraintreeWindowNamespace", "hashStr", "str", "hash", "total", "charCodeAt", "String", "fromCharCode", "Math", "abs", "generateErrorMessage", "_a", "reactComponentName", "sdkComponentKey", "_b", "sdkRequestedComponents", "_c", "sdkDataNamespace", "requiredOptionCapitalized", "char<PERSON>t", "toUpperCase", "substring", "errorMessage", "requestedComponents", "join", "includes", "expectedComponents", "filter", "Boolean", "getScriptID", "options", "paypalScriptOptions", "JSON", "stringify", "destroySDKScript", "reactPayPalScriptID", "scriptNode", "self", "document", "querySelector", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "scriptReducer", "state", "action", "type", "LOADING_STATUS", "value", "loadingStatus", "loadingStatusErrorMessage", "RESET_OPTIONS", "PENDING", "SET_BRAINTREE_INSTANCE", "braintreePayPalCheckoutInstance", "ScriptContext", "validateReducer", "scriptContext", "dispatch", "validateBraintreeAuthorizationData", "usePayPalScriptReducer", "derivedStatusContext", "isInitial", "INITIAL", "isPending", "isResolved", "RESOLVED", "isRejected", "REJECTED", "useScriptProviderContext", "PayPalHostedFieldsContext", "usePayPalHostedFields", "useProxyProps", "props", "proxyRef", "Proxy", "get", "target", "prop", "receiver", "args", "_i", "Reflect", "current", "PayPalButtons", "className", "_d", "disabled", "children", "_e", "forceReRender", "buttonProps", "isDisabledStyle", "opacity", "classNames", "trim", "buttonsContainerRef", "buttons", "proxyProps", "_f", "_g", "initActions", "setInitActions", "_h", "isEligible", "setIsEligible", "_j", "setErrorState", "closeButtonsComponent", "close", "catch", "updateProps", "paypalWindowNamespace", "dataNamespace", "undefined", "Buttons", "displayName", "components", "decoratedOnInit", "data", "actions", "onInit", "err", "render", "fundingSource", "disable", "enable", "createElement", "Fragment", "ref", "style", "__rest", "findScript", "url", "attributes", "currentScript", "nextScript", "createScriptElement", "currentScriptClone", "cloneNode", "dataset", "uidAuto", "keys", "isExactMatch", "for<PERSON>ach", "key", "insertScriptElement", "onSuccess", "onError", "newScript", "onerror", "onload", "head", "insertBefore", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "processOptions", "customSdkBaseUrl", "sdkBaseUrl", "environment", "processSdkBaseUrl", "optionsWithStringIndex", "reduce", "accumulator", "toString", "camelCaseToKebabCase", "queryParams", "objectToQueryString", "replacer", "match", "indexOfMatch", "toLowerCase", "replace", "params", "queryString", "src", "setAttribute", "loadScript", "PromisePonyfill", "Promise", "validateArguments", "resolve", "existingWindowNamespace", "getPayPalWindowNamespace", "loadCustomScript", "then", "newWindowNamespace", "reject", "defaultError", "isValidBraintreeNamespace", "braintreeSource", "client", "create", "paypalCheckout", "decorateActions", "payPalCheckoutInstance", "createOrderRef", "createOrder", "createBillingAgreementRef", "createBillingAgreement", "onApproveRef", "onApprove", "braintree", "getBraintreeNamespace", "all", "BraintreePayPalButtons", "braintreeNamespace", "merchantAccountId", "providerContext", "clientTokenizationKey", "clientToken", "authorization", "clientInstance", "merchantProp", "paypalCheckoutInstance", "PayPalMarks", "markProps", "markContainer<PERSON>ef", "renderPayPalMark", "mark", "<PERSON><PERSON><PERSON><PERSON>", "Marks", "PayPalMessages", "messageProps", "messagesContainerRef", "messages", "Messages", "PayPalScriptProvider", "clientId", "deferLoading", "isSubscribed", "console", "Provider", "useHostedFieldsRegister", "initialValue", "registeredFields", "registerHostedField", "component", "generateMissingHostedFieldsError", "validateExpirationDate", "registerTypes", "EXPIRATION_DATE", "EXPIRATION_MONTH", "EXPIRATION_YEAR", "hasDefaultC<PERSON><PERSON>n", "NUMBER", "CVV", "noDuplicateChildren", "Set", "size", "validateHostedFieldChildren", "PayPalHostedFieldsProvider", "styles", "notEligibleError", "installments", "cardFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hostedFieldsContainerRef", "<PERSON><PERSON><PERSON><PERSON>", "HostedFields", "teardown", "fields", "cardFieldsInstance", "PayPalHostedField", "hostedFieldType", "hostedFieldContext", "selector", "placeholder", "formatInput", "maskInput", "select", "maxlength", "minlength", "prefill", "rejectUnsupportedCards", "generateMissingCardFieldsError", "ignore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "container", "PayPalCardFieldsContext", "cardFieldsForm", "registerField", "unregisterField", "usePayPalCardFields", "usePayPalCardFieldsRegistry", "setError", "fieldName", "field", "FullWidthContainer", "width", "PayPalCardFieldsProvider", "setCardFieldsForm", "<PERSON><PERSON><PERSON>s", "PayPalCardField", "containerRef", "closeComponent", "registeredField", "PayPalNameField", "PayPalNumberField", "PayPalExpiryField", "PayPalCVVField", "FlexContainer", "display", "PayPalCardFieldsForm", "FUNDING$1", "PAYPAL", "VENMO", "APPLEPAY", "ITAU", "CREDIT", "PAYLATER", "CARD", "IDEAL", "SEPA", "BANCONTACT", "GIROPAY", "SOFORT", "EPS", "MYBANK", "P24", "PAYU", "BLIK", "TRUSTLY", "OXXO", "BOLETO", "BOLETOBANCARIO", "WECHATPAY", "MERCADOPAGO", "MULTIBANCO", "SATISPAY", "PAIDY", "ZIMPLER", "MAXIMA", "FUNDING"], "sources": ["D:/electron/invisible/web-frontend/node_modules/@paypal/react-paypal-js/dist/esm/react-paypal-js.js"], "sourcesContent": ["/*!\n * react-paypal-js v8.8.3 (2025-04-11T19:50:46.506Z)\n * Copyright 2020-present, PayPal, Inc. All rights reserved.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport React, { createContext, useContext, useRef, useState, useEffect, useReducer } from 'react';\n\n/**\n * Enum for the SDK script resolve status,\n *\n * @enum {string}\n */\nvar SCRIPT_LOADING_STATE;\n(function (SCRIPT_LOADING_STATE) {\n  SCRIPT_LOADING_STATE[\"INITIAL\"] = \"initial\";\n  SCRIPT_LOADING_STATE[\"PENDING\"] = \"pending\";\n  SCRIPT_LOADING_STATE[\"REJECTED\"] = \"rejected\";\n  SCRIPT_LOADING_STATE[\"RESOLVED\"] = \"resolved\";\n})(SCRIPT_LOADING_STATE || (SCRIPT_LOADING_STATE = {}));\n/**\n * Enum for the PayPalScriptProvider context dispatch actions\n *\n * @enum {string}\n */\nvar DISPATCH_ACTION;\n(function (DISPATCH_ACTION) {\n  DISPATCH_ACTION[\"LOADING_STATUS\"] = \"setLoadingStatus\";\n  DISPATCH_ACTION[\"RESET_OPTIONS\"] = \"resetOptions\";\n  DISPATCH_ACTION[\"SET_BRAINTREE_INSTANCE\"] = \"braintreeInstance\";\n})(DISPATCH_ACTION || (DISPATCH_ACTION = {}));\n/**\n * Enum for all the available hosted fields\n *\n * @enum {string}\n */\nvar PAYPAL_HOSTED_FIELDS_TYPES;\n(function (PAYPAL_HOSTED_FIELDS_TYPES) {\n  PAYPAL_HOSTED_FIELDS_TYPES[\"NUMBER\"] = \"number\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"CVV\"] = \"cvv\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_DATE\"] = \"expirationDate\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_MONTH\"] = \"expirationMonth\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"EXPIRATION_YEAR\"] = \"expirationYear\";\n  PAYPAL_HOSTED_FIELDS_TYPES[\"POSTAL_CODE\"] = \"postalCode\";\n})(PAYPAL_HOSTED_FIELDS_TYPES || (PAYPAL_HOSTED_FIELDS_TYPES = {}));\nvar __assign = function () {\n  __assign = Object.assign || function __assign(t) {\n    for (var s, i = 1, n = arguments.length; i < n; i++) {\n      s = arguments[i];\n      for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n    }\n    return t;\n  };\n  return __assign.apply(this, arguments);\n};\nfunction __rest$1(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n    if (ar || !(i in from)) {\n      if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n      ar[i] = from[i];\n    }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\n/*********************************************\n * Common reference to the script identifier *\n *********************************************/\n// keep this script id value in kebab-case format\nvar SCRIPT_ID = \"data-react-paypal-script-id\";\nvar SDK_SETTINGS = {\n  DATA_CLIENT_TOKEN: \"dataClientToken\",\n  DATA_JS_SDK_LIBRARY: \"dataJsSdkLibrary\",\n  DATA_LIBRARY_VALUE: \"react-paypal-js\",\n  DATA_NAMESPACE: \"dataNamespace\",\n  DATA_SDK_INTEGRATION_SOURCE: \"dataSdkIntegrationSource\",\n  DATA_USER_ID_TOKEN: \"dataUserIdToken\"\n};\nvar LOAD_SCRIPT_ERROR = \"Failed to load the PayPal JS SDK script.\";\n/****************************\n * Braintree error messages *\n ****************************/\nvar EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE = \"Invalid authorization data. Use dataClientToken or dataUserIdToken to authorize.\";\nvar braintreeVersion = \"3.117.0\";\nvar BRAINTREE_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/client.min.js\");\nvar BRAINTREE_PAYPAL_CHECKOUT_SOURCE = \"https://js.braintreegateway.com/web/\".concat(braintreeVersion, \"/js/paypal-checkout.min.js\");\n/*********************\n * PayPal namespaces *\n *********************/\nvar DEFAULT_PAYPAL_NAMESPACE = \"paypal\";\nvar DEFAULT_BRAINTREE_NAMESPACE = \"braintree\";\n/*****************\n * Hosted Fields *\n *****************/\nvar HOSTED_FIELDS_CHILDREN_ERROR = \"To use HostedFields you must use it with at least 3 children with types: [number, cvv, expirationDate] includes\";\nvar HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate HostedFields as children\";\n/*******************\n * Script Provider *\n *******************/\nvar SCRIPT_PROVIDER_REDUCER_ERROR = \"usePayPalScriptReducer must be used within a PayPalScriptProvider\";\nvar CARD_FIELDS_DUPLICATE_CHILDREN_ERROR = \"Cannot use duplicate CardFields as children\";\nvar CARD_FIELDS_CONTEXT_ERROR = \"Individual CardFields must be rendered inside the PayPalCardFieldsProvider\";\n\n/**\n * Get the namespace from the window in the browser\n * this is useful to get the paypal object from window\n * after load PayPal SDK script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getPayPalWindowNamespace$1(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_PAYPAL_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Get a namespace from the window in the browser\n * this is useful to get the braintree from window\n * after load Braintree script\n *\n * @param namespace the name space to return\n * @returns the namespace if exists or undefined otherwise\n */\nfunction getBraintreeWindowNamespace(namespace) {\n  if (namespace === void 0) {\n    namespace = DEFAULT_BRAINTREE_NAMESPACE;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return window[namespace];\n}\n/**\n * Creates a string hash code based on the string argument\n *\n * @param str the source input string to hash\n * @returns string hash code\n */\nfunction hashStr(str) {\n  var hash = \"\";\n  for (var i = 0; i < str.length; i++) {\n    var total = str[i].charCodeAt(0) * i;\n    if (str[i + 1]) {\n      total += str[i + 1].charCodeAt(0) * (i - 1);\n    }\n    hash += String.fromCharCode(97 + Math.abs(total) % 26);\n  }\n  return hash;\n}\nfunction generateErrorMessage(_a) {\n  var reactComponentName = _a.reactComponentName,\n    sdkComponentKey = _a.sdkComponentKey,\n    _b = _a.sdkRequestedComponents,\n    sdkRequestedComponents = _b === void 0 ? \"\" : _b,\n    _c = _a.sdkDataNamespace,\n    sdkDataNamespace = _c === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _c;\n  var requiredOptionCapitalized = sdkComponentKey.charAt(0).toUpperCase().concat(sdkComponentKey.substring(1));\n  var errorMessage = \"Unable to render <\".concat(reactComponentName, \" /> because window.\").concat(sdkDataNamespace, \".\").concat(requiredOptionCapitalized, \" is undefined.\");\n  // The JS SDK only loads the buttons component by default.\n  // All other components like messages and marks must be requested using the \"components\" query parameter\n  var requestedComponents = typeof sdkRequestedComponents === \"string\" ? sdkRequestedComponents : sdkRequestedComponents.join(\",\");\n  if (!requestedComponents.includes(sdkComponentKey)) {\n    var expectedComponents = [requestedComponents, sdkComponentKey].filter(Boolean).join();\n    errorMessage += \"\\nTo fix the issue, add '\".concat(sdkComponentKey, \"' to the list of components passed to the parent PayPalScriptProvider:\") + \"\\n`<PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>`.\");\n  }\n  return errorMessage;\n}\n\n/**\n * Generate a new random identifier for react-paypal-js\n *\n * @returns the {@code string} containing the random library name\n */\nfunction getScriptID(options) {\n  // exclude the data-react-paypal-script-id value from the options hash\n  var _a = options,\n    _b = SCRIPT_ID;\n  _a[_b];\n  var paypalScriptOptions = __rest$1(_a, [_b + \"\"]);\n  return \"react-paypal-js-\".concat(hashStr(JSON.stringify(paypalScriptOptions)));\n}\n/**\n * Destroy the PayPal SDK from the document page\n *\n * @param reactPayPalScriptID the script identifier\n */\nfunction destroySDKScript(reactPayPalScriptID) {\n  var scriptNode = self.document.querySelector(\"script[\".concat(SCRIPT_ID, \"=\\\"\").concat(reactPayPalScriptID, \"\\\"]\"));\n  if (scriptNode === null || scriptNode === void 0 ? void 0 : scriptNode.parentNode) {\n    scriptNode.parentNode.removeChild(scriptNode);\n  }\n}\n/**\n * Reducer function to handle complex state changes on the context\n *\n * @param state  the current state on the context object\n * @param action the action to be executed on the previous state\n * @returns a the same state if the action wasn't found, or a new state otherwise\n */\nfunction scriptReducer(state, action) {\n  var _a, _b;\n  switch (action.type) {\n    case DISPATCH_ACTION.LOADING_STATUS:\n      if (typeof action.value === \"object\") {\n        return __assign(__assign({}, state), {\n          loadingStatus: action.value.state,\n          loadingStatusErrorMessage: action.value.message\n        });\n      }\n      return __assign(__assign({}, state), {\n        loadingStatus: action.value\n      });\n    case DISPATCH_ACTION.RESET_OPTIONS:\n      // destroy existing script to make sure only one script loads at a time\n      destroySDKScript(state.options[SCRIPT_ID]);\n      return __assign(__assign({}, state), {\n        loadingStatus: SCRIPT_LOADING_STATE.PENDING,\n        options: __assign(__assign((_a = {}, _a[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _a), action.value), (_b = {}, _b[SCRIPT_ID] = \"\".concat(getScriptID(action.value)), _b))\n      });\n    case DISPATCH_ACTION.SET_BRAINTREE_INSTANCE:\n      return __assign(__assign({}, state), {\n        braintreePayPalCheckoutInstance: action.value\n      });\n    default:\n      {\n        return state;\n      }\n  }\n}\n// Create the React context to use in the script provider component\nvar ScriptContext = createContext(null);\n\n/**\n * Check if the context is valid and ready to dispatch actions.\n *\n * @param scriptContext the result of connecting to the context provider\n * @returns strict context avoiding null values in the type\n */\nfunction validateReducer(scriptContext) {\n  if (typeof (scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.dispatch) === \"function\" && scriptContext.dispatch.length !== 0) {\n    return scriptContext;\n  }\n  throw new Error(SCRIPT_PROVIDER_REDUCER_ERROR);\n}\n/**\n * Check if the dataClientToken or the dataUserIdToken are\n * set in the options of the context.\n * @type dataClientToken is use to pass a client token\n * @type dataUserIdToken is use to pass a client tokenization key\n *\n * @param scriptContext the result of connecting to the context provider\n * @throws an {@link Error} if both dataClientToken and the dataUserIdToken keys are null or undefined\n * @returns strict context if one of the keys are defined\n */\nvar validateBraintreeAuthorizationData = function (scriptContext) {\n  var _a, _b;\n  if (!((_a = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _a === void 0 ? void 0 : _a[SDK_SETTINGS.DATA_CLIENT_TOKEN]) && !((_b = scriptContext === null || scriptContext === void 0 ? void 0 : scriptContext.options) === null || _b === void 0 ? void 0 : _b[SDK_SETTINGS.DATA_USER_ID_TOKEN])) {\n    throw new Error(EMPTY_BRAINTREE_AUTHORIZATION_ERROR_MESSAGE);\n  }\n  return scriptContext;\n};\n\n/**\n * Custom hook to get access to the Script context and\n * dispatch actions to modify the state on the {@link ScriptProvider} component\n *\n * @returns a tuple containing the state of the context and\n * a dispatch function to modify the state\n */\nfunction usePayPalScriptReducer() {\n  var scriptContext = validateReducer(useContext(ScriptContext));\n  var derivedStatusContext = __assign(__assign({}, scriptContext), {\n    isInitial: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.INITIAL,\n    isPending: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.PENDING,\n    isResolved: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.RESOLVED,\n    isRejected: scriptContext.loadingStatus === SCRIPT_LOADING_STATE.REJECTED\n  });\n  return [derivedStatusContext, scriptContext.dispatch];\n}\n/**\n * Custom hook to get access to the ScriptProvider context\n *\n * @returns the latest state of the context\n */\nfunction useScriptProviderContext() {\n  var scriptContext = validateBraintreeAuthorizationData(validateReducer(useContext(ScriptContext)));\n  return [scriptContext, scriptContext.dispatch];\n}\n\n// Create the React context to use in the PayPal hosted fields provider\nvar PayPalHostedFieldsContext = createContext({});\n\n/**\n * Custom hook to get access to the PayPal Hosted Fields instance.\n * The instance represent the returned object after the render process\n * With this object a user can submit the fields and dynamically modify the cards\n *\n * @returns the hosted fields instance if is available in the component\n */\nfunction usePayPalHostedFields() {\n  return useContext(PayPalHostedFieldsContext);\n}\nfunction useProxyProps(props) {\n  var proxyRef = useRef(new Proxy({}, {\n    get: function (target, prop, receiver) {\n      /**\n       *\n       * If target[prop] is a function, return a function that accesses\n       * this function off the target object. We can mutate the target with\n       * new copies of this function without having to re-render the\n       * SDK components to pass new callbacks.\n       *\n       * */\n      if (typeof target[prop] === \"function\") {\n        return function () {\n          var args = [];\n          for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n          }\n          // eslint-disable-next-line @typescript-eslint/ban-types\n          return target[prop].apply(target, args);\n        };\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  }));\n  proxyRef.current = Object.assign(proxyRef.current, props);\n  return proxyRef.current;\n}\n\n/**\nThis `<PayPalButtons />` component supports rendering [buttons](https://developer.paypal.com/docs/business/javascript-sdk/javascript-sdk-reference/#buttons) for PayPal, Venmo, and alternative payment methods.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalButtons = function (_a) {\n  var _b;\n  var _c = _a.className,\n    className = _c === void 0 ? \"\" : _c,\n    _d = _a.disabled,\n    disabled = _d === void 0 ? false : _d,\n    children = _a.children,\n    _e = _a.forceReRender,\n    forceReRender = _e === void 0 ? [] : _e,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\"]);\n  var isDisabledStyle = disabled ? {\n    opacity: 0.38\n  } : {};\n  var classNames = \"\".concat(className, \" \").concat(disabled ? \"paypal-buttons-disabled\" : \"\").trim();\n  var buttonsContainerRef = useRef(null);\n  var buttons = useRef(null);\n  var proxyProps = useProxyProps(buttonProps);\n  var _f = usePayPalScriptReducer()[0],\n    isResolved = _f.isResolved,\n    options = _f.options;\n  var _g = useState(null),\n    initActions = _g[0],\n    setInitActions = _g[1];\n  var _h = useState(true),\n    isEligible = _h[0],\n    setIsEligible = _h[1];\n  var _j = useState(null),\n    setErrorState = _j[1];\n  function closeButtonsComponent() {\n    if (buttons.current !== null) {\n      buttons.current.close().catch(function () {\n        // ignore errors when closing the component\n      });\n    }\n  }\n  if ((_b = buttons.current) === null || _b === void 0 ? void 0 : _b.updateProps) {\n    buttons.current.updateProps({\n      message: buttonProps.message\n    });\n  }\n  // useEffect hook for rendering the buttons\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return closeButtonsComponent;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options.dataNamespace);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Buttons === undefined) {\n      setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalButtons.displayName,\n          sdkComponentKey: \"buttons\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n      return closeButtonsComponent;\n    }\n    var decoratedOnInit = function (data, actions) {\n      setInitActions(actions);\n      if (typeof buttonProps.onInit === \"function\") {\n        buttonProps.onInit(data, actions);\n      }\n    };\n    try {\n      buttons.current = paypalWindowNamespace.Buttons(__assign(__assign({}, proxyProps), {\n        onInit: decoratedOnInit\n      }));\n    } catch (err) {\n      return setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. Failed to initialize:  \".concat(err));\n      });\n    }\n    // only render the button when eligible\n    if (buttons.current.isEligible() === false) {\n      setIsEligible(false);\n      return closeButtonsComponent;\n    }\n    if (!buttonsContainerRef.current) {\n      return closeButtonsComponent;\n    }\n    buttons.current.render(buttonsContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (buttonsContainerRef.current === null || buttonsContainerRef.current.children.length === 0) {\n        // paypal buttons container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal buttons container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalButtons /> component. \".concat(err));\n      });\n    });\n    return closeButtonsComponent;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray(__spreadArray([isResolved], forceReRender, true), [buttonProps.fundingSource], false));\n  // useEffect hook for managing disabled state\n  useEffect(function () {\n    if (initActions === null) {\n      return;\n    }\n    if (disabled === true) {\n      initActions.disable().catch(function () {\n        // ignore errors when disabling the component\n      });\n    } else {\n      initActions.enable().catch(function () {\n        // ignore errors when enabling the component\n      });\n    }\n  }, [disabled, initActions]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: buttonsContainerRef,\n    style: isDisabledStyle,\n    className: classNames\n  }) : children);\n};\nPayPalButtons.displayName = \"PayPalButtons\";\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\") for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n    if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];\n  }\n  return t;\n}\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\nfunction findScript(url, attributes) {\n  var currentScript = document.querySelector(\"script[src=\\\"\".concat(url, \"\\\"]\"));\n  if (currentScript === null) return null;\n  var nextScript = createScriptElement(url, attributes);\n  var currentScriptClone = currentScript.cloneNode();\n  delete currentScriptClone.dataset.uidAuto;\n  if (Object.keys(currentScriptClone.dataset).length !== Object.keys(nextScript.dataset).length) {\n    return null;\n  }\n  var isExactMatch = true;\n  Object.keys(currentScriptClone.dataset).forEach(function (key) {\n    if (currentScriptClone.dataset[key] !== nextScript.dataset[key]) {\n      isExactMatch = false;\n    }\n  });\n  return isExactMatch ? currentScript : null;\n}\nfunction insertScriptElement(_a) {\n  var url = _a.url,\n    attributes = _a.attributes,\n    onSuccess = _a.onSuccess,\n    onError = _a.onError;\n  var newScript = createScriptElement(url, attributes);\n  newScript.onerror = onError;\n  newScript.onload = onSuccess;\n  document.head.insertBefore(newScript, document.head.firstElementChild);\n}\nfunction processOptions(_a) {\n  var customSdkBaseUrl = _a.sdkBaseUrl,\n    environment = _a.environment,\n    options = __rest(_a, [\"sdkBaseUrl\", \"environment\"]);\n  var sdkBaseUrl = customSdkBaseUrl || processSdkBaseUrl(environment);\n  var optionsWithStringIndex = options;\n  var _b = Object.keys(optionsWithStringIndex).filter(function (key) {\n      return typeof optionsWithStringIndex[key] !== \"undefined\" && optionsWithStringIndex[key] !== null && optionsWithStringIndex[key] !== \"\";\n    }).reduce(function (accumulator, key) {\n      var value = optionsWithStringIndex[key].toString();\n      key = camelCaseToKebabCase(key);\n      if (key.substring(0, 4) === \"data\" || key === \"crossorigin\") {\n        accumulator.attributes[key] = value;\n      } else {\n        accumulator.queryParams[key] = value;\n      }\n      return accumulator;\n    }, {\n      queryParams: {},\n      attributes: {}\n    }),\n    queryParams = _b.queryParams,\n    attributes = _b.attributes;\n  if (queryParams[\"merchant-id\"] && queryParams[\"merchant-id\"].indexOf(\",\") !== -1) {\n    attributes[\"data-merchant-id\"] = queryParams[\"merchant-id\"];\n    queryParams[\"merchant-id\"] = \"*\";\n  }\n  return {\n    url: \"\".concat(sdkBaseUrl, \"?\").concat(objectToQueryString(queryParams)),\n    attributes: attributes\n  };\n}\nfunction camelCaseToKebabCase(str) {\n  var replacer = function (match, indexOfMatch) {\n    return (indexOfMatch ? \"-\" : \"\") + match.toLowerCase();\n  };\n  return str.replace(/[A-Z]+(?![a-z])|[A-Z]/g, replacer);\n}\nfunction objectToQueryString(params) {\n  var queryString = \"\";\n  Object.keys(params).forEach(function (key) {\n    if (queryString.length !== 0) queryString += \"&\";\n    queryString += key + \"=\" + params[key];\n  });\n  return queryString;\n}\nfunction processSdkBaseUrl(environment) {\n  return environment === \"sandbox\" ? \"https://www.sandbox.paypal.com/sdk/js\" : \"https://www.paypal.com/sdk/js\";\n}\nfunction createScriptElement(url, attributes) {\n  if (attributes === void 0) {\n    attributes = {};\n  }\n  var newScript = document.createElement(\"script\");\n  newScript.src = url;\n  Object.keys(attributes).forEach(function (key) {\n    newScript.setAttribute(key, attributes[key]);\n    if (key === \"data-csp-nonce\") {\n      newScript.setAttribute(\"nonce\", attributes[\"data-csp-nonce\"]);\n    }\n  });\n  return newScript;\n}\nfunction loadScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  if (typeof document === \"undefined\") return PromisePonyfill.resolve(null);\n  var _a = processOptions(options),\n    url = _a.url,\n    attributes = _a.attributes;\n  var namespace = attributes[\"data-namespace\"] || \"paypal\";\n  var existingWindowNamespace = getPayPalWindowNamespace(namespace);\n  if (!attributes[\"data-js-sdk-library\"]) {\n    attributes[\"data-js-sdk-library\"] = \"paypal-js\";\n  }\n  if (findScript(url, attributes) && existingWindowNamespace) {\n    return PromisePonyfill.resolve(existingWindowNamespace);\n  }\n  return loadCustomScript({\n    url: url,\n    attributes: attributes\n  }, PromisePonyfill).then(function () {\n    var newWindowNamespace = getPayPalWindowNamespace(namespace);\n    if (newWindowNamespace) {\n      return newWindowNamespace;\n    }\n    throw new Error(\"The window.\".concat(namespace, \" global variable is not available.\"));\n  });\n}\nfunction loadCustomScript(options, PromisePonyfill) {\n  if (PromisePonyfill === void 0) {\n    PromisePonyfill = Promise;\n  }\n  validateArguments(options, PromisePonyfill);\n  var url = options.url,\n    attributes = options.attributes;\n  if (typeof url !== \"string\" || url.length === 0) {\n    throw new Error(\"Invalid url.\");\n  }\n  if (typeof attributes !== \"undefined\" && typeof attributes !== \"object\") {\n    throw new Error(\"Expected attributes to be an object.\");\n  }\n  return new PromisePonyfill(function (resolve, reject) {\n    if (typeof document === \"undefined\") return resolve();\n    insertScriptElement({\n      url: url,\n      attributes: attributes,\n      onSuccess: function () {\n        return resolve();\n      },\n      onError: function () {\n        var defaultError = new Error(\"The script \\\"\".concat(url, \"\\\" failed to load. Check the HTTP status code and response body in DevTools to learn more.\"));\n        return reject(defaultError);\n      }\n    });\n  });\n}\nfunction getPayPalWindowNamespace(namespace) {\n  return window[namespace];\n}\nfunction validateArguments(options, PromisePonyfill) {\n  if (typeof options !== \"object\" || options === null) {\n    throw new Error(\"Expected an options object.\");\n  }\n  var environment = options.environment;\n  if (environment && environment !== \"production\" && environment !== \"sandbox\") {\n    throw new Error('The `environment` option must be either \"production\" or \"sandbox\".');\n  }\n  if (typeof PromisePonyfill !== \"undefined\" && typeof PromisePonyfill !== \"function\") {\n    throw new Error(\"Expected PromisePonyfill to be a function.\");\n  }\n}\n\n/**\n * Simple check to determine if the Braintree is a valid namespace.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns a boolean representing if the namespace is valid.\n */\nvar isValidBraintreeNamespace = function (braintreeSource) {\n  var _a, _b;\n  if (typeof ((_a = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.client) === null || _a === void 0 ? void 0 : _a.create) !== \"function\" && typeof ((_b = braintreeSource === null || braintreeSource === void 0 ? void 0 : braintreeSource.paypalCheckout) === null || _b === void 0 ? void 0 : _b.create) !== \"function\") {\n    throw new Error(\"The braintreeNamespace property is not a valid BraintreeNamespace type.\");\n  }\n  return true;\n};\n/**\n * Use `actions.braintree` to provide an interface for the paypalCheckoutInstance\n * through the createOrder, createBillingAgreement and onApprove callbacks\n *\n * @param braintreeButtonProps the component button options\n * @returns a new copy of the component button options casted as {@link PayPalButtonsComponentProps}\n */\nvar decorateActions = function (buttonProps, payPalCheckoutInstance) {\n  var createOrderRef = buttonProps.createOrder;\n  var createBillingAgreementRef = buttonProps.createBillingAgreement;\n  var onApproveRef = buttonProps.onApprove;\n  if (typeof createOrderRef === \"function\") {\n    buttonProps.createOrder = function (data, actions) {\n      return createOrderRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof createBillingAgreementRef === \"function\") {\n    buttonProps.createBillingAgreement = function (data, actions) {\n      return createBillingAgreementRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  if (typeof onApproveRef === \"function\") {\n    buttonProps.onApprove = function (data, actions) {\n      return onApproveRef(data, __assign(__assign({}, actions), {\n        braintree: payPalCheckoutInstance\n      }));\n    };\n  }\n  return __assign({}, buttonProps);\n};\n/**\n * Get the Braintree namespace from the component props.\n * If the prop `braintreeNamespace` is undefined will try to load it from the CDN.\n * This function allows users to set the braintree manually on the `BraintreePayPalButtons` component.\n *\n * Use case can be for example legacy sites using AMD/UMD modules,\n * trying to integrate the `BraintreePayPalButtons` component.\n * If we attempt to load the Braintree from the CDN won't define the braintree namespace.\n * This happens because the braintree script is an UMD module.\n * After detecting the AMD on the global scope will create an anonymous module using `define`\n * and the `BraintreePayPalButtons` won't be able to get access to the `window.braintree` namespace\n * from the global context.\n *\n * @param braintreeSource the source {@link BraintreeNamespace}\n * @returns the {@link BraintreeNamespace}\n */\nvar getBraintreeNamespace = function (braintreeSource) {\n  if (braintreeSource && isValidBraintreeNamespace(braintreeSource)) {\n    return Promise.resolve(braintreeSource);\n  }\n  return Promise.all([loadCustomScript({\n    url: BRAINTREE_SOURCE\n  }), loadCustomScript({\n    url: BRAINTREE_PAYPAL_CHECKOUT_SOURCE\n  })]).then(function () {\n    return getBraintreeWindowNamespace();\n  });\n};\n\n/**\nThis `<BraintreePayPalButtons />` component renders the [Braintree PayPal Buttons](https://developer.paypal.com/braintree/docs/guides/paypal/overview) for Braintree Merchants.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nNote: You are able to make your integration using the client token or using the tokenization key.\n\n- To use the client token integration set the key `dataClientToken` in the `PayPayScriptProvider` component's options.\n- To use the tokenization key integration set the key `dataUserIdToken` in the `PayPayScriptProvider` component's options.\n*/\nvar BraintreePayPalButtons = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.disabled,\n    disabled = _c === void 0 ? false : _c,\n    children = _a.children,\n    _d = _a.forceReRender,\n    forceReRender = _d === void 0 ? [] : _d,\n    braintreeNamespace = _a.braintreeNamespace,\n    merchantAccountId = _a.merchantAccountId,\n    buttonProps = __rest$1(_a, [\"className\", \"disabled\", \"children\", \"forceReRender\", \"braintreeNamespace\", \"merchantAccountId\"]);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var _f = useScriptProviderContext(),\n    providerContext = _f[0],\n    dispatch = _f[1];\n  useEffect(function () {\n    getBraintreeNamespace(braintreeNamespace).then(function (braintree) {\n      var clientTokenizationKey = providerContext.options[SDK_SETTINGS.DATA_USER_ID_TOKEN];\n      var clientToken = providerContext.options[SDK_SETTINGS.DATA_CLIENT_TOKEN];\n      return braintree.client.create({\n        authorization: clientTokenizationKey || clientToken\n      }).then(function (clientInstance) {\n        var merchantProp = merchantAccountId ? {\n          merchantAccountId: merchantAccountId\n        } : {};\n        return braintree.paypalCheckout.create(__assign(__assign({}, merchantProp), {\n          client: clientInstance\n        }));\n      }).then(function (paypalCheckoutInstance) {\n        dispatch({\n          type: DISPATCH_ACTION.SET_BRAINTREE_INSTANCE,\n          value: paypalCheckoutInstance\n        });\n      });\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [providerContext.options]);\n  return React.createElement(React.Fragment, null, providerContext.braintreePayPalCheckoutInstance && React.createElement(PayPalButtons, __assign({\n    className: className,\n    disabled: disabled,\n    forceReRender: forceReRender\n  }, decorateActions(buttonProps, providerContext.braintreePayPalCheckoutInstance)), children));\n};\n\n/**\nThe `<PayPalMarks />` component is used for conditionally rendering different payment options using radio buttons.\nThe [Display PayPal Buttons with other Payment Methods guide](https://developer.paypal.com/docs/business/checkout/add-capabilities/buyer-experience/#display-paypal-buttons-with-other-payment-methods) describes this style of integration in detail.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n\nThis component can also be configured to use a single funding source similar to the [standalone buttons](https://developer.paypal.com/docs/business/checkout/configure-payments/standalone-buttons/) approach.\nA `FUNDING` object is exported by this library which has a key for every available funding source option.\n*/\nvar PayPalMarks = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    children = _a.children,\n    markProps = __rest$1(_a, [\"className\", \"children\"]);\n  var _c = usePayPalScriptReducer()[0],\n    isResolved = _c.isResolved,\n    options = _c.options;\n  var markContainerRef = useRef(null);\n  var _d = useState(true),\n    isEligible = _d[0],\n    setIsEligible = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  /**\n   * Render PayPal Mark into the DOM\n   */\n  var renderPayPalMark = function (mark) {\n    var current = markContainerRef.current;\n    // only render the mark when eligible\n    if (!current || !mark.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Remove any children before render it again\n    if (current.firstChild) {\n      current.removeChild(current.firstChild);\n    }\n    mark.render(current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (current === null || current.children.length === 0) {\n        // paypal marks container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal marks container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMarks /> component. \".concat(err));\n      });\n    });\n  };\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Marks === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMarks.displayName,\n          sdkComponentKey: \"marks\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    renderPayPalMark(paypalWindowNamespace.Marks(__assign({}, markProps)));\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [isResolved, markProps.fundingSource]);\n  return React.createElement(React.Fragment, null, isEligible ? React.createElement(\"div\", {\n    ref: markContainerRef,\n    className: className\n  }) : children);\n};\nPayPalMarks.displayName = \"PayPalMarks\";\n\n/**\nThis `<PayPalMessages />` messages component renders a credit messaging on upstream merchant sites.\nIt relies on the `<PayPalScriptProvider />` parent component for managing state related to loading the JS SDK script.\n*/\nvar PayPalMessages = function (_a) {\n  var _b = _a.className,\n    className = _b === void 0 ? \"\" : _b,\n    _c = _a.forceReRender,\n    forceReRender = _c === void 0 ? [] : _c,\n    messageProps = __rest$1(_a, [\"className\", \"forceReRender\"]);\n  var _d = usePayPalScriptReducer()[0],\n    isResolved = _d.isResolved,\n    options = _d.options;\n  var messagesContainerRef = useRef(null);\n  var messages = useRef(null);\n  var _e = useState(null),\n    setErrorState = _e[1];\n  useEffect(function () {\n    // verify the sdk script has successfully loaded\n    if (isResolved === false) {\n      return;\n    }\n    var paypalWindowNamespace = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]);\n    // verify dependency on window object\n    if (paypalWindowNamespace === undefined || paypalWindowNamespace.Messages === undefined) {\n      return setErrorState(function () {\n        throw new Error(generateErrorMessage({\n          reactComponentName: PayPalMessages.displayName,\n          sdkComponentKey: \"messages\",\n          sdkRequestedComponents: options.components,\n          sdkDataNamespace: options[SDK_SETTINGS.DATA_NAMESPACE]\n        }));\n      });\n    }\n    messages.current = paypalWindowNamespace.Messages(__assign({}, messageProps));\n    messages.current.render(messagesContainerRef.current).catch(function (err) {\n      // component failed to render, possibly because it was closed or destroyed.\n      if (messagesContainerRef.current === null || messagesContainerRef.current.children.length === 0) {\n        // paypal messages container is no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // paypal messages container is still in the DOM\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalMessages /> component. \".concat(err));\n      });\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, __spreadArray([isResolved], forceReRender, true));\n  return React.createElement(\"div\", {\n    ref: messagesContainerRef,\n    className: className\n  });\n};\nPayPalMessages.displayName = \"PayPalMessages\";\n\n/**\nThis `<PayPalScriptProvider />` component takes care of loading the JS SDK `<script>`.\nIt manages state for script loading so children components like `<PayPalButtons />` know when it's safe to use the `window.paypal` global namespace.\n\nNote: You always should use this component as a wrapper for  `PayPalButtons`, `PayPalMarks`, `PayPalMessages` and `BraintreePayPalButtons` components.\n */\nvar PayPalScriptProvider = function (_a) {\n  var _b;\n  var _c = _a.options,\n    options = _c === void 0 ? {\n      clientId: \"test\"\n    } : _c,\n    children = _a.children,\n    _d = _a.deferLoading,\n    deferLoading = _d === void 0 ? false : _d;\n  var _e = useReducer(scriptReducer, {\n      options: __assign(__assign({}, options), (_b = {}, _b[SDK_SETTINGS.DATA_JS_SDK_LIBRARY] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SDK_SETTINGS.DATA_SDK_INTEGRATION_SOURCE] = SDK_SETTINGS.DATA_LIBRARY_VALUE, _b[SCRIPT_ID] = \"\".concat(getScriptID(options)), _b)),\n      loadingStatus: deferLoading ? SCRIPT_LOADING_STATE.INITIAL : SCRIPT_LOADING_STATE.PENDING\n    }),\n    state = _e[0],\n    dispatch = _e[1];\n  useEffect(function () {\n    if (deferLoading === false && state.loadingStatus === SCRIPT_LOADING_STATE.INITIAL) {\n      return dispatch({\n        type: DISPATCH_ACTION.LOADING_STATUS,\n        value: SCRIPT_LOADING_STATE.PENDING\n      });\n    }\n    if (state.loadingStatus !== SCRIPT_LOADING_STATE.PENDING) {\n      return;\n    }\n    var isSubscribed = true;\n    loadScript(state.options).then(function () {\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: SCRIPT_LOADING_STATE.RESOLVED\n        });\n      }\n    }).catch(function (err) {\n      console.error(\"\".concat(LOAD_SCRIPT_ERROR, \" \").concat(err));\n      if (isSubscribed) {\n        dispatch({\n          type: DISPATCH_ACTION.LOADING_STATUS,\n          value: {\n            state: SCRIPT_LOADING_STATE.REJECTED,\n            message: String(err)\n          }\n        });\n      }\n    });\n    return function () {\n      isSubscribed = false;\n    };\n  }, [state.options, deferLoading, state.loadingStatus]);\n  return React.createElement(ScriptContext.Provider, {\n    value: __assign(__assign({}, state), {\n      dispatch: dispatch\n    })\n  }, children);\n};\n\n/**\n * Custom hook to store registered hosted fields children\n * Each `PayPalHostedField` component should be registered on the parent provider\n *\n * @param initialValue the initially registered components\n * @returns at first, an {@link Object} containing the registered hosted fields,\n * and at the second a function handler to register the hosted fields components\n */\nvar useHostedFieldsRegister = function (initialValue) {\n  if (initialValue === void 0) {\n    initialValue = {};\n  }\n  var registeredFields = useRef(initialValue);\n  var registerHostedField = function (component) {\n    registeredFields.current = __assign(__assign({}, registeredFields.current), component);\n  };\n  return [registeredFields, registerHostedField];\n};\n\n/**\n * Throw an exception if the HostedFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the hosted-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'hosted-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingHostedFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",hosted-fields\") : \"hosted-fields\";\n  var errorMessage = \"Unable to render <PayPalHostedFieldsProvider /> because window.\".concat(dataNamespace, \".HostedFields is undefined.\");\n  if (!components.includes(\"hosted-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'hosted-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\n/**\n * Validate the expiration date component. Valid combinations are:\n * 1- Only the `expirationDate` field exists.\n * 2- Only the `expirationMonth` and `expirationYear` fields exist. Cannot be used with the `expirationDate` field.\n *\n * @param registerTypes\n * @returns @type {true} when the children are valid\n */\nvar validateExpirationDate = function (registerTypes) {\n  return !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_DATE) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_MONTH) && !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.EXPIRATION_YEAR);\n};\n/**\n * Check if we find the [number, expiration, cvv] in children\n *\n * @param requiredChildren the list with required children [number, expiration, cvv]\n * @param registerTypes    the list of all the children types pass to the parent\n * @throw an @type {Error} when not find the default children\n */\nvar hasDefaultChildren = function (registerTypes) {\n  if (!registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.NUMBER) || !registerTypes.includes(PAYPAL_HOSTED_FIELDS_TYPES.CVV) || validateExpirationDate(registerTypes)) {\n    throw new Error(HOSTED_FIELDS_CHILDREN_ERROR);\n  }\n};\n/**\n * Check if we don't have duplicate children types\n *\n * @param registerTypes the list of all the children types pass to the parent\n * @throw an @type {Error} when duplicate types was found\n */\nvar noDuplicateChildren = function (registerTypes) {\n  if (registerTypes.length !== new Set(registerTypes).size) {\n    throw new Error(HOSTED_FIELDS_DUPLICATE_CHILDREN_ERROR);\n  }\n};\n/**\n * Validate the hosted field children in the PayPalHostedFieldsProvider component.\n * These are the rules:\n * 1- We need to find 3 default children for number, expiration, cvv\n * 2- No duplicate children are allowed\n * 3- No invalid combinations of `expirationDate`, `expirationMonth`, and `expirationYear`\n *\n * @param childrenList     the list of children\n * @param requiredChildren the list with required children [number, expiration, cvv]\n */\nvar validateHostedFieldChildren = function (registeredFields) {\n  hasDefaultChildren(registeredFields);\n  noDuplicateChildren(registeredFields);\n};\n\n/**\nThis `<PayPalHostedFieldsProvider />` provider component wraps the form field elements and accepts props like `createOrder()`.\n\nThis provider component is designed to be used with the `<PayPalHostedField />` component.\n\nWarning: If you don't see anything in the screen probably your client is ineligible.\nTo handle this problem make sure to use the prop `notEligibleError` and pass a component with a custom message.\nTake a look to this link if that is the case: https://developer.paypal.com/docs/checkout/advanced/integrate/\n*/\nvar PayPalHostedFieldsProvider = function (_a) {\n  var styles = _a.styles,\n    createOrder = _a.createOrder,\n    notEligibleError = _a.notEligibleError,\n    children = _a.children,\n    installments = _a.installments;\n  var _b = useScriptProviderContext()[0],\n    options = _b.options,\n    loadingStatus = _b.loadingStatus;\n  var _c = useState(true),\n    isEligible = _c[0],\n    setIsEligible = _c[1];\n  var _d = useState(),\n    cardFields = _d[0],\n    setCardFields = _d[1];\n  var _e = useState(null),\n    setErrorState = _e[1];\n  var hostedFieldsContainerRef = useRef(null);\n  var hostedFields = useRef();\n  var _f = useHostedFieldsRegister(),\n    registeredFields = _f[0],\n    registerHostedField = _f[1];\n  useEffect(function () {\n    var _a;\n    validateHostedFieldChildren(Object.keys(registeredFields.current));\n    // Only render the hosted fields when script is loaded and hostedFields is eligible\n    if (!(loadingStatus === SCRIPT_LOADING_STATE.RESOLVED)) {\n      return;\n    }\n    // Get the hosted fields from the [window.paypal.HostedFields] SDK\n    hostedFields.current = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE]).HostedFields;\n    if (!hostedFields.current) {\n      throw new Error(generateMissingHostedFieldsError((_a = {\n        components: options.components\n      }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n    }\n    if (!hostedFields.current.isEligible()) {\n      return setIsEligible(false);\n    }\n    // Clean all the fields before the rerender\n    if (cardFields) {\n      cardFields.teardown();\n    }\n    hostedFields.current.render({\n      // Call your server to set up the transaction\n      createOrder: createOrder,\n      fields: registeredFields.current,\n      installments: installments,\n      styles: styles\n    }).then(function (cardFieldsInstance) {\n      if (hostedFieldsContainerRef.current) {\n        setCardFields(cardFieldsInstance);\n      }\n    }).catch(function (err) {\n      setErrorState(function () {\n        throw new Error(\"Failed to render <PayPalHostedFieldsProvider /> component. \".concat(err));\n      });\n    });\n  }, [loadingStatus, styles]); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: hostedFieldsContainerRef\n  }, isEligible ? React.createElement(PayPalHostedFieldsContext.Provider, {\n    value: {\n      cardFields: cardFields,\n      registerHostedField: registerHostedField\n    }\n  }, children) : notEligibleError);\n};\n\n/**\nThis `<PayPalHostedField />` component renders individual fields for [Hosted Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nIt relies on the `<PayPalHostedFieldsProvider />` parent component for managing state related to loading the JS SDK script\nand execute some validations before the rendering the fields.\n\nTo use the PayPal hosted fields you need to define at least three fields:\n\n- A card number field\n- The CVV code from the client card\n- The expiration date\n\nYou can define the expiration date as a single field similar to the example below,\nor you are able to define it in [two separate fields](https://paypal.github.io/react-paypal-js//?path=/docs/paypal-paypalhostedfields--expiration-date). One for the month and second for year.\n\nNote: Take care when using multiple instances of the PayPal Hosted Fields on the same page.\nThe component will fail to render when any of the selectors return more than one element.\n*/\nvar PayPalHostedField = function (_a) {\n  var hostedFieldType = _a.hostedFieldType,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    options = _a.options,\n    // eslint-disable-line @typescript-eslint/no-unused-vars\n    props = __rest$1(_a, [\"hostedFieldType\", \"options\"]);\n  var hostedFieldContext = useContext(PayPalHostedFieldsContext);\n  useEffect(function () {\n    var _a;\n    if (!(hostedFieldContext === null || hostedFieldContext === void 0 ? void 0 : hostedFieldContext.registerHostedField)) {\n      throw new Error(\"The HostedField cannot be register in the PayPalHostedFieldsProvider parent component\");\n    }\n    // Register in the parent provider\n    hostedFieldContext.registerHostedField((_a = {}, _a[hostedFieldType] = {\n      selector: options.selector,\n      placeholder: options.placeholder,\n      type: options.type,\n      formatInput: options.formatInput,\n      maskInput: options.maskInput,\n      select: options.select,\n      maxlength: options.maxlength,\n      minlength: options.minlength,\n      prefill: options.prefill,\n      rejectUnsupportedCards: options.rejectUnsupportedCards\n    }, _a));\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", __assign({}, props));\n};\n\n/**\n * Throw an exception if the CardFields is not found in the paypal namespace\n * Probably cause for this problem is not sending the card-fields string\n * as part of the components props in options\n * {@code <PayPalScriptProvider options={{ components: 'card-fields'}}>}\n *\n * @param param0 and object containing the components and namespace defined in options\n * @throws {@code Error}\n *\n */\nvar generateMissingCardFieldsError = function (_a) {\n  var _b = _a.components,\n    components = _b === void 0 ? \"\" : _b,\n    _c = SDK_SETTINGS.DATA_NAMESPACE,\n    _d = _a[_c],\n    dataNamespace = _d === void 0 ? DEFAULT_PAYPAL_NAMESPACE : _d;\n  var expectedComponents = components ? \"\".concat(components, \",card-fields\") : \"card-fields\";\n  var errorMessage = \"Unable to render <PayPalCardFieldsProvider /> because window.\".concat(dataNamespace, \".CardFields is undefined.\");\n  if (!components.includes(\"card-fields\")) {\n    errorMessage += \"\\nTo fix the issue, add 'card-fields' to the list of components passed to the parent PayPalScriptProvider: <PayPalScriptProvider options={{ components: '\".concat(expectedComponents, \"'}}>\");\n  }\n  return errorMessage;\n};\nfunction ignore() {\n  return;\n}\nfunction hasChildren(container) {\n  var _a;\n  return !!((_a = container.current) === null || _a === void 0 ? void 0 : _a.children.length);\n}\nvar PayPalCardFieldsContext = createContext({\n  cardFieldsForm: null,\n  fields: {},\n  registerField: ignore,\n  unregisterField: ignore // implementation is inside hook and passed through the provider\n});\nvar usePayPalCardFields = function () {\n  return useContext(PayPalCardFieldsContext);\n};\nvar usePayPalCardFieldsRegistry = function () {\n  var _a = useState(null),\n    setError = _a[1];\n  var registeredFields = useRef({});\n  var registerField = function () {\n    var props = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      props[_i] = arguments[_i];\n    }\n    var fieldName = props[0],\n      options = props[1],\n      cardFields = props[2];\n    if (registeredFields.current[fieldName]) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_DUPLICATE_CHILDREN_ERROR);\n      });\n    }\n    registeredFields.current[fieldName] = cardFields === null || cardFields === void 0 ? void 0 : cardFields[fieldName](options);\n    return registeredFields.current[fieldName];\n  };\n  var unregisterField = function (fieldName) {\n    var field = registeredFields.current[fieldName];\n    if (field) {\n      field.close().catch(ignore);\n      delete registeredFields.current[fieldName];\n    }\n  };\n  return {\n    fields: registeredFields.current,\n    registerField: registerField,\n    unregisterField: unregisterField\n  };\n};\nvar FullWidthContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThe `<PayPalCardFieldsProvider />` is a context provider that is designed to support the rendering and state management of PayPal CardFields in your application.\n\nThe context provider will initialize the `CardFields` instance from the JS SDK and determine eligibility to render the CardField components. Once the `CardFields` are initialized, the context provider will manage the state of the `CardFields` instance as well as the reference to each individual card field.\n\nPassing the `inputEvents` and `style` props to the context provider will apply them to each of the individual field components.\n\nThe state managed by the provider is accessible through our custom hook `usePayPalCardFields`.\n\n*/\nvar PayPalCardFieldsProvider = function (_a) {\n  var children = _a.children,\n    props = __rest$1(_a, [\"children\"]);\n  var _b = usePayPalScriptReducer()[0],\n    isResolved = _b.isResolved,\n    options = _b.options;\n  var _c = usePayPalCardFieldsRegistry(),\n    fields = _c.fields,\n    registerField = _c.registerField,\n    unregisterField = _c.unregisterField;\n  var _d = useState(null),\n    cardFieldsForm = _d[0],\n    setCardFieldsForm = _d[1];\n  var cardFieldsInstance = useRef(null);\n  var _e = useState(false),\n    isEligible = _e[0],\n    setIsEligible = _e[1];\n  // We set the error inside state so that it can be caught by React's error boundary\n  var _f = useState(null),\n    setError = _f[1];\n  useEffect(function () {\n    var _a, _b, _c;\n    if (!isResolved) {\n      return;\n    }\n    try {\n      cardFieldsInstance.current = (_c = (_b = (_a = getPayPalWindowNamespace$1(options[SDK_SETTINGS.DATA_NAMESPACE])).CardFields) === null || _b === void 0 ? void 0 : _b.call(_a, __assign({}, props))) !== null && _c !== void 0 ? _c : null;\n    } catch (error) {\n      setError(function () {\n        throw new Error(\"Failed to render <PayPalCardFieldsProvider /> component. Failed to initialize:  \".concat(error));\n      });\n      return;\n    }\n    if (!cardFieldsInstance.current) {\n      setError(function () {\n        var _a;\n        throw new Error(generateMissingCardFieldsError((_a = {\n          components: options.components\n        }, _a[SDK_SETTINGS.DATA_NAMESPACE] = options[SDK_SETTINGS.DATA_NAMESPACE], _a)));\n      });\n      return;\n    }\n    setIsEligible(cardFieldsInstance.current.isEligible());\n    setCardFieldsForm(cardFieldsInstance.current);\n    return function () {\n      setCardFieldsForm(null);\n      cardFieldsInstance.current = null;\n    };\n  }, [isResolved]); // eslint-disable-line react-hooks/exhaustive-deps\n  if (!isEligible) {\n    // TODO: What should be returned here?\n    return React.createElement(\"div\", null);\n  }\n  return React.createElement(FullWidthContainer, null, React.createElement(PayPalCardFieldsContext.Provider, {\n    value: {\n      cardFieldsForm: cardFieldsForm,\n      fields: fields,\n      registerField: registerField,\n      unregisterField: unregisterField\n    }\n  }, children));\n};\nvar PayPalCardField = function (_a) {\n  var className = _a.className,\n    fieldName = _a.fieldName,\n    options = __rest$1(_a, [\"className\", \"fieldName\"]);\n  var _b = usePayPalCardFields(),\n    cardFieldsForm = _b.cardFieldsForm,\n    registerField = _b.registerField,\n    unregisterField = _b.unregisterField;\n  var containerRef = useRef(null);\n  // Set errors is state so that they can be caught by React's error boundary\n  var _c = useState(null),\n    setError = _c[1];\n  function closeComponent() {\n    unregisterField(fieldName);\n  }\n  useEffect(function () {\n    if (!cardFieldsForm) {\n      setError(function () {\n        throw new Error(CARD_FIELDS_CONTEXT_ERROR);\n      });\n      return closeComponent;\n    }\n    if (!containerRef.current) {\n      return closeComponent;\n    }\n    var registeredField = registerField(fieldName, options, cardFieldsForm);\n    registeredField === null || registeredField === void 0 ? void 0 : registeredField.render(containerRef.current).catch(function (err) {\n      if (!hasChildren(containerRef)) {\n        // Component no longer in the DOM, we can safely ignore the error\n        return;\n      }\n      // Component is still in the DOM\n      setError(function () {\n        throw new Error(\"Failed to render <PayPal\".concat(fieldName, \" /> component. \").concat(err));\n      });\n    });\n    return closeComponent;\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\n  return React.createElement(\"div\", {\n    ref: containerRef,\n    className: className\n  });\n};\nvar PayPalNameField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NameField\"\n  }, options));\n};\nvar PayPalNumberField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"NumberField\"\n  }, options));\n};\nvar PayPalExpiryField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"ExpiryField\"\n  }, options));\n};\nvar PayPalCVVField = function (options) {\n  return React.createElement(PayPalCardField, __assign({\n    fieldName: \"CVVField\"\n  }, options));\n};\nvar FlexContainer = function (_a) {\n  var children = _a.children;\n  return React.createElement(\"div\", {\n    style: {\n      display: \"flex\",\n      width: \"100%\"\n    }\n  }, children);\n};\n\n/**\nThis `<PayPalCardFieldsForm />` component renders the 4 individual fields for [Card Fields](https://developer.paypal.com/docs/business/checkout/advanced-card-payments/integrate#3-add-javascript-sdk-and-card-form) integrations.\nThis setup relies on the `<PayPalCardFieldsProvider />` parent component, which manages the state related to loading the JS SDK script and performs certain validations before rendering the fields.\n\n\n\nNote: If you want to have more granular control over the layout of how the fields are rendered, you can alternatively use our Individual Fields.\n*/\nvar PayPalCardFieldsForm = function (_a) {\n  var className = _a.className;\n  return React.createElement(\"div\", {\n    className: className\n  }, React.createElement(PayPalCardField, {\n    fieldName: \"NameField\"\n  }), React.createElement(PayPalCardField, {\n    fieldName: \"NumberField\"\n  }), React.createElement(FlexContainer, null, React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"ExpiryField\"\n  })), React.createElement(FullWidthContainer, null, React.createElement(PayPalCardField, {\n    fieldName: \"CVVField\"\n  }))));\n};\nvar FUNDING$1 = {\n  PAYPAL: \"paypal\",\n  VENMO: \"venmo\",\n  APPLEPAY: \"applepay\",\n  ITAU: \"itau\",\n  CREDIT: \"credit\",\n  PAYLATER: \"paylater\",\n  CARD: \"card\",\n  IDEAL: \"ideal\",\n  SEPA: \"sepa\",\n  BANCONTACT: \"bancontact\",\n  GIROPAY: \"giropay\",\n  SOFORT: \"sofort\",\n  EPS: \"eps\",\n  MYBANK: \"mybank\",\n  P24: \"p24\",\n  PAYU: \"payu\",\n  BLIK: \"blik\",\n  TRUSTLY: \"trustly\",\n  OXXO: \"oxxo\",\n  BOLETO: \"boleto\",\n  BOLETOBANCARIO: \"boletobancario\",\n  WECHATPAY: \"wechatpay\",\n  MERCADOPAGO: \"mercadopago\",\n  MULTIBANCO: \"multibanco\",\n  SATISPAY: \"satispay\",\n  PAIDY: \"paidy\",\n  ZIMPLER: \"zimpler\",\n  MAXIMA: \"maxima\"\n};\n[FUNDING$1.IDEAL, FUNDING$1.BANCONTACT, FUNDING$1.GIROPAY, FUNDING$1.SOFORT, FUNDING$1.EPS, FUNDING$1.MYBANK, FUNDING$1.P24, FUNDING$1.PAYU, FUNDING$1.BLIK, FUNDING$1.TRUSTLY, FUNDING$1.OXXO, FUNDING$1.BOLETO, FUNDING$1.BOLETOBANCARIO, FUNDING$1.WECHATPAY, FUNDING$1.MERCADOPAGO, FUNDING$1.MULTIBANCO, FUNDING$1.SATISPAY, FUNDING$1.PAIDY, FUNDING$1.MAXIMA, FUNDING$1.ZIMPLER];\n\n// We do not re-export `FUNDING` from the `sdk-constants` module\n// directly because it has no type definitions.\n//\n// See https://github.com/paypal/react-paypal-js/issues/125\nvar FUNDING = FUNDING$1;\nexport { BraintreePayPalButtons, DISPATCH_ACTION, FUNDING, PAYPAL_HOSTED_FIELDS_TYPES, PayPalButtons, PayPalCVVField, PayPalCardFieldsContext, PayPalCardFieldsForm, PayPalCardFieldsProvider, PayPalExpiryField, PayPalHostedField, PayPalHostedFieldsProvider, PayPalMarks, PayPalMessages, PayPalNameField, PayPalNumberField, PayPalScriptProvider, SCRIPT_LOADING_STATE, ScriptContext, destroySDKScript, getScriptID, scriptReducer, usePayPalCardFields, usePayPalHostedFields, usePayPalScriptReducer, useScriptProviderContext };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;;AAEjG;AACA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC/BA,oBAAoB,CAAC,SAAS,CAAC,GAAG,SAAS;EAC3CA,oBAAoB,CAAC,SAAS,CAAC,GAAG,SAAS;EAC3CA,oBAAoB,CAAC,UAAU,CAAC,GAAG,UAAU;EAC7CA,oBAAoB,CAAC,UAAU,CAAC,GAAG,UAAU;AAC/C,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe;AACnB,CAAC,UAAUA,eAAe,EAAE;EAC1BA,eAAe,CAAC,gBAAgB,CAAC,GAAG,kBAAkB;EACtDA,eAAe,CAAC,eAAe,CAAC,GAAG,cAAc;EACjDA,eAAe,CAAC,wBAAwB,CAAC,GAAG,mBAAmB;AACjE,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7C;AACA;AACA;AACA;AACA;AACA,IAAIC,0BAA0B;AAC9B,CAAC,UAAUA,0BAA0B,EAAE;EACrCA,0BAA0B,CAAC,QAAQ,CAAC,GAAG,QAAQ;EAC/CA,0BAA0B,CAAC,KAAK,CAAC,GAAG,KAAK;EACzCA,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,gBAAgB;EAChEA,0BAA0B,CAAC,kBAAkB,CAAC,GAAG,iBAAiB;EAClEA,0BAA0B,CAAC,iBAAiB,CAAC,GAAG,gBAAgB;EAChEA,0BAA0B,CAAC,aAAa,CAAC,GAAG,YAAY;AAC1D,CAAC,EAAEA,0BAA0B,KAAKA,0BAA0B,GAAG,CAAC,CAAC,CAAC,CAAC;AACnE,IAAIC,QAAQ,GAAG,SAAAA,CAAA,EAAY;EACzBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,IAAI,SAASF,QAAQA,CAACG,CAAC,EAAE;IAC/C,KAAK,IAAIC,CAAC,EAAEC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACnDD,CAAC,GAAGG,SAAS,CAACF,CAAC,CAAC;MAChB,KAAK,IAAII,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;IAC9E;IACA,OAAON,CAAC;EACV,CAAC;EACD,OAAOH,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;AACxC,CAAC;AACD,SAASO,QAAQA,CAACV,CAAC,EAAEW,CAAC,EAAE;EACtB,IAAIZ,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIM,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,IAAIM,CAAC,CAACC,OAAO,CAACP,CAAC,CAAC,GAAG,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAChG,IAAIL,CAAC,IAAI,IAAI,IAAI,OAAOH,MAAM,CAACgB,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGR,MAAM,CAACgB,qBAAqB,CAACb,CAAC,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACD,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC3I,IAAIU,CAAC,CAACC,OAAO,CAACP,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIJ,MAAM,CAACS,SAAS,CAACQ,oBAAoB,CAACN,IAAI,CAACR,CAAC,EAAEK,CAAC,CAACJ,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACM,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACK,CAAC,CAACJ,CAAC,CAAC,CAAC;EACnG;EACA,OAAOF,CAAC;AACV;AACA,SAASgB,aAAaA,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,EAAE;EACrC,IAAIA,IAAI,IAAIf,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEkB,CAAC,GAAGF,IAAI,CAACb,MAAM,EAAEgB,EAAE,EAAEnB,CAAC,GAAGkB,CAAC,EAAElB,CAAC,EAAE,EAAE;IACnF,IAAImB,EAAE,IAAI,EAAEnB,CAAC,IAAIgB,IAAI,CAAC,EAAE;MACtB,IAAI,CAACG,EAAE,EAAEA,EAAE,GAAGC,KAAK,CAACf,SAAS,CAACgB,KAAK,CAACd,IAAI,CAACS,IAAI,EAAE,CAAC,EAAEhB,CAAC,CAAC;MACpDmB,EAAE,CAACnB,CAAC,CAAC,GAAGgB,IAAI,CAAChB,CAAC,CAAC;IACjB;EACF;EACA,OAAOe,EAAE,CAACO,MAAM,CAACH,EAAE,IAAIC,KAAK,CAACf,SAAS,CAACgB,KAAK,CAACd,IAAI,CAACS,IAAI,CAAC,CAAC;AAC1D;AACA,OAAOO,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC9F,IAAIhB,CAAC,GAAG,IAAIiB,KAAK,CAACD,OAAO,CAAC;EAC1B,OAAOhB,CAAC,CAACkB,IAAI,GAAG,iBAAiB,EAAElB,CAAC,CAACc,KAAK,GAAGA,KAAK,EAAEd,CAAC,CAACe,UAAU,GAAGA,UAAU,EAAEf,CAAC;AAClF,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAImB,SAAS,GAAG,6BAA6B;AAC7C,IAAIC,YAAY,GAAG;EACjBC,iBAAiB,EAAE,iBAAiB;EACpCC,mBAAmB,EAAE,kBAAkB;EACvCC,kBAAkB,EAAE,iBAAiB;EACrCC,cAAc,EAAE,eAAe;EAC/BC,2BAA2B,EAAE,0BAA0B;EACvDC,kBAAkB,EAAE;AACtB,CAAC;AACD,IAAIC,iBAAiB,GAAG,0CAA0C;AAClE;AACA;AACA;AACA,IAAIC,2CAA2C,GAAG,kFAAkF;AACpI,IAAIC,gBAAgB,GAAG,SAAS;AAChC,IAAIC,gBAAgB,GAAG,sCAAsC,CAAClB,MAAM,CAACiB,gBAAgB,EAAE,mBAAmB,CAAC;AAC3G,IAAIE,gCAAgC,GAAG,sCAAsC,CAACnB,MAAM,CAACiB,gBAAgB,EAAE,4BAA4B,CAAC;AACpI;AACA;AACA;AACA,IAAIG,wBAAwB,GAAG,QAAQ;AACvC,IAAIC,2BAA2B,GAAG,WAAW;AAC7C;AACA;AACA;AACA,IAAIC,4BAA4B,GAAG,iHAAiH;AACpJ,IAAIC,sCAAsC,GAAG,+CAA+C;AAC5F;AACA;AACA;AACA,IAAIC,6BAA6B,GAAG,mEAAmE;AACvG,IAAIC,oCAAoC,GAAG,6CAA6C;AACxF,IAAIC,yBAAyB,GAAG,4EAA4E;;AAE5G;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,0BAA0BA,CAACC,SAAS,EAAE;EAC7C,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAGR,wBAAwB;EACtC;EACA;EACA,OAAOS,MAAM,CAACD,SAAS,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,2BAA2BA,CAACF,SAAS,EAAE;EAC9C,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAGP,2BAA2B;EACzC;EACA;EACA,OAAOQ,MAAM,CAACD,SAAS,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,OAAOA,CAACC,GAAG,EAAE;EACpB,IAAIC,IAAI,GAAG,EAAE;EACb,KAAK,IAAIvD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsD,GAAG,CAACnD,MAAM,EAAEH,CAAC,EAAE,EAAE;IACnC,IAAIwD,KAAK,GAAGF,GAAG,CAACtD,CAAC,CAAC,CAACyD,UAAU,CAAC,CAAC,CAAC,GAAGzD,CAAC;IACpC,IAAIsD,GAAG,CAACtD,CAAC,GAAG,CAAC,CAAC,EAAE;MACdwD,KAAK,IAAIF,GAAG,CAACtD,CAAC,GAAG,CAAC,CAAC,CAACyD,UAAU,CAAC,CAAC,CAAC,IAAIzD,CAAC,GAAG,CAAC,CAAC;IAC7C;IACAuD,IAAI,IAAIG,MAAM,CAACC,YAAY,CAAC,EAAE,GAAGC,IAAI,CAACC,GAAG,CAACL,KAAK,CAAC,GAAG,EAAE,CAAC;EACxD;EACA,OAAOD,IAAI;AACb;AACA,SAASO,oBAAoBA,CAACC,EAAE,EAAE;EAChC,IAAIC,kBAAkB,GAAGD,EAAE,CAACC,kBAAkB;IAC5CC,eAAe,GAAGF,EAAE,CAACE,eAAe;IACpCC,EAAE,GAAGH,EAAE,CAACI,sBAAsB;IAC9BA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAChDE,EAAE,GAAGL,EAAE,CAACM,gBAAgB;IACxBA,gBAAgB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG1B,wBAAwB,GAAG0B,EAAE;EAClE,IAAIE,yBAAyB,GAAGL,eAAe,CAACM,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAClD,MAAM,CAAC2C,eAAe,CAACQ,SAAS,CAAC,CAAC,CAAC,CAAC;EAC5G,IAAIC,YAAY,GAAG,oBAAoB,CAACpD,MAAM,CAAC0C,kBAAkB,EAAE,qBAAqB,CAAC,CAAC1C,MAAM,CAAC+C,gBAAgB,EAAE,GAAG,CAAC,CAAC/C,MAAM,CAACgD,yBAAyB,EAAE,gBAAgB,CAAC;EAC3K;EACA;EACA,IAAIK,mBAAmB,GAAG,OAAOR,sBAAsB,KAAK,QAAQ,GAAGA,sBAAsB,GAAGA,sBAAsB,CAACS,IAAI,CAAC,GAAG,CAAC;EAChI,IAAI,CAACD,mBAAmB,CAACE,QAAQ,CAACZ,eAAe,CAAC,EAAE;IAClD,IAAIa,kBAAkB,GAAG,CAACH,mBAAmB,EAAEV,eAAe,CAAC,CAACc,MAAM,CAACC,OAAO,CAAC,CAACJ,IAAI,CAAC,CAAC;IACtFF,YAAY,IAAI,2BAA2B,CAACpD,MAAM,CAAC2C,eAAe,EAAE,wEAAwE,CAAC,GAAG,mDAAmD,CAAC3C,MAAM,CAACwD,kBAAkB,EAAE,QAAQ,CAAC;EAC1O;EACA,OAAOJ,YAAY;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASO,WAAWA,CAACC,OAAO,EAAE;EAC5B;EACA,IAAInB,EAAE,GAAGmB,OAAO;IACdhB,EAAE,GAAGrC,SAAS;EAChBkC,EAAE,CAACG,EAAE,CAAC;EACN,IAAIiB,mBAAmB,GAAG1E,QAAQ,CAACsD,EAAE,EAAE,CAACG,EAAE,GAAG,EAAE,CAAC,CAAC;EACjD,OAAO,kBAAkB,CAAC5C,MAAM,CAAC+B,OAAO,CAAC+B,IAAI,CAACC,SAAS,CAACF,mBAAmB,CAAC,CAAC,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,gBAAgBA,CAACC,mBAAmB,EAAE;EAC7C,IAAIC,UAAU,GAAGC,IAAI,CAACC,QAAQ,CAACC,aAAa,CAAC,SAAS,CAACrE,MAAM,CAACO,SAAS,EAAE,KAAK,CAAC,CAACP,MAAM,CAACiE,mBAAmB,EAAE,KAAK,CAAC,CAAC;EACnH,IAAIC,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,UAAU,EAAE;IACjFJ,UAAU,CAACI,UAAU,CAACC,WAAW,CAACL,UAAU,CAAC;EAC/C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACpC,IAAIjC,EAAE,EAAEG,EAAE;EACV,QAAQ8B,MAAM,CAACC,IAAI;IACjB,KAAKxG,eAAe,CAACyG,cAAc;MACjC,IAAI,OAAOF,MAAM,CAACG,KAAK,KAAK,QAAQ,EAAE;QACpC,OAAOxG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE;UACnCK,aAAa,EAAEJ,MAAM,CAACG,KAAK,CAACJ,KAAK;UACjCM,yBAAyB,EAAEL,MAAM,CAACG,KAAK,CAACzE;QAC1C,CAAC,CAAC;MACJ;MACA,OAAO/B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE;QACnCK,aAAa,EAAEJ,MAAM,CAACG;MACxB,CAAC,CAAC;IACJ,KAAK1G,eAAe,CAAC6G,aAAa;MAChC;MACAhB,gBAAgB,CAACS,KAAK,CAACb,OAAO,CAACrD,SAAS,CAAC,CAAC;MAC1C,OAAOlC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE;QACnCK,aAAa,EAAE5G,oBAAoB,CAAC+G,OAAO;QAC3CrB,OAAO,EAAEvF,QAAQ,CAACA,QAAQ,EAAEoE,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACjC,YAAY,CAACK,2BAA2B,CAAC,GAAGL,YAAY,CAACG,kBAAkB,EAAE8B,EAAE,GAAGiC,MAAM,CAACG,KAAK,CAAC,GAAGjC,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACrC,SAAS,CAAC,GAAG,EAAE,CAACP,MAAM,CAAC2D,WAAW,CAACe,MAAM,CAACG,KAAK,CAAC,CAAC,EAAEjC,EAAE,CAAC;MAC9M,CAAC,CAAC;IACJ,KAAKzE,eAAe,CAAC+G,sBAAsB;MACzC,OAAO7G,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE;QACnCU,+BAA+B,EAAET,MAAM,CAACG;MAC1C,CAAC,CAAC;IACJ;MACE;QACE,OAAOJ,KAAK;MACd;EACJ;AACF;AACA;AACA,IAAIW,aAAa,GAAGxH,aAAa,CAAC,IAAI,CAAC;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,SAASyH,eAAeA,CAACC,aAAa,EAAE;EACtC,IAAI,QAAQA,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,QAAQ,CAAC,KAAK,UAAU,IAAID,aAAa,CAACC,QAAQ,CAAC1G,MAAM,KAAK,CAAC,EAAE;IACvJ,OAAOyG,aAAa;EACtB;EACA,MAAM,IAAIjF,KAAK,CAACmB,6BAA6B,CAAC;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgE,kCAAkC,GAAG,SAAAA,CAAUF,aAAa,EAAE;EAChE,IAAI7C,EAAE,EAAEG,EAAE;EACV,IAAI,EAAE,CAACH,EAAE,GAAG6C,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC1B,OAAO,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjC,YAAY,CAACC,iBAAiB,CAAC,CAAC,IAAI,EAAE,CAACmC,EAAE,GAAG0C,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC1B,OAAO,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACpC,YAAY,CAACM,kBAAkB,CAAC,CAAC,EAAE;IACnV,MAAM,IAAIT,KAAK,CAACW,2CAA2C,CAAC;EAC9D;EACA,OAAOsE,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,sBAAsBA,CAAA,EAAG;EAChC,IAAIH,aAAa,GAAGD,eAAe,CAACxH,UAAU,CAACuH,aAAa,CAAC,CAAC;EAC9D,IAAIM,oBAAoB,GAAGrH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEiH,aAAa,CAAC,EAAE;IAC/DK,SAAS,EAAEL,aAAa,CAACR,aAAa,KAAK5G,oBAAoB,CAAC0H,OAAO;IACvEC,SAAS,EAAEP,aAAa,CAACR,aAAa,KAAK5G,oBAAoB,CAAC+G,OAAO;IACvEa,UAAU,EAAER,aAAa,CAACR,aAAa,KAAK5G,oBAAoB,CAAC6H,QAAQ;IACzEC,UAAU,EAAEV,aAAa,CAACR,aAAa,KAAK5G,oBAAoB,CAAC+H;EACnE,CAAC,CAAC;EACF,OAAO,CAACP,oBAAoB,EAAEJ,aAAa,CAACC,QAAQ,CAAC;AACvD;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,wBAAwBA,CAAA,EAAG;EAClC,IAAIZ,aAAa,GAAGE,kCAAkC,CAACH,eAAe,CAACxH,UAAU,CAACuH,aAAa,CAAC,CAAC,CAAC;EAClG,OAAO,CAACE,aAAa,EAAEA,aAAa,CAACC,QAAQ,CAAC;AAChD;;AAEA;AACA,IAAIY,yBAAyB,GAAGvI,aAAa,CAAC,CAAC,CAAC,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwI,qBAAqBA,CAAA,EAAG;EAC/B,OAAOvI,UAAU,CAACsI,yBAAyB,CAAC;AAC9C;AACA,SAASE,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIC,QAAQ,GAAGzI,MAAM,CAAC,IAAI0I,KAAK,CAAC,CAAC,CAAC,EAAE;IAClCC,GAAG,EAAE,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,EAAE;MACrC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;MACM,IAAI,OAAOF,MAAM,CAACC,IAAI,CAAC,KAAK,UAAU,EAAE;QACtC,OAAO,YAAY;UACjB,IAAIE,IAAI,GAAG,EAAE;UACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlI,SAAS,CAACC,MAAM,EAAEiI,EAAE,EAAE,EAAE;YAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGlI,SAAS,CAACkI,EAAE,CAAC;UAC1B;UACA;UACA,OAAOJ,MAAM,CAACC,IAAI,CAAC,CAACzH,KAAK,CAACwH,MAAM,EAAEG,IAAI,CAAC;QACzC,CAAC;MACH;MACA,OAAOE,OAAO,CAACN,GAAG,CAACC,MAAM,EAAEC,IAAI,EAAEC,QAAQ,CAAC;IAC5C;EACF,CAAC,CAAC,CAAC;EACHL,QAAQ,CAACS,OAAO,GAAG1I,MAAM,CAACC,MAAM,CAACgI,QAAQ,CAACS,OAAO,EAAEV,KAAK,CAAC;EACzD,OAAOC,QAAQ,CAACS,OAAO;AACzB;;AAEA;AACA;AACA;AACA;AACA,IAAIC,aAAa,GAAG,SAAAA,CAAUxE,EAAE,EAAE;EAChC,IAAIG,EAAE;EACN,IAAIE,EAAE,GAAGL,EAAE,CAACyE,SAAS;IACnBA,SAAS,GAAGpE,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACnCqE,EAAE,GAAG1E,EAAE,CAAC2E,QAAQ;IAChBA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACrCE,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACtBC,EAAE,GAAG7E,EAAE,CAAC8E,aAAa;IACrBA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACvCE,WAAW,GAAGrI,QAAQ,CAACsD,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC;EACpF,IAAIgF,eAAe,GAAGL,QAAQ,GAAG;IAC/BM,OAAO,EAAE;EACX,CAAC,GAAG,CAAC,CAAC;EACN,IAAIC,UAAU,GAAG,EAAE,CAAC3H,MAAM,CAACkH,SAAS,EAAE,GAAG,CAAC,CAAClH,MAAM,CAACoH,QAAQ,GAAG,yBAAyB,GAAG,EAAE,CAAC,CAACQ,IAAI,CAAC,CAAC;EACnG,IAAIC,mBAAmB,GAAG/J,MAAM,CAAC,IAAI,CAAC;EACtC,IAAIgK,OAAO,GAAGhK,MAAM,CAAC,IAAI,CAAC;EAC1B,IAAIiK,UAAU,GAAG1B,aAAa,CAACmB,WAAW,CAAC;EAC3C,IAAIQ,EAAE,GAAGvC,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCK,UAAU,GAAGkC,EAAE,CAAClC,UAAU;IAC1BlC,OAAO,GAAGoE,EAAE,CAACpE,OAAO;EACtB,IAAIqE,EAAE,GAAGlK,QAAQ,CAAC,IAAI,CAAC;IACrBmK,WAAW,GAAGD,EAAE,CAAC,CAAC,CAAC;IACnBE,cAAc,GAAGF,EAAE,CAAC,CAAC,CAAC;EACxB,IAAIG,EAAE,GAAGrK,QAAQ,CAAC,IAAI,CAAC;IACrBsK,UAAU,GAAGD,EAAE,CAAC,CAAC,CAAC;IAClBE,aAAa,GAAGF,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIG,EAAE,GAAGxK,QAAQ,CAAC,IAAI,CAAC;IACrByK,aAAa,GAAGD,EAAE,CAAC,CAAC,CAAC;EACvB,SAASE,qBAAqBA,CAAA,EAAG;IAC/B,IAAIX,OAAO,CAACd,OAAO,KAAK,IAAI,EAAE;MAC5Bc,OAAO,CAACd,OAAO,CAAC0B,KAAK,CAAC,CAAC,CAACC,KAAK,CAAC,YAAY;QACxC;MAAA,CACD,CAAC;IACJ;EACF;EACA,IAAI,CAAC/F,EAAE,GAAGkF,OAAO,CAACd,OAAO,MAAM,IAAI,IAAIpE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgG,WAAW,EAAE;IAC9Ed,OAAO,CAACd,OAAO,CAAC4B,WAAW,CAAC;MAC1BxI,OAAO,EAAEoH,WAAW,CAACpH;IACvB,CAAC,CAAC;EACJ;EACA;EACApC,SAAS,CAAC,YAAY;IACpB;IACA,IAAI8H,UAAU,KAAK,KAAK,EAAE;MACxB,OAAO2C,qBAAqB;IAC9B;IACA,IAAII,qBAAqB,GAAGlH,0BAA0B,CAACiC,OAAO,CAACkF,aAAa,CAAC;IAC7E;IACA,IAAID,qBAAqB,KAAKE,SAAS,IAAIF,qBAAqB,CAACG,OAAO,KAAKD,SAAS,EAAE;MACtFP,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAACmC,oBAAoB,CAAC;UACnCE,kBAAkB,EAAEuE,aAAa,CAACgC,WAAW;UAC7CtG,eAAe,EAAE,SAAS;UAC1BE,sBAAsB,EAAEe,OAAO,CAACsF,UAAU;UAC1CnG,gBAAgB,EAAEa,OAAO,CAACpD,YAAY,CAACI,cAAc;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACF,OAAO6H,qBAAqB;IAC9B;IACA,IAAIU,eAAe,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAE;MAC7ClB,cAAc,CAACkB,OAAO,CAAC;MACvB,IAAI,OAAO7B,WAAW,CAAC8B,MAAM,KAAK,UAAU,EAAE;QAC5C9B,WAAW,CAAC8B,MAAM,CAACF,IAAI,EAAEC,OAAO,CAAC;MACnC;IACF,CAAC;IACD,IAAI;MACFvB,OAAO,CAACd,OAAO,GAAG6B,qBAAqB,CAACG,OAAO,CAAC3K,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0J,UAAU,CAAC,EAAE;QACjFuB,MAAM,EAAEH;MACV,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ,OAAOf,aAAa,CAAC,YAAY;QAC/B,MAAM,IAAInI,KAAK,CAAC,uEAAuE,CAACL,MAAM,CAACuJ,GAAG,CAAC,CAAC;MACtG,CAAC,CAAC;IACJ;IACA;IACA,IAAIzB,OAAO,CAACd,OAAO,CAACqB,UAAU,CAAC,CAAC,KAAK,KAAK,EAAE;MAC1CC,aAAa,CAAC,KAAK,CAAC;MACpB,OAAOG,qBAAqB;IAC9B;IACA,IAAI,CAACZ,mBAAmB,CAACb,OAAO,EAAE;MAChC,OAAOyB,qBAAqB;IAC9B;IACAX,OAAO,CAACd,OAAO,CAACwC,MAAM,CAAC3B,mBAAmB,CAACb,OAAO,CAAC,CAAC2B,KAAK,CAAC,UAAUY,GAAG,EAAE;MACvE;MACA,IAAI1B,mBAAmB,CAACb,OAAO,KAAK,IAAI,IAAIa,mBAAmB,CAACb,OAAO,CAACK,QAAQ,CAACxI,MAAM,KAAK,CAAC,EAAE;QAC7F;QACA;MACF;MACA;MACA2J,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAAC,gDAAgD,CAACL,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAC/E,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOd,qBAAqB;IAC5B;EACF,CAAC,EAAEjJ,aAAa,CAACA,aAAa,CAAC,CAACsG,UAAU,CAAC,EAAEyB,aAAa,EAAE,IAAI,CAAC,EAAE,CAACC,WAAW,CAACiC,aAAa,CAAC,EAAE,KAAK,CAAC,CAAC;EACvG;EACAzL,SAAS,CAAC,YAAY;IACpB,IAAIkK,WAAW,KAAK,IAAI,EAAE;MACxB;IACF;IACA,IAAId,QAAQ,KAAK,IAAI,EAAE;MACrBc,WAAW,CAACwB,OAAO,CAAC,CAAC,CAACf,KAAK,CAAC,YAAY;QACtC;MAAA,CACD,CAAC;IACJ,CAAC,MAAM;MACLT,WAAW,CAACyB,MAAM,CAAC,CAAC,CAAChB,KAAK,CAAC,YAAY;QACrC;MAAA,CACD,CAAC;IACJ;EACF,CAAC,EAAE,CAACvB,QAAQ,EAAEc,WAAW,CAAC,CAAC;EAC3B,OAAOvK,KAAK,CAACiM,aAAa,CAACjM,KAAK,CAACkM,QAAQ,EAAE,IAAI,EAAExB,UAAU,GAAG1K,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IACvFE,GAAG,EAAEjC,mBAAmB;IACxBkC,KAAK,EAAEtC,eAAe;IACtBP,SAAS,EAAES;EACb,CAAC,CAAC,GAAGN,QAAQ,CAAC;AAChB,CAAC;AACDJ,aAAa,CAACgC,WAAW,GAAG,eAAe;AAC3C,SAASe,MAAMA,CAACvL,CAAC,EAAEW,CAAC,EAAE;EACpB,IAAIZ,CAAC,GAAG,CAAC,CAAC;EACV,KAAK,IAAIM,CAAC,IAAIL,CAAC,EAAE,IAAIH,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACR,CAAC,EAAEK,CAAC,CAAC,IAAIM,CAAC,CAACC,OAAO,CAACP,CAAC,CAAC,GAAG,CAAC,EAAEN,CAAC,CAACM,CAAC,CAAC,GAAGL,CAAC,CAACK,CAAC,CAAC;EAChG,IAAIL,CAAC,IAAI,IAAI,IAAI,OAAOH,MAAM,CAACgB,qBAAqB,KAAK,UAAU,EAAE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEI,CAAC,GAAGR,MAAM,CAACgB,qBAAqB,CAACb,CAAC,CAAC,EAAEC,CAAC,GAAGI,CAAC,CAACD,MAAM,EAAEH,CAAC,EAAE,EAAE;IAC3I,IAAIU,CAAC,CAACC,OAAO,CAACP,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAIJ,MAAM,CAACS,SAAS,CAACQ,oBAAoB,CAACN,IAAI,CAACR,CAAC,EAAEK,CAAC,CAACJ,CAAC,CAAC,CAAC,EAAEF,CAAC,CAACM,CAAC,CAACJ,CAAC,CAAC,CAAC,GAAGD,CAAC,CAACK,CAAC,CAACJ,CAAC,CAAC,CAAC;EACnG;EACA,OAAOF,CAAC;AACV;AACA,OAAOyB,eAAe,KAAK,UAAU,GAAGA,eAAe,GAAG,UAAUC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC9F,IAAIhB,CAAC,GAAG,IAAIiB,KAAK,CAACD,OAAO,CAAC;EAC1B,OAAOhB,CAAC,CAACkB,IAAI,GAAG,iBAAiB,EAAElB,CAAC,CAACc,KAAK,GAAGA,KAAK,EAAEd,CAAC,CAACe,UAAU,GAAGA,UAAU,EAAEf,CAAC;AAClF,CAAC;AACD,SAAS6K,UAAUA,CAACC,GAAG,EAAEC,UAAU,EAAE;EACnC,IAAIC,aAAa,GAAGhG,QAAQ,CAACC,aAAa,CAAC,eAAe,CAACrE,MAAM,CAACkK,GAAG,EAAE,KAAK,CAAC,CAAC;EAC9E,IAAIE,aAAa,KAAK,IAAI,EAAE,OAAO,IAAI;EACvC,IAAIC,UAAU,GAAGC,mBAAmB,CAACJ,GAAG,EAAEC,UAAU,CAAC;EACrD,IAAII,kBAAkB,GAAGH,aAAa,CAACI,SAAS,CAAC,CAAC;EAClD,OAAOD,kBAAkB,CAACE,OAAO,CAACC,OAAO;EACzC,IAAIpM,MAAM,CAACqM,IAAI,CAACJ,kBAAkB,CAACE,OAAO,CAAC,CAAC5L,MAAM,KAAKP,MAAM,CAACqM,IAAI,CAACN,UAAU,CAACI,OAAO,CAAC,CAAC5L,MAAM,EAAE;IAC7F,OAAO,IAAI;EACb;EACA,IAAI+L,YAAY,GAAG,IAAI;EACvBtM,MAAM,CAACqM,IAAI,CAACJ,kBAAkB,CAACE,OAAO,CAAC,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7D,IAAIP,kBAAkB,CAACE,OAAO,CAACK,GAAG,CAAC,KAAKT,UAAU,CAACI,OAAO,CAACK,GAAG,CAAC,EAAE;MAC/DF,YAAY,GAAG,KAAK;IACtB;EACF,CAAC,CAAC;EACF,OAAOA,YAAY,GAAGR,aAAa,GAAG,IAAI;AAC5C;AACA,SAASW,mBAAmBA,CAACtI,EAAE,EAAE;EAC/B,IAAIyH,GAAG,GAAGzH,EAAE,CAACyH,GAAG;IACdC,UAAU,GAAG1H,EAAE,CAAC0H,UAAU;IAC1Ba,SAAS,GAAGvI,EAAE,CAACuI,SAAS;IACxBC,OAAO,GAAGxI,EAAE,CAACwI,OAAO;EACtB,IAAIC,SAAS,GAAGZ,mBAAmB,CAACJ,GAAG,EAAEC,UAAU,CAAC;EACpDe,SAAS,CAACC,OAAO,GAAGF,OAAO;EAC3BC,SAAS,CAACE,MAAM,GAAGJ,SAAS;EAC5B5G,QAAQ,CAACiH,IAAI,CAACC,YAAY,CAACJ,SAAS,EAAE9G,QAAQ,CAACiH,IAAI,CAACE,iBAAiB,CAAC;AACxE;AACA,SAASC,cAAcA,CAAC/I,EAAE,EAAE;EAC1B,IAAIgJ,gBAAgB,GAAGhJ,EAAE,CAACiJ,UAAU;IAClCC,WAAW,GAAGlJ,EAAE,CAACkJ,WAAW;IAC5B/H,OAAO,GAAGoG,MAAM,CAACvH,EAAE,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;EACrD,IAAIiJ,UAAU,GAAGD,gBAAgB,IAAIG,iBAAiB,CAACD,WAAW,CAAC;EACnE,IAAIE,sBAAsB,GAAGjI,OAAO;EACpC,IAAIhB,EAAE,GAAGtE,MAAM,CAACqM,IAAI,CAACkB,sBAAsB,CAAC,CAACpI,MAAM,CAAC,UAAUqH,GAAG,EAAE;MAC/D,OAAO,OAAOe,sBAAsB,CAACf,GAAG,CAAC,KAAK,WAAW,IAAIe,sBAAsB,CAACf,GAAG,CAAC,KAAK,IAAI,IAAIe,sBAAsB,CAACf,GAAG,CAAC,KAAK,EAAE;IACzI,CAAC,CAAC,CAACgB,MAAM,CAAC,UAAUC,WAAW,EAAEjB,GAAG,EAAE;MACpC,IAAIjG,KAAK,GAAGgH,sBAAsB,CAACf,GAAG,CAAC,CAACkB,QAAQ,CAAC,CAAC;MAClDlB,GAAG,GAAGmB,oBAAoB,CAACnB,GAAG,CAAC;MAC/B,IAAIA,GAAG,CAAC3H,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,IAAI2H,GAAG,KAAK,aAAa,EAAE;QAC3DiB,WAAW,CAAC5B,UAAU,CAACW,GAAG,CAAC,GAAGjG,KAAK;MACrC,CAAC,MAAM;QACLkH,WAAW,CAACG,WAAW,CAACpB,GAAG,CAAC,GAAGjG,KAAK;MACtC;MACA,OAAOkH,WAAW;IACpB,CAAC,EAAE;MACDG,WAAW,EAAE,CAAC,CAAC;MACf/B,UAAU,EAAE,CAAC;IACf,CAAC,CAAC;IACF+B,WAAW,GAAGtJ,EAAE,CAACsJ,WAAW;IAC5B/B,UAAU,GAAGvH,EAAE,CAACuH,UAAU;EAC5B,IAAI+B,WAAW,CAAC,aAAa,CAAC,IAAIA,WAAW,CAAC,aAAa,CAAC,CAAC7M,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAChF8K,UAAU,CAAC,kBAAkB,CAAC,GAAG+B,WAAW,CAAC,aAAa,CAAC;IAC3DA,WAAW,CAAC,aAAa,CAAC,GAAG,GAAG;EAClC;EACA,OAAO;IACLhC,GAAG,EAAE,EAAE,CAAClK,MAAM,CAAC0L,UAAU,EAAE,GAAG,CAAC,CAAC1L,MAAM,CAACmM,mBAAmB,CAACD,WAAW,CAAC,CAAC;IACxE/B,UAAU,EAAEA;EACd,CAAC;AACH;AACA,SAAS8B,oBAAoBA,CAACjK,GAAG,EAAE;EACjC,IAAIoK,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAEC,YAAY,EAAE;IAC5C,OAAO,CAACA,YAAY,GAAG,GAAG,GAAG,EAAE,IAAID,KAAK,CAACE,WAAW,CAAC,CAAC;EACxD,CAAC;EACD,OAAOvK,GAAG,CAACwK,OAAO,CAAC,wBAAwB,EAAEJ,QAAQ,CAAC;AACxD;AACA,SAASD,mBAAmBA,CAACM,MAAM,EAAE;EACnC,IAAIC,WAAW,GAAG,EAAE;EACpBpO,MAAM,CAACqM,IAAI,CAAC8B,MAAM,CAAC,CAAC5B,OAAO,CAAC,UAAUC,GAAG,EAAE;IACzC,IAAI4B,WAAW,CAAC7N,MAAM,KAAK,CAAC,EAAE6N,WAAW,IAAI,GAAG;IAChDA,WAAW,IAAI5B,GAAG,GAAG,GAAG,GAAG2B,MAAM,CAAC3B,GAAG,CAAC;EACxC,CAAC,CAAC;EACF,OAAO4B,WAAW;AACpB;AACA,SAASd,iBAAiBA,CAACD,WAAW,EAAE;EACtC,OAAOA,WAAW,KAAK,SAAS,GAAG,uCAAuC,GAAG,+BAA+B;AAC9G;AACA,SAASrB,mBAAmBA,CAACJ,GAAG,EAAEC,UAAU,EAAE;EAC5C,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;IACzBA,UAAU,GAAG,CAAC,CAAC;EACjB;EACA,IAAIe,SAAS,GAAG9G,QAAQ,CAACwF,aAAa,CAAC,QAAQ,CAAC;EAChDsB,SAAS,CAACyB,GAAG,GAAGzC,GAAG;EACnB5L,MAAM,CAACqM,IAAI,CAACR,UAAU,CAAC,CAACU,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC7CI,SAAS,CAAC0B,YAAY,CAAC9B,GAAG,EAAEX,UAAU,CAACW,GAAG,CAAC,CAAC;IAC5C,IAAIA,GAAG,KAAK,gBAAgB,EAAE;MAC5BI,SAAS,CAAC0B,YAAY,CAAC,OAAO,EAAEzC,UAAU,CAAC,gBAAgB,CAAC,CAAC;IAC/D;EACF,CAAC,CAAC;EACF,OAAOe,SAAS;AAClB;AACA,SAAS2B,UAAUA,CAACjJ,OAAO,EAAEkJ,eAAe,EAAE;EAC5C,IAAIA,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAGC,OAAO;EAC3B;EACAC,iBAAiB,CAACpJ,OAAO,EAAEkJ,eAAe,CAAC;EAC3C,IAAI,OAAO1I,QAAQ,KAAK,WAAW,EAAE,OAAO0I,eAAe,CAACG,OAAO,CAAC,IAAI,CAAC;EACzE,IAAIxK,EAAE,GAAG+I,cAAc,CAAC5H,OAAO,CAAC;IAC9BsG,GAAG,GAAGzH,EAAE,CAACyH,GAAG;IACZC,UAAU,GAAG1H,EAAE,CAAC0H,UAAU;EAC5B,IAAIvI,SAAS,GAAGuI,UAAU,CAAC,gBAAgB,CAAC,IAAI,QAAQ;EACxD,IAAI+C,uBAAuB,GAAGC,wBAAwB,CAACvL,SAAS,CAAC;EACjE,IAAI,CAACuI,UAAU,CAAC,qBAAqB,CAAC,EAAE;IACtCA,UAAU,CAAC,qBAAqB,CAAC,GAAG,WAAW;EACjD;EACA,IAAIF,UAAU,CAACC,GAAG,EAAEC,UAAU,CAAC,IAAI+C,uBAAuB,EAAE;IAC1D,OAAOJ,eAAe,CAACG,OAAO,CAACC,uBAAuB,CAAC;EACzD;EACA,OAAOE,gBAAgB,CAAC;IACtBlD,GAAG,EAAEA,GAAG;IACRC,UAAU,EAAEA;EACd,CAAC,EAAE2C,eAAe,CAAC,CAACO,IAAI,CAAC,YAAY;IACnC,IAAIC,kBAAkB,GAAGH,wBAAwB,CAACvL,SAAS,CAAC;IAC5D,IAAI0L,kBAAkB,EAAE;MACtB,OAAOA,kBAAkB;IAC3B;IACA,MAAM,IAAIjN,KAAK,CAAC,aAAa,CAACL,MAAM,CAAC4B,SAAS,EAAE,oCAAoC,CAAC,CAAC;EACxF,CAAC,CAAC;AACJ;AACA,SAASwL,gBAAgBA,CAACxJ,OAAO,EAAEkJ,eAAe,EAAE;EAClD,IAAIA,eAAe,KAAK,KAAK,CAAC,EAAE;IAC9BA,eAAe,GAAGC,OAAO;EAC3B;EACAC,iBAAiB,CAACpJ,OAAO,EAAEkJ,eAAe,CAAC;EAC3C,IAAI5C,GAAG,GAAGtG,OAAO,CAACsG,GAAG;IACnBC,UAAU,GAAGvG,OAAO,CAACuG,UAAU;EACjC,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACrL,MAAM,KAAK,CAAC,EAAE;IAC/C,MAAM,IAAIwB,KAAK,CAAC,cAAc,CAAC;EACjC;EACA,IAAI,OAAO8J,UAAU,KAAK,WAAW,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;IACvE,MAAM,IAAI9J,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,OAAO,IAAIyM,eAAe,CAAC,UAAUG,OAAO,EAAEM,MAAM,EAAE;IACpD,IAAI,OAAOnJ,QAAQ,KAAK,WAAW,EAAE,OAAO6I,OAAO,CAAC,CAAC;IACrDlC,mBAAmB,CAAC;MAClBb,GAAG,EAAEA,GAAG;MACRC,UAAU,EAAEA,UAAU;MACtBa,SAAS,EAAE,SAAAA,CAAA,EAAY;QACrB,OAAOiC,OAAO,CAAC,CAAC;MAClB,CAAC;MACDhC,OAAO,EAAE,SAAAA,CAAA,EAAY;QACnB,IAAIuC,YAAY,GAAG,IAAInN,KAAK,CAAC,eAAe,CAACL,MAAM,CAACkK,GAAG,EAAE,4FAA4F,CAAC,CAAC;QACvJ,OAAOqD,MAAM,CAACC,YAAY,CAAC;MAC7B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA,SAASL,wBAAwBA,CAACvL,SAAS,EAAE;EAC3C,OAAOC,MAAM,CAACD,SAAS,CAAC;AAC1B;AACA,SAASoL,iBAAiBA,CAACpJ,OAAO,EAAEkJ,eAAe,EAAE;EACnD,IAAI,OAAOlJ,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE;IACnD,MAAM,IAAIvD,KAAK,CAAC,6BAA6B,CAAC;EAChD;EACA,IAAIsL,WAAW,GAAG/H,OAAO,CAAC+H,WAAW;EACrC,IAAIA,WAAW,IAAIA,WAAW,KAAK,YAAY,IAAIA,WAAW,KAAK,SAAS,EAAE;IAC5E,MAAM,IAAItL,KAAK,CAAC,oEAAoE,CAAC;EACvF;EACA,IAAI,OAAOyM,eAAe,KAAK,WAAW,IAAI,OAAOA,eAAe,KAAK,UAAU,EAAE;IACnF,MAAM,IAAIzM,KAAK,CAAC,4CAA4C,CAAC;EAC/D;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIoN,yBAAyB,GAAG,SAAAA,CAAUC,eAAe,EAAE;EACzD,IAAIjL,EAAE,EAAEG,EAAE;EACV,IAAI,QAAQ,CAACH,EAAE,GAAGiL,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACC,MAAM,MAAM,IAAI,IAAIlL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmL,MAAM,CAAC,KAAK,UAAU,IAAI,QAAQ,CAAChL,EAAE,GAAG8K,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACG,cAAc,MAAM,IAAI,IAAIjL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgL,MAAM,CAAC,KAAK,UAAU,EAAE;IAC5V,MAAM,IAAIvN,KAAK,CAAC,yEAAyE,CAAC;EAC5F;EACA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIyN,eAAe,GAAG,SAAAA,CAAUtG,WAAW,EAAEuG,sBAAsB,EAAE;EACnE,IAAIC,cAAc,GAAGxG,WAAW,CAACyG,WAAW;EAC5C,IAAIC,yBAAyB,GAAG1G,WAAW,CAAC2G,sBAAsB;EAClE,IAAIC,YAAY,GAAG5G,WAAW,CAAC6G,SAAS;EACxC,IAAI,OAAOL,cAAc,KAAK,UAAU,EAAE;IACxCxG,WAAW,CAACyG,WAAW,GAAG,UAAU7E,IAAI,EAAEC,OAAO,EAAE;MACjD,OAAO2E,cAAc,CAAC5E,IAAI,EAAE/K,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgL,OAAO,CAAC,EAAE;QAC1DiF,SAAS,EAAEP;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EACA,IAAI,OAAOG,yBAAyB,KAAK,UAAU,EAAE;IACnD1G,WAAW,CAAC2G,sBAAsB,GAAG,UAAU/E,IAAI,EAAEC,OAAO,EAAE;MAC5D,OAAO6E,yBAAyB,CAAC9E,IAAI,EAAE/K,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgL,OAAO,CAAC,EAAE;QACrEiF,SAAS,EAAEP;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EACA,IAAI,OAAOK,YAAY,KAAK,UAAU,EAAE;IACtC5G,WAAW,CAAC6G,SAAS,GAAG,UAAUjF,IAAI,EAAEC,OAAO,EAAE;MAC/C,OAAO+E,YAAY,CAAChF,IAAI,EAAE/K,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgL,OAAO,CAAC,EAAE;QACxDiF,SAAS,EAAEP;MACb,CAAC,CAAC,CAAC;IACL,CAAC;EACH;EACA,OAAO1P,QAAQ,CAAC,CAAC,CAAC,EAAEmJ,WAAW,CAAC;AAClC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI+G,qBAAqB,GAAG,SAAAA,CAAUb,eAAe,EAAE;EACrD,IAAIA,eAAe,IAAID,yBAAyB,CAACC,eAAe,CAAC,EAAE;IACjE,OAAOX,OAAO,CAACE,OAAO,CAACS,eAAe,CAAC;EACzC;EACA,OAAOX,OAAO,CAACyB,GAAG,CAAC,CAACpB,gBAAgB,CAAC;IACnClD,GAAG,EAAEhJ;EACP,CAAC,CAAC,EAAEkM,gBAAgB,CAAC;IACnBlD,GAAG,EAAE/I;EACP,CAAC,CAAC,CAAC,CAAC,CAACkM,IAAI,CAAC,YAAY;IACpB,OAAOvL,2BAA2B,CAAC,CAAC;EACtC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI2M,sBAAsB,GAAG,SAAAA,CAAUhM,EAAE,EAAE;EACzC,IAAIG,EAAE,GAAGH,EAAE,CAACyE,SAAS;IACnBA,SAAS,GAAGtE,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACnCE,EAAE,GAAGL,EAAE,CAAC2E,QAAQ;IAChBA,QAAQ,GAAGtE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACrCuE,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACtBF,EAAE,GAAG1E,EAAE,CAAC8E,aAAa;IACrBA,aAAa,GAAGJ,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACvCuH,kBAAkB,GAAGjM,EAAE,CAACiM,kBAAkB;IAC1CC,iBAAiB,GAAGlM,EAAE,CAACkM,iBAAiB;IACxCnH,WAAW,GAAGrI,QAAQ,CAACsD,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,oBAAoB,EAAE,mBAAmB,CAAC,CAAC;EAC/H,IAAI6E,EAAE,GAAGvJ,QAAQ,CAAC,IAAI,CAAC;IACrByK,aAAa,GAAGlB,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIU,EAAE,GAAG9B,wBAAwB,CAAC,CAAC;IACjC0I,eAAe,GAAG5G,EAAE,CAAC,CAAC,CAAC;IACvBzC,QAAQ,GAAGyC,EAAE,CAAC,CAAC,CAAC;EAClBhK,SAAS,CAAC,YAAY;IACpBuQ,qBAAqB,CAACG,kBAAkB,CAAC,CAACrB,IAAI,CAAC,UAAUiB,SAAS,EAAE;MAClE,IAAIO,qBAAqB,GAAGD,eAAe,CAAChL,OAAO,CAACpD,YAAY,CAACM,kBAAkB,CAAC;MACpF,IAAIgO,WAAW,GAAGF,eAAe,CAAChL,OAAO,CAACpD,YAAY,CAACC,iBAAiB,CAAC;MACzE,OAAO6N,SAAS,CAACX,MAAM,CAACC,MAAM,CAAC;QAC7BmB,aAAa,EAAEF,qBAAqB,IAAIC;MAC1C,CAAC,CAAC,CAACzB,IAAI,CAAC,UAAU2B,cAAc,EAAE;QAChC,IAAIC,YAAY,GAAGN,iBAAiB,GAAG;UACrCA,iBAAiB,EAAEA;QACrB,CAAC,GAAG,CAAC,CAAC;QACN,OAAOL,SAAS,CAACT,cAAc,CAACD,MAAM,CAACvP,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4Q,YAAY,CAAC,EAAE;UAC1EtB,MAAM,EAAEqB;QACV,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC3B,IAAI,CAAC,UAAU6B,sBAAsB,EAAE;QACxC3J,QAAQ,CAAC;UACPZ,IAAI,EAAExG,eAAe,CAAC+G,sBAAsB;UAC5CL,KAAK,EAAEqK;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC,CAACvG,KAAK,CAAC,UAAUY,GAAG,EAAE;MACtBf,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAAC,EAAE,CAACL,MAAM,CAACe,iBAAiB,EAAE,GAAG,CAAC,CAACf,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAChE,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;EACF,CAAC,EAAE,CAACqF,eAAe,CAAChL,OAAO,CAAC,CAAC;EAC7B,OAAOjG,KAAK,CAACiM,aAAa,CAACjM,KAAK,CAACkM,QAAQ,EAAE,IAAI,EAAE+E,eAAe,CAACzJ,+BAA+B,IAAIxH,KAAK,CAACiM,aAAa,CAAC3C,aAAa,EAAE5I,QAAQ,CAAC;IAC9I6I,SAAS,EAAEA,SAAS;IACpBE,QAAQ,EAAEA,QAAQ;IAClBG,aAAa,EAAEA;EACjB,CAAC,EAAEuG,eAAe,CAACtG,WAAW,EAAEoH,eAAe,CAACzJ,+BAA+B,CAAC,CAAC,EAAEkC,QAAQ,CAAC,CAAC;AAC/F,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI8H,WAAW,GAAG,SAAAA,CAAU1M,EAAE,EAAE;EAC9B,IAAIG,EAAE,GAAGH,EAAE,CAACyE,SAAS;IACnBA,SAAS,GAAGtE,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACnCyE,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACtB+H,SAAS,GAAGjQ,QAAQ,CAACsD,EAAE,EAAE,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;EACrD,IAAIK,EAAE,GAAG2C,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCK,UAAU,GAAGhD,EAAE,CAACgD,UAAU;IAC1BlC,OAAO,GAAGd,EAAE,CAACc,OAAO;EACtB,IAAIyL,gBAAgB,GAAGvR,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIqJ,EAAE,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;IACrBsK,UAAU,GAAGlB,EAAE,CAAC,CAAC,CAAC;IAClBmB,aAAa,GAAGnB,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIG,EAAE,GAAGvJ,QAAQ,CAAC,IAAI,CAAC;IACrByK,aAAa,GAAGlB,EAAE,CAAC,CAAC,CAAC;EACvB;AACF;AACA;EACE,IAAIgI,gBAAgB,GAAG,SAAAA,CAAUC,IAAI,EAAE;IACrC,IAAIvI,OAAO,GAAGqI,gBAAgB,CAACrI,OAAO;IACtC;IACA,IAAI,CAACA,OAAO,IAAI,CAACuI,IAAI,CAAClH,UAAU,CAAC,CAAC,EAAE;MAClC,OAAOC,aAAa,CAAC,KAAK,CAAC;IAC7B;IACA;IACA,IAAItB,OAAO,CAACwI,UAAU,EAAE;MACtBxI,OAAO,CAACzC,WAAW,CAACyC,OAAO,CAACwI,UAAU,CAAC;IACzC;IACAD,IAAI,CAAC/F,MAAM,CAACxC,OAAO,CAAC,CAAC2B,KAAK,CAAC,UAAUY,GAAG,EAAE;MACxC;MACA,IAAIvC,OAAO,KAAK,IAAI,IAAIA,OAAO,CAACK,QAAQ,CAACxI,MAAM,KAAK,CAAC,EAAE;QACrD;QACA;MACF;MACA;MACA2J,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAAC,8CAA8C,CAACL,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAC7E,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDvL,SAAS,CAAC,YAAY;IACpB;IACA,IAAI8H,UAAU,KAAK,KAAK,EAAE;MACxB;IACF;IACA,IAAI+C,qBAAqB,GAAGlH,0BAA0B,CAACiC,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,CAAC;IAC5F;IACA,IAAIiI,qBAAqB,KAAKE,SAAS,IAAIF,qBAAqB,CAAC4G,KAAK,KAAK1G,SAAS,EAAE;MACpF,OAAOP,aAAa,CAAC,YAAY;QAC/B,MAAM,IAAInI,KAAK,CAACmC,oBAAoB,CAAC;UACnCE,kBAAkB,EAAEyM,WAAW,CAAClG,WAAW;UAC3CtG,eAAe,EAAE,OAAO;UACxBE,sBAAsB,EAAEe,OAAO,CAACsF,UAAU;UAC1CnG,gBAAgB,EAAEa,OAAO,CAACpD,YAAY,CAACI,cAAc;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IACA0O,gBAAgB,CAACzG,qBAAqB,CAAC4G,KAAK,CAACpR,QAAQ,CAAC,CAAC,CAAC,EAAE+Q,SAAS,CAAC,CAAC,CAAC;IACtE;EACF,CAAC,EAAE,CAACtJ,UAAU,EAAEsJ,SAAS,CAAC3F,aAAa,CAAC,CAAC;EACzC,OAAO9L,KAAK,CAACiM,aAAa,CAACjM,KAAK,CAACkM,QAAQ,EAAE,IAAI,EAAExB,UAAU,GAAG1K,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IACvFE,GAAG,EAAEuF,gBAAgB;IACrBnI,SAAS,EAAEA;EACb,CAAC,CAAC,GAAGG,QAAQ,CAAC;AAChB,CAAC;AACD8H,WAAW,CAAClG,WAAW,GAAG,aAAa;;AAEvC;AACA;AACA;AACA;AACA,IAAIyG,cAAc,GAAG,SAAAA,CAAUjN,EAAE,EAAE;EACjC,IAAIG,EAAE,GAAGH,EAAE,CAACyE,SAAS;IACnBA,SAAS,GAAGtE,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACnCE,EAAE,GAAGL,EAAE,CAAC8E,aAAa;IACrBA,aAAa,GAAGzE,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACvC6M,YAAY,GAAGxQ,QAAQ,CAACsD,EAAE,EAAE,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;EAC7D,IAAI0E,EAAE,GAAG1B,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCK,UAAU,GAAGqB,EAAE,CAACrB,UAAU;IAC1BlC,OAAO,GAAGuD,EAAE,CAACvD,OAAO;EACtB,IAAIgM,oBAAoB,GAAG9R,MAAM,CAAC,IAAI,CAAC;EACvC,IAAI+R,QAAQ,GAAG/R,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIwJ,EAAE,GAAGvJ,QAAQ,CAAC,IAAI,CAAC;IACrByK,aAAa,GAAGlB,EAAE,CAAC,CAAC,CAAC;EACvBtJ,SAAS,CAAC,YAAY;IACpB;IACA,IAAI8H,UAAU,KAAK,KAAK,EAAE;MACxB;IACF;IACA,IAAI+C,qBAAqB,GAAGlH,0BAA0B,CAACiC,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,CAAC;IAC5F;IACA,IAAIiI,qBAAqB,KAAKE,SAAS,IAAIF,qBAAqB,CAACiH,QAAQ,KAAK/G,SAAS,EAAE;MACvF,OAAOP,aAAa,CAAC,YAAY;QAC/B,MAAM,IAAInI,KAAK,CAACmC,oBAAoB,CAAC;UACnCE,kBAAkB,EAAEgN,cAAc,CAACzG,WAAW;UAC9CtG,eAAe,EAAE,UAAU;UAC3BE,sBAAsB,EAAEe,OAAO,CAACsF,UAAU;UAC1CnG,gBAAgB,EAAEa,OAAO,CAACpD,YAAY,CAACI,cAAc;QACvD,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;IACAiP,QAAQ,CAAC7I,OAAO,GAAG6B,qBAAqB,CAACiH,QAAQ,CAACzR,QAAQ,CAAC,CAAC,CAAC,EAAEsR,YAAY,CAAC,CAAC;IAC7EE,QAAQ,CAAC7I,OAAO,CAACwC,MAAM,CAACoG,oBAAoB,CAAC5I,OAAO,CAAC,CAAC2B,KAAK,CAAC,UAAUY,GAAG,EAAE;MACzE;MACA,IAAIqG,oBAAoB,CAAC5I,OAAO,KAAK,IAAI,IAAI4I,oBAAoB,CAAC5I,OAAO,CAACK,QAAQ,CAACxI,MAAM,KAAK,CAAC,EAAE;QAC/F;QACA;MACF;MACA;MACA2J,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAAC,iDAAiD,CAACL,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAChF,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;EACF,CAAC,EAAE/J,aAAa,CAAC,CAACsG,UAAU,CAAC,EAAEyB,aAAa,EAAE,IAAI,CAAC,CAAC;EACpD,OAAO5J,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChCE,GAAG,EAAE8F,oBAAoB;IACzB1I,SAAS,EAAEA;EACb,CAAC,CAAC;AACJ,CAAC;AACDwI,cAAc,CAACzG,WAAW,GAAG,gBAAgB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA,IAAI8G,oBAAoB,GAAG,SAAAA,CAAUtN,EAAE,EAAE;EACvC,IAAIG,EAAE;EACN,IAAIE,EAAE,GAAGL,EAAE,CAACmB,OAAO;IACjBA,OAAO,GAAGd,EAAE,KAAK,KAAK,CAAC,GAAG;MACxBkN,QAAQ,EAAE;IACZ,CAAC,GAAGlN,EAAE;IACNuE,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACtBF,EAAE,GAAG1E,EAAE,CAACwN,YAAY;IACpBA,YAAY,GAAG9I,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;EAC3C,IAAIG,EAAE,GAAGrJ,UAAU,CAACuG,aAAa,EAAE;MAC/BZ,OAAO,EAAEvF,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEuF,OAAO,CAAC,GAAGhB,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAACpC,YAAY,CAACE,mBAAmB,CAAC,GAAGF,YAAY,CAACG,kBAAkB,EAAEiC,EAAE,CAACpC,YAAY,CAACK,2BAA2B,CAAC,GAAGL,YAAY,CAACG,kBAAkB,EAAEiC,EAAE,CAACrC,SAAS,CAAC,GAAG,EAAE,CAACP,MAAM,CAAC2D,WAAW,CAACC,OAAO,CAAC,CAAC,EAAEhB,EAAE,CAAC,CAAC;MAChQkC,aAAa,EAAEmL,YAAY,GAAG/R,oBAAoB,CAAC0H,OAAO,GAAG1H,oBAAoB,CAAC+G;IACpF,CAAC,CAAC;IACFR,KAAK,GAAG6C,EAAE,CAAC,CAAC,CAAC;IACb/B,QAAQ,GAAG+B,EAAE,CAAC,CAAC,CAAC;EAClBtJ,SAAS,CAAC,YAAY;IACpB,IAAIiS,YAAY,KAAK,KAAK,IAAIxL,KAAK,CAACK,aAAa,KAAK5G,oBAAoB,CAAC0H,OAAO,EAAE;MAClF,OAAOL,QAAQ,CAAC;QACdZ,IAAI,EAAExG,eAAe,CAACyG,cAAc;QACpCC,KAAK,EAAE3G,oBAAoB,CAAC+G;MAC9B,CAAC,CAAC;IACJ;IACA,IAAIR,KAAK,CAACK,aAAa,KAAK5G,oBAAoB,CAAC+G,OAAO,EAAE;MACxD;IACF;IACA,IAAIiL,YAAY,GAAG,IAAI;IACvBrD,UAAU,CAACpI,KAAK,CAACb,OAAO,CAAC,CAACyJ,IAAI,CAAC,YAAY;MACzC,IAAI6C,YAAY,EAAE;QAChB3K,QAAQ,CAAC;UACPZ,IAAI,EAAExG,eAAe,CAACyG,cAAc;UACpCC,KAAK,EAAE3G,oBAAoB,CAAC6H;QAC9B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,CAAC4C,KAAK,CAAC,UAAUY,GAAG,EAAE;MACtB4G,OAAO,CAACjQ,KAAK,CAAC,EAAE,CAACF,MAAM,CAACe,iBAAiB,EAAE,GAAG,CAAC,CAACf,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAC5D,IAAI2G,YAAY,EAAE;QAChB3K,QAAQ,CAAC;UACPZ,IAAI,EAAExG,eAAe,CAACyG,cAAc;UACpCC,KAAK,EAAE;YACLJ,KAAK,EAAEvG,oBAAoB,CAAC+H,QAAQ;YACpC7F,OAAO,EAAEgC,MAAM,CAACmH,GAAG;UACrB;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACF,OAAO,YAAY;MACjB2G,YAAY,GAAG,KAAK;IACtB,CAAC;EACH,CAAC,EAAE,CAACzL,KAAK,CAACb,OAAO,EAAEqM,YAAY,EAAExL,KAAK,CAACK,aAAa,CAAC,CAAC;EACtD,OAAOnH,KAAK,CAACiM,aAAa,CAACxE,aAAa,CAACgL,QAAQ,EAAE;IACjDvL,KAAK,EAAExG,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEoG,KAAK,CAAC,EAAE;MACnCc,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,EAAE8B,QAAQ,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgJ,uBAAuB,GAAG,SAAAA,CAAUC,YAAY,EAAE;EACpD,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,CAAC,CAAC;EACnB;EACA,IAAIC,gBAAgB,GAAGzS,MAAM,CAACwS,YAAY,CAAC;EAC3C,IAAIE,mBAAmB,GAAG,SAAAA,CAAUC,SAAS,EAAE;IAC7CF,gBAAgB,CAACvJ,OAAO,GAAG3I,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEkS,gBAAgB,CAACvJ,OAAO,CAAC,EAAEyJ,SAAS,CAAC;EACxF,CAAC;EACD,OAAO,CAACF,gBAAgB,EAAEC,mBAAmB,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,gCAAgC,GAAG,SAAAA,CAAUjO,EAAE,EAAE;EACnD,IAAIG,EAAE,GAAGH,EAAE,CAACyG,UAAU;IACpBA,UAAU,GAAGtG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACpCE,EAAE,GAAGtC,YAAY,CAACI,cAAc;IAChCuG,EAAE,GAAG1E,EAAE,CAACK,EAAE,CAAC;IACXgG,aAAa,GAAG3B,EAAE,KAAK,KAAK,CAAC,GAAG/F,wBAAwB,GAAG+F,EAAE;EAC/D,IAAI3D,kBAAkB,GAAG0F,UAAU,GAAG,EAAE,CAAClJ,MAAM,CAACkJ,UAAU,EAAE,gBAAgB,CAAC,GAAG,eAAe;EAC/F,IAAI9F,YAAY,GAAG,iEAAiE,CAACpD,MAAM,CAAC8I,aAAa,EAAE,6BAA6B,CAAC;EACzI,IAAI,CAACI,UAAU,CAAC3F,QAAQ,CAAC,eAAe,CAAC,EAAE;IACzCH,YAAY,IAAI,6JAA6J,CAACpD,MAAM,CAACwD,kBAAkB,EAAE,MAAM,CAAC;EAClN;EACA,OAAOJ,YAAY;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIuN,sBAAsB,GAAG,SAAAA,CAAUC,aAAa,EAAE;EACpD,OAAO,CAACA,aAAa,CAACrN,QAAQ,CAACnF,0BAA0B,CAACyS,eAAe,CAAC,IAAI,CAACD,aAAa,CAACrN,QAAQ,CAACnF,0BAA0B,CAAC0S,gBAAgB,CAAC,IAAI,CAACF,aAAa,CAACrN,QAAQ,CAACnF,0BAA0B,CAAC2S,eAAe,CAAC;AAC3N,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,kBAAkB,GAAG,SAAAA,CAAUJ,aAAa,EAAE;EAChD,IAAI,CAACA,aAAa,CAACrN,QAAQ,CAACnF,0BAA0B,CAAC6S,MAAM,CAAC,IAAI,CAACL,aAAa,CAACrN,QAAQ,CAACnF,0BAA0B,CAAC8S,GAAG,CAAC,IAAIP,sBAAsB,CAACC,aAAa,CAAC,EAAE;IAClK,MAAM,IAAIvQ,KAAK,CAACiB,4BAA4B,CAAC;EAC/C;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,IAAI6P,mBAAmB,GAAG,SAAAA,CAAUP,aAAa,EAAE;EACjD,IAAIA,aAAa,CAAC/R,MAAM,KAAK,IAAIuS,GAAG,CAACR,aAAa,CAAC,CAACS,IAAI,EAAE;IACxD,MAAM,IAAIhR,KAAK,CAACkB,sCAAsC,CAAC;EACzD;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI+P,2BAA2B,GAAG,SAAAA,CAAUf,gBAAgB,EAAE;EAC5DS,kBAAkB,CAACT,gBAAgB,CAAC;EACpCY,mBAAmB,CAACZ,gBAAgB,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIgB,0BAA0B,GAAG,SAAAA,CAAU9O,EAAE,EAAE;EAC7C,IAAI+O,MAAM,GAAG/O,EAAE,CAAC+O,MAAM;IACpBvD,WAAW,GAAGxL,EAAE,CAACwL,WAAW;IAC5BwD,gBAAgB,GAAGhP,EAAE,CAACgP,gBAAgB;IACtCpK,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACtBqK,YAAY,GAAGjP,EAAE,CAACiP,YAAY;EAChC,IAAI9O,EAAE,GAAGsD,wBAAwB,CAAC,CAAC,CAAC,CAAC,CAAC;IACpCtC,OAAO,GAAGhB,EAAE,CAACgB,OAAO;IACpBkB,aAAa,GAAGlC,EAAE,CAACkC,aAAa;EAClC,IAAIhC,EAAE,GAAG/E,QAAQ,CAAC,IAAI,CAAC;IACrBsK,UAAU,GAAGvF,EAAE,CAAC,CAAC,CAAC;IAClBwF,aAAa,GAAGxF,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIqE,EAAE,GAAGpJ,QAAQ,CAAC,CAAC;IACjB4T,UAAU,GAAGxK,EAAE,CAAC,CAAC,CAAC;IAClByK,aAAa,GAAGzK,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIG,EAAE,GAAGvJ,QAAQ,CAAC,IAAI,CAAC;IACrByK,aAAa,GAAGlB,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIuK,wBAAwB,GAAG/T,MAAM,CAAC,IAAI,CAAC;EAC3C,IAAIgU,YAAY,GAAGhU,MAAM,CAAC,CAAC;EAC3B,IAAIkK,EAAE,GAAGqI,uBAAuB,CAAC,CAAC;IAChCE,gBAAgB,GAAGvI,EAAE,CAAC,CAAC,CAAC;IACxBwI,mBAAmB,GAAGxI,EAAE,CAAC,CAAC,CAAC;EAC7BhK,SAAS,CAAC,YAAY;IACpB,IAAIyE,EAAE;IACN6O,2BAA2B,CAAChT,MAAM,CAACqM,IAAI,CAAC4F,gBAAgB,CAACvJ,OAAO,CAAC,CAAC;IAClE;IACA,IAAI,EAAElC,aAAa,KAAK5G,oBAAoB,CAAC6H,QAAQ,CAAC,EAAE;MACtD;IACF;IACA;IACA+L,YAAY,CAAC9K,OAAO,GAAGrF,0BAA0B,CAACiC,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,CAAC,CAACmR,YAAY;IACpG,IAAI,CAACD,YAAY,CAAC9K,OAAO,EAAE;MACzB,MAAM,IAAI3G,KAAK,CAACqQ,gCAAgC,EAAEjO,EAAE,GAAG;QACrDyG,UAAU,EAAEtF,OAAO,CAACsF;MACtB,CAAC,EAAEzG,EAAE,CAACjC,YAAY,CAACI,cAAc,CAAC,GAAGgD,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,EAAE6B,EAAE,CAAC,CAAC,CAAC;IAClF;IACA,IAAI,CAACqP,YAAY,CAAC9K,OAAO,CAACqB,UAAU,CAAC,CAAC,EAAE;MACtC,OAAOC,aAAa,CAAC,KAAK,CAAC;IAC7B;IACA;IACA,IAAIqJ,UAAU,EAAE;MACdA,UAAU,CAACK,QAAQ,CAAC,CAAC;IACvB;IACAF,YAAY,CAAC9K,OAAO,CAACwC,MAAM,CAAC;MAC1B;MACAyE,WAAW,EAAEA,WAAW;MACxBgE,MAAM,EAAE1B,gBAAgB,CAACvJ,OAAO;MAChC0K,YAAY,EAAEA,YAAY;MAC1BF,MAAM,EAAEA;IACV,CAAC,CAAC,CAACnE,IAAI,CAAC,UAAU6E,kBAAkB,EAAE;MACpC,IAAIL,wBAAwB,CAAC7K,OAAO,EAAE;QACpC4K,aAAa,CAACM,kBAAkB,CAAC;MACnC;IACF,CAAC,CAAC,CAACvJ,KAAK,CAAC,UAAUY,GAAG,EAAE;MACtBf,aAAa,CAAC,YAAY;QACxB,MAAM,IAAInI,KAAK,CAAC,6DAA6D,CAACL,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAC5F,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACzE,aAAa,EAAE0M,MAAM,CAAC,CAAC,CAAC,CAAC;EAC7B,OAAO7T,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChCE,GAAG,EAAE+H;EACP,CAAC,EAAExJ,UAAU,GAAG1K,KAAK,CAACiM,aAAa,CAACzD,yBAAyB,CAACiK,QAAQ,EAAE;IACtEvL,KAAK,EAAE;MACL8M,UAAU,EAAEA,UAAU;MACtBnB,mBAAmB,EAAEA;IACvB;EACF,CAAC,EAAEnJ,QAAQ,CAAC,GAAGoK,gBAAgB,CAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIU,iBAAiB,GAAG,SAAAA,CAAU1P,EAAE,EAAE;EACpC,IAAI2P,eAAe,GAAG3P,EAAE,CAAC2P,eAAe;IACtC;IACAxO,OAAO,GAAGnB,EAAE,CAACmB,OAAO;IACpB;IACA0C,KAAK,GAAGnH,QAAQ,CAACsD,EAAE,EAAE,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;EACtD,IAAI4P,kBAAkB,GAAGxU,UAAU,CAACsI,yBAAyB,CAAC;EAC9DnI,SAAS,CAAC,YAAY;IACpB,IAAIyE,EAAE;IACN,IAAI,EAAE4P,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAAC7B,mBAAmB,CAAC,EAAE;MACrH,MAAM,IAAInQ,KAAK,CAAC,uFAAuF,CAAC;IAC1G;IACA;IACAgS,kBAAkB,CAAC7B,mBAAmB,EAAE/N,EAAE,GAAG,CAAC,CAAC,EAAEA,EAAE,CAAC2P,eAAe,CAAC,GAAG;MACrEE,QAAQ,EAAE1O,OAAO,CAAC0O,QAAQ;MAC1BC,WAAW,EAAE3O,OAAO,CAAC2O,WAAW;MAChC5N,IAAI,EAAEf,OAAO,CAACe,IAAI;MAClB6N,WAAW,EAAE5O,OAAO,CAAC4O,WAAW;MAChCC,SAAS,EAAE7O,OAAO,CAAC6O,SAAS;MAC5BC,MAAM,EAAE9O,OAAO,CAAC8O,MAAM;MACtBC,SAAS,EAAE/O,OAAO,CAAC+O,SAAS;MAC5BC,SAAS,EAAEhP,OAAO,CAACgP,SAAS;MAC5BC,OAAO,EAAEjP,OAAO,CAACiP,OAAO;MACxBC,sBAAsB,EAAElP,OAAO,CAACkP;IAClC,CAAC,EAAErQ,EAAE,CAAC,CAAC;EACT,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACR,OAAO9E,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAEvL,QAAQ,CAAC,CAAC,CAAC,EAAEiI,KAAK,CAAC,CAAC;AACxD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIyM,8BAA8B,GAAG,SAAAA,CAAUtQ,EAAE,EAAE;EACjD,IAAIG,EAAE,GAAGH,EAAE,CAACyG,UAAU;IACpBA,UAAU,GAAGtG,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACpCE,EAAE,GAAGtC,YAAY,CAACI,cAAc;IAChCuG,EAAE,GAAG1E,EAAE,CAACK,EAAE,CAAC;IACXgG,aAAa,GAAG3B,EAAE,KAAK,KAAK,CAAC,GAAG/F,wBAAwB,GAAG+F,EAAE;EAC/D,IAAI3D,kBAAkB,GAAG0F,UAAU,GAAG,EAAE,CAAClJ,MAAM,CAACkJ,UAAU,EAAE,cAAc,CAAC,GAAG,aAAa;EAC3F,IAAI9F,YAAY,GAAG,+DAA+D,CAACpD,MAAM,CAAC8I,aAAa,EAAE,2BAA2B,CAAC;EACrI,IAAI,CAACI,UAAU,CAAC3F,QAAQ,CAAC,aAAa,CAAC,EAAE;IACvCH,YAAY,IAAI,2JAA2J,CAACpD,MAAM,CAACwD,kBAAkB,EAAE,MAAM,CAAC;EAChN;EACA,OAAOJ,YAAY;AACrB,CAAC;AACD,SAAS4P,MAAMA,CAAA,EAAG;EAChB;AACF;AACA,SAASC,WAAWA,CAACC,SAAS,EAAE;EAC9B,IAAIzQ,EAAE;EACN,OAAO,CAAC,EAAE,CAACA,EAAE,GAAGyQ,SAAS,CAAClM,OAAO,MAAM,IAAI,IAAIvE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4E,QAAQ,CAACxI,MAAM,CAAC;AAC7F;AACA,IAAIsU,uBAAuB,GAAGvV,aAAa,CAAC;EAC1CwV,cAAc,EAAE,IAAI;EACpBnB,MAAM,EAAE,CAAC,CAAC;EACVoB,aAAa,EAAEL,MAAM;EACrBM,eAAe,EAAEN,MAAM,CAAC;AAC1B,CAAC,CAAC;AACF,IAAIO,mBAAmB,GAAG,SAAAA,CAAA,EAAY;EACpC,OAAO1V,UAAU,CAACsV,uBAAuB,CAAC;AAC5C,CAAC;AACD,IAAIK,2BAA2B,GAAG,SAAAA,CAAA,EAAY;EAC5C,IAAI/Q,EAAE,GAAG1E,QAAQ,CAAC,IAAI,CAAC;IACrB0V,QAAQ,GAAGhR,EAAE,CAAC,CAAC,CAAC;EAClB,IAAI8N,gBAAgB,GAAGzS,MAAM,CAAC,CAAC,CAAC,CAAC;EACjC,IAAIuV,aAAa,GAAG,SAAAA,CAAA,EAAY;IAC9B,IAAI/M,KAAK,GAAG,EAAE;IACd,KAAK,IAAIQ,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlI,SAAS,CAACC,MAAM,EAAEiI,EAAE,EAAE,EAAE;MAC5CR,KAAK,CAACQ,EAAE,CAAC,GAAGlI,SAAS,CAACkI,EAAE,CAAC;IAC3B;IACA,IAAI4M,SAAS,GAAGpN,KAAK,CAAC,CAAC,CAAC;MACtB1C,OAAO,GAAG0C,KAAK,CAAC,CAAC,CAAC;MAClBqL,UAAU,GAAGrL,KAAK,CAAC,CAAC,CAAC;IACvB,IAAIiK,gBAAgB,CAACvJ,OAAO,CAAC0M,SAAS,CAAC,EAAE;MACvCD,QAAQ,CAAC,YAAY;QACnB,MAAM,IAAIpT,KAAK,CAACoB,oCAAoC,CAAC;MACvD,CAAC,CAAC;IACJ;IACA8O,gBAAgB,CAACvJ,OAAO,CAAC0M,SAAS,CAAC,GAAG/B,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC+B,SAAS,CAAC,CAAC9P,OAAO,CAAC;IAC5H,OAAO2M,gBAAgB,CAACvJ,OAAO,CAAC0M,SAAS,CAAC;EAC5C,CAAC;EACD,IAAIJ,eAAe,GAAG,SAAAA,CAAUI,SAAS,EAAE;IACzC,IAAIC,KAAK,GAAGpD,gBAAgB,CAACvJ,OAAO,CAAC0M,SAAS,CAAC;IAC/C,IAAIC,KAAK,EAAE;MACTA,KAAK,CAACjL,KAAK,CAAC,CAAC,CAACC,KAAK,CAACqK,MAAM,CAAC;MAC3B,OAAOzC,gBAAgB,CAACvJ,OAAO,CAAC0M,SAAS,CAAC;IAC5C;EACF,CAAC;EACD,OAAO;IACLzB,MAAM,EAAE1B,gBAAgB,CAACvJ,OAAO;IAChCqM,aAAa,EAAEA,aAAa;IAC5BC,eAAe,EAAEA;EACnB,CAAC;AACH,CAAC;AACD,IAAIM,kBAAkB,GAAG,SAAAA,CAAUnR,EAAE,EAAE;EACrC,IAAI4E,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;EAC1B,OAAO1J,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChCG,KAAK,EAAE;MACL8J,KAAK,EAAE;IACT;EACF,CAAC,EAAExM,QAAQ,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIyM,wBAAwB,GAAG,SAAAA,CAAUrR,EAAE,EAAE;EAC3C,IAAI4E,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;IACxBf,KAAK,GAAGnH,QAAQ,CAACsD,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACpC,IAAIG,EAAE,GAAG6C,sBAAsB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCK,UAAU,GAAGlD,EAAE,CAACkD,UAAU;IAC1BlC,OAAO,GAAGhB,EAAE,CAACgB,OAAO;EACtB,IAAId,EAAE,GAAG0Q,2BAA2B,CAAC,CAAC;IACpCvB,MAAM,GAAGnP,EAAE,CAACmP,MAAM;IAClBoB,aAAa,GAAGvQ,EAAE,CAACuQ,aAAa;IAChCC,eAAe,GAAGxQ,EAAE,CAACwQ,eAAe;EACtC,IAAInM,EAAE,GAAGpJ,QAAQ,CAAC,IAAI,CAAC;IACrBqV,cAAc,GAAGjM,EAAE,CAAC,CAAC,CAAC;IACtB4M,iBAAiB,GAAG5M,EAAE,CAAC,CAAC,CAAC;EAC3B,IAAI+K,kBAAkB,GAAGpU,MAAM,CAAC,IAAI,CAAC;EACrC,IAAIwJ,EAAE,GAAGvJ,QAAQ,CAAC,KAAK,CAAC;IACtBsK,UAAU,GAAGf,EAAE,CAAC,CAAC,CAAC;IAClBgB,aAAa,GAAGhB,EAAE,CAAC,CAAC,CAAC;EACvB;EACA,IAAIU,EAAE,GAAGjK,QAAQ,CAAC,IAAI,CAAC;IACrB0V,QAAQ,GAAGzL,EAAE,CAAC,CAAC,CAAC;EAClBhK,SAAS,CAAC,YAAY;IACpB,IAAIyE,EAAE,EAAEG,EAAE,EAAEE,EAAE;IACd,IAAI,CAACgD,UAAU,EAAE;MACf;IACF;IACA,IAAI;MACFoM,kBAAkB,CAAClL,OAAO,GAAG,CAAClE,EAAE,GAAG,CAACF,EAAE,GAAG,CAACH,EAAE,GAAGd,0BAA0B,CAACiC,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,CAAC,EAAEoT,UAAU,MAAM,IAAI,IAAIpR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC3D,IAAI,CAACwD,EAAE,EAAEpE,QAAQ,CAAC,CAAC,CAAC,EAAEiI,KAAK,CAAC,CAAC,MAAM,IAAI,IAAIxD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;IAC3O,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACduT,QAAQ,CAAC,YAAY;QACnB,MAAM,IAAIpT,KAAK,CAAC,kFAAkF,CAACL,MAAM,CAACE,KAAK,CAAC,CAAC;MACnH,CAAC,CAAC;MACF;IACF;IACA,IAAI,CAACgS,kBAAkB,CAAClL,OAAO,EAAE;MAC/ByM,QAAQ,CAAC,YAAY;QACnB,IAAIhR,EAAE;QACN,MAAM,IAAIpC,KAAK,CAAC0S,8BAA8B,EAAEtQ,EAAE,GAAG;UACnDyG,UAAU,EAAEtF,OAAO,CAACsF;QACtB,CAAC,EAAEzG,EAAE,CAACjC,YAAY,CAACI,cAAc,CAAC,GAAGgD,OAAO,CAACpD,YAAY,CAACI,cAAc,CAAC,EAAE6B,EAAE,CAAC,CAAC,CAAC;MAClF,CAAC,CAAC;MACF;IACF;IACA6F,aAAa,CAAC4J,kBAAkB,CAAClL,OAAO,CAACqB,UAAU,CAAC,CAAC,CAAC;IACtD0L,iBAAiB,CAAC7B,kBAAkB,CAAClL,OAAO,CAAC;IAC7C,OAAO,YAAY;MACjB+M,iBAAiB,CAAC,IAAI,CAAC;MACvB7B,kBAAkB,CAAClL,OAAO,GAAG,IAAI;IACnC,CAAC;EACH,CAAC,EAAE,CAAClB,UAAU,CAAC,CAAC,CAAC,CAAC;EAClB,IAAI,CAACuC,UAAU,EAAE;IACf;IACA,OAAO1K,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC;EACzC;EACA,OAAOjM,KAAK,CAACiM,aAAa,CAACgK,kBAAkB,EAAE,IAAI,EAAEjW,KAAK,CAACiM,aAAa,CAACuJ,uBAAuB,CAAC/C,QAAQ,EAAE;IACzGvL,KAAK,EAAE;MACLuO,cAAc,EAAEA,cAAc;MAC9BnB,MAAM,EAAEA,MAAM;MACdoB,aAAa,EAAEA,aAAa;MAC5BC,eAAe,EAAEA;IACnB;EACF,CAAC,EAAEjM,QAAQ,CAAC,CAAC;AACf,CAAC;AACD,IAAI4M,eAAe,GAAG,SAAAA,CAAUxR,EAAE,EAAE;EAClC,IAAIyE,SAAS,GAAGzE,EAAE,CAACyE,SAAS;IAC1BwM,SAAS,GAAGjR,EAAE,CAACiR,SAAS;IACxB9P,OAAO,GAAGzE,QAAQ,CAACsD,EAAE,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EACpD,IAAIG,EAAE,GAAG2Q,mBAAmB,CAAC,CAAC;IAC5BH,cAAc,GAAGxQ,EAAE,CAACwQ,cAAc;IAClCC,aAAa,GAAGzQ,EAAE,CAACyQ,aAAa;IAChCC,eAAe,GAAG1Q,EAAE,CAAC0Q,eAAe;EACtC,IAAIY,YAAY,GAAGpW,MAAM,CAAC,IAAI,CAAC;EAC/B;EACA,IAAIgF,EAAE,GAAG/E,QAAQ,CAAC,IAAI,CAAC;IACrB0V,QAAQ,GAAG3Q,EAAE,CAAC,CAAC,CAAC;EAClB,SAASqR,cAAcA,CAAA,EAAG;IACxBb,eAAe,CAACI,SAAS,CAAC;EAC5B;EACA1V,SAAS,CAAC,YAAY;IACpB,IAAI,CAACoV,cAAc,EAAE;MACnBK,QAAQ,CAAC,YAAY;QACnB,MAAM,IAAIpT,KAAK,CAACqB,yBAAyB,CAAC;MAC5C,CAAC,CAAC;MACF,OAAOyS,cAAc;IACvB;IACA,IAAI,CAACD,YAAY,CAAClN,OAAO,EAAE;MACzB,OAAOmN,cAAc;IACvB;IACA,IAAIC,eAAe,GAAGf,aAAa,CAACK,SAAS,EAAE9P,OAAO,EAAEwP,cAAc,CAAC;IACvEgB,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC5K,MAAM,CAAC0K,YAAY,CAAClN,OAAO,CAAC,CAAC2B,KAAK,CAAC,UAAUY,GAAG,EAAE;MAClI,IAAI,CAAC0J,WAAW,CAACiB,YAAY,CAAC,EAAE;QAC9B;QACA;MACF;MACA;MACAT,QAAQ,CAAC,YAAY;QACnB,MAAM,IAAIpT,KAAK,CAAC,0BAA0B,CAACL,MAAM,CAAC0T,SAAS,EAAE,iBAAiB,CAAC,CAAC1T,MAAM,CAACuJ,GAAG,CAAC,CAAC;MAC9F,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO4K,cAAc;EACvB,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;EACR,OAAOxW,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChCE,GAAG,EAAEoK,YAAY;IACjBhN,SAAS,EAAEA;EACb,CAAC,CAAC;AACJ,CAAC;AACD,IAAImN,eAAe,GAAG,SAAAA,CAAUzQ,OAAO,EAAE;EACvC,OAAOjG,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE5V,QAAQ,CAAC;IACnDqV,SAAS,EAAE;EACb,CAAC,EAAE9P,OAAO,CAAC,CAAC;AACd,CAAC;AACD,IAAI0Q,iBAAiB,GAAG,SAAAA,CAAU1Q,OAAO,EAAE;EACzC,OAAOjG,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE5V,QAAQ,CAAC;IACnDqV,SAAS,EAAE;EACb,CAAC,EAAE9P,OAAO,CAAC,CAAC;AACd,CAAC;AACD,IAAI2Q,iBAAiB,GAAG,SAAAA,CAAU3Q,OAAO,EAAE;EACzC,OAAOjG,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE5V,QAAQ,CAAC;IACnDqV,SAAS,EAAE;EACb,CAAC,EAAE9P,OAAO,CAAC,CAAC;AACd,CAAC;AACD,IAAI4Q,cAAc,GAAG,SAAAA,CAAU5Q,OAAO,EAAE;EACtC,OAAOjG,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE5V,QAAQ,CAAC;IACnDqV,SAAS,EAAE;EACb,CAAC,EAAE9P,OAAO,CAAC,CAAC;AACd,CAAC;AACD,IAAI6Q,aAAa,GAAG,SAAAA,CAAUhS,EAAE,EAAE;EAChC,IAAI4E,QAAQ,GAAG5E,EAAE,CAAC4E,QAAQ;EAC1B,OAAO1J,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChCG,KAAK,EAAE;MACL2K,OAAO,EAAE,MAAM;MACfb,KAAK,EAAE;IACT;EACF,CAAC,EAAExM,QAAQ,CAAC;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIsN,oBAAoB,GAAG,SAAAA,CAAUlS,EAAE,EAAE;EACvC,IAAIyE,SAAS,GAAGzE,EAAE,CAACyE,SAAS;EAC5B,OAAOvJ,KAAK,CAACiM,aAAa,CAAC,KAAK,EAAE;IAChC1C,SAAS,EAAEA;EACb,CAAC,EAAEvJ,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE;IACtCP,SAAS,EAAE;EACb,CAAC,CAAC,EAAE/V,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE;IACvCP,SAAS,EAAE;EACb,CAAC,CAAC,EAAE/V,KAAK,CAACiM,aAAa,CAAC6K,aAAa,EAAE,IAAI,EAAE9W,KAAK,CAACiM,aAAa,CAACgK,kBAAkB,EAAE,IAAI,EAAEjW,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE;IAC9HP,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,EAAE/V,KAAK,CAACiM,aAAa,CAACgK,kBAAkB,EAAE,IAAI,EAAEjW,KAAK,CAACiM,aAAa,CAACqK,eAAe,EAAE;IACtFP,SAAS,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;AACD,IAAIkB,SAAS,GAAG;EACdC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,MAAM;EACZC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,YAAY;EACxBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,GAAG,EAAE,KAAK;EACVC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,cAAc,EAAE,gBAAgB;EAChCC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,QAAQ,EAAE,UAAU;EACpBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE;AACV,CAAC;AACD,CAAC5B,SAAS,CAACQ,KAAK,EAAER,SAAS,CAACU,UAAU,EAAEV,SAAS,CAACW,OAAO,EAAEX,SAAS,CAACY,MAAM,EAAEZ,SAAS,CAACa,GAAG,EAAEb,SAAS,CAACc,MAAM,EAAEd,SAAS,CAACe,GAAG,EAAEf,SAAS,CAACgB,IAAI,EAAEhB,SAAS,CAACiB,IAAI,EAAEjB,SAAS,CAACkB,OAAO,EAAElB,SAAS,CAACmB,IAAI,EAAEnB,SAAS,CAACoB,MAAM,EAAEpB,SAAS,CAACqB,cAAc,EAAErB,SAAS,CAACsB,SAAS,EAAEtB,SAAS,CAACuB,WAAW,EAAEvB,SAAS,CAACwB,UAAU,EAAExB,SAAS,CAACyB,QAAQ,EAAEzB,SAAS,CAAC0B,KAAK,EAAE1B,SAAS,CAAC4B,MAAM,EAAE5B,SAAS,CAAC2B,OAAO,CAAC;;AAEvX;AACA;AACA;AACA;AACA,IAAIE,OAAO,GAAG7B,SAAS;AACvB,SAASnG,sBAAsB,EAAEtQ,eAAe,EAAEsY,OAAO,EAAErY,0BAA0B,EAAE6I,aAAa,EAAEuN,cAAc,EAAErB,uBAAuB,EAAEwB,oBAAoB,EAAEb,wBAAwB,EAAES,iBAAiB,EAAEpC,iBAAiB,EAAEZ,0BAA0B,EAAEpC,WAAW,EAAEO,cAAc,EAAE2E,eAAe,EAAEC,iBAAiB,EAAEvE,oBAAoB,EAAE7R,oBAAoB,EAAEkH,aAAa,EAAEpB,gBAAgB,EAAEL,WAAW,EAAEa,aAAa,EAAE+O,mBAAmB,EAAEnN,qBAAqB,EAAEX,sBAAsB,EAAES,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}