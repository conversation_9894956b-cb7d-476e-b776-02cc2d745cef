{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowDownNarrowWide = createLucideIcon(\"ArrowDownNarrowWide\", [[\"path\", {\n  d: \"m3 16 4 4 4-4\",\n  key: \"1co6wj\"\n}], [\"path\", {\n  d: \"M7 20V4\",\n  key: \"1yoxec\"\n}], [\"path\", {\n  d: \"M11 4h4\",\n  key: \"6d7r33\"\n}], [\"path\", {\n  d: \"M11 8h7\",\n  key: \"djye34\"\n}], [\"path\", {\n  d: \"M11 12h10\",\n  key: \"1438ji\"\n}]]);\nexport { ArrowDownNarrowWide as default };", "map": {"version": 3, "names": ["ArrowDownNarrowWide", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\arrow-down-narrow-wide.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDownNarrowWide\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMyAxNiA0IDQgNC00IiAvPgogIDxwYXRoIGQ9Ik03IDIwVjQiIC8+CiAgPHBhdGggZD0iTTExIDRoNCIgLz4KICA8cGF0aCBkPSJNMTEgOGg3IiAvPgogIDxwYXRoIGQ9Ik0xMSAxMmgxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/arrow-down-narrow-wide\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownNarrowWide = createLucideIcon('ArrowDownNarrowWide', [\n  ['path', { d: 'm3 16 4 4 4-4', key: '1co6wj' }],\n  ['path', { d: 'M7 20V4', key: '1yoxec' }],\n  ['path', { d: 'M11 4h4', key: '6d7r33' }],\n  ['path', { d: 'M11 8h7', key: 'djye34' }],\n  ['path', { d: 'M11 12h10', key: '1438ji' }],\n]);\n\nexport default ArrowDownNarrowWide;\n"], "mappings": ";;;;;AAaM,MAAAA,mBAAA,GAAsBC,gBAAA,CAAiB,qBAAuB,GAClE,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}