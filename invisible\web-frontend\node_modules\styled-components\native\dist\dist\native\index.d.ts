/// <reference path="../../src/global.d.ts" />
import React from 'react';
import { Styled } from '../constructors/constructWithOptions';
import css from '../constructors/css';
import withTheme from '../hoc/withTheme';
import ThemeProvider, { ThemeConsumer, ThemeContext, useTheme } from '../models/ThemeProvider';
import { NativeTarget, RuleSet } from '../types';
import isStyledComponent from '../utils/isStyledComponent';
declare const styled: (<Target extends NativeTarget>(tag: Target) => Styled<"native", Target, Target extends import("../types").KnownTarget ? React.ComponentPropsWithRef<Target> : import("../types").BaseObject, import("../types").BaseObject>) & {
    ActivityIndicator: Styled<"native", typeof import("react-native").ActivityIndicator, import("react-native").ActivityIndicatorProps, import("../types").BaseObject>;
    Button: Styled<"native", typeof import("react-native").Button, import("react-native").ButtonProps, import("../types").BaseObject>;
    DatePickerIOS: Styled<"native", typeof import("react-native").DatePickerIOS, import("react-native").DatePickerIOSProps, import("../types").BaseObject>;
    DrawerLayoutAndroid: Styled<"native", typeof import("react-native").DrawerLayoutAndroid, import("react-native").DrawerLayoutAndroidProps, import("../types").BaseObject>;
    FlatList: Styled<"native", typeof import("react-native").FlatList, import("react-native").FlatListProps<unknown>, import("../types").BaseObject>;
    Image: Styled<"native", typeof import("react-native").Image, import("react-native").ImageProps, import("../types").BaseObject>;
    ImageBackground: Styled<"native", typeof import("react-native").ImageBackground, import("react-native").ImageBackgroundProps, import("../types").BaseObject>;
    KeyboardAvoidingView: Styled<"native", typeof import("react-native").KeyboardAvoidingView, import("react-native").KeyboardAvoidingViewProps, import("../types").BaseObject>;
    Modal: Styled<"native", typeof import("react-native").Modal, import("react-native").ModalBaseProps & import("react-native").ModalPropsIOS & import("react-native").ModalPropsAndroid & import("react-native").ViewProps, import("../types").BaseObject>;
    Pressable: Styled<"native", React.ForwardRefExoticComponent<import("react-native").PressableProps & React.RefAttributes<import("react-native").View>>, import("react-native").PressableProps & React.RefAttributes<import("react-native").View>, import("../types").BaseObject>;
    ProgressBarAndroid: Styled<"native", typeof import("react-native").ProgressBarAndroid, import("react-native").ProgressBarAndroidProps, import("../types").BaseObject>;
    ProgressViewIOS: Styled<"native", typeof import("react-native").ProgressViewIOS, import("react-native").ProgressViewIOSProps, import("../types").BaseObject>;
    RefreshControl: Styled<"native", typeof import("react-native").RefreshControl, import("react-native").RefreshControlProps, import("../types").BaseObject>;
    SafeAreaView: Styled<"native", typeof import("react-native").SafeAreaView, import("react-native").ViewProps, import("../types").BaseObject>;
    ScrollView: Styled<"native", typeof import("react-native").ScrollView, import("react-native").ScrollViewProps, import("../types").BaseObject>;
    SectionList: Styled<"native", typeof import("react-native").SectionList, import("react-native").SectionListProps<unknown, unknown>, import("../types").BaseObject>;
    Slider: Styled<"native", typeof import("react-native").Slider, import("react-native").SliderProps, import("../types").BaseObject>;
    Switch: Styled<"native", typeof import("react-native").Switch, import("react-native").SwitchProps, import("../types").BaseObject>;
    Text: Styled<"native", typeof import("react-native").Text, import("react-native").TextProps, import("../types").BaseObject>;
    TextInput: Styled<"native", typeof import("react-native").TextInput, import("react-native").TextInputProps, import("../types").BaseObject>;
    TouchableHighlight: Styled<"native", typeof import("react-native").TouchableHighlight, import("react-native").TouchableHighlightProps, import("../types").BaseObject>;
    TouchableOpacity: Styled<"native", typeof import("react-native").TouchableOpacity, import("react-native").TouchableOpacityProps, import("../types").BaseObject>;
    View: Styled<"native", typeof import("react-native").View, import("react-native").ViewProps, import("../types").BaseObject>;
    VirtualizedList: Styled<"native", typeof import("react-native").VirtualizedList, import("react-native").VirtualizedListProps<unknown>, import("../types").BaseObject>;
};
declare const toStyleSheet: (rules: RuleSet<object>) => import("css-to-react-native").Style;
export { CSSKeyframes, CSSObject, CSSProperties, CSSPseudos, DefaultTheme, ExecutionContext, ExecutionProps, IStyledComponent, IStyledComponentFactory, IStyledStatics, NativeTarget, PolymorphicComponent, PolymorphicComponentProps, Runtime, StyledObject, StyledOptions, } from '../types';
export { ThemeConsumer, ThemeContext, ThemeProvider, css, styled as default, isStyledComponent, styled, toStyleSheet, useTheme, withTheme, };
