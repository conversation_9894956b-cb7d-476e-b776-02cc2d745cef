@echo off
echo 🔧 TESTING BUILD FIX FOR IMAGE LOADING ISSUE
echo =============================================
echo.

echo 📋 ISSUE: Users getting "failed to load image data" after download
echo 🎯 FIX: Include directory structure in build, exclude only image files
echo.

echo 🔍 STEP 1: Checking current build configuration...
type package.json | findstr /C:"files" /A:5
echo.

echo 📁 STEP 2: Verifying .gitkeep files exist...
if exist "screenshots\.gitkeep" (
    echo ✅ screenshots\.gitkeep exists
) else (
    echo ❌ screenshots\.gitkeep missing
)

if exist "temp\.gitkeep" (
    echo ✅ temp\.gitkeep exists
) else (
    echo ❌ temp\.gitkeep missing
)

if exist "cache\.gitkeep" (
    echo ✅ cache\.gitkeep exists
) else (
    echo ❌ cache\.gitkeep missing
)
echo.

echo 🧹 STEP 3: Cleaning previous builds...
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ Removed old dist folder
)
echo.

echo 🏗️ STEP 4: Building with fixed configuration...
call npm run build-portable
if %errorlevel% neq 0 (
    echo ❌ Build failed!
    pause
    exit /b 1
)
echo.

echo 📦 STEP 5: Checking build output...
if exist "dist\InvisibleAssessmentTool-Portable-x64.exe" (
    echo ✅ Executable created successfully
    dir "dist\*.exe" /B
) else (
    echo ❌ Executable not found!
    pause
    exit /b 1
)
echo.

echo 🎉 BUILD FIX COMPLETE!
echo.
echo 📋 WHAT WAS FIXED:
echo   • Changed "!screenshots/**/*" to include directory structure
echo   • Added .gitkeep files to ensure directories exist in build
echo   • Exclude only image files, not entire directories
echo   • Added directory creation logic in main.js
echo.
echo 🚀 NEXT STEPS:
echo   1. Test the executable on a clean system
echo   2. Verify screenshots can be taken and saved
echo   3. Confirm no "failed to load image data" errors
echo   4. Update download system with fixed executable
echo.
pause
