import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { XCircle, ArrowLeft } from 'lucide-react';

const CancelContainer = styled.div`
  min-height: 100vh;
  padding: 120px 20px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const CancelCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
`;

const CancelIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 20px;
  color: #FF5722;
`;

const CancelTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #FF5722;
`;

const CancelMessage = styled.p`
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
`;

const PaymentCancel = () => {
  const navigate = useNavigate();

  return (
    <CancelContainer>
      <CancelCard>
        <CancelIcon>
          <XCircle size={64} />
        </CancelIcon>
        <CancelTitle>Payment Cancelled</CancelTitle>
        <CancelMessage>
          Your payment was cancelled. No charges were made to your account.
        </CancelMessage>
        
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center' }}>
          <button
            className="btn-secondary"
            onClick={() => navigate('/purchase')}
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}
          >
            <ArrowLeft size={18} />
            Try Again
          </button>
          
          <button
            className="btn-primary"
            onClick={() => navigate('/dashboard')}
          >
            Go to Dashboard
          </button>
        </div>
        
        <p style={{ marginTop: '30px', fontSize: '0.9rem', opacity: '0.7' }}>
          If you experienced any issues, please contact our support team.
        </p>
      </CancelCard>
    </CancelContainer>
  );
};

export default PaymentCancel;
