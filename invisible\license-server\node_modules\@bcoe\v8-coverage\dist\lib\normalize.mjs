import { compareFunctionCovs, compareRangeCovs, compareScriptCovs } from "./compare";
import { RangeTree } from "./range-tree";
/**
 * Normalizes a process coverage.
 *
 * Sorts the scripts alphabetically by `url`.
 * Reassigns script ids: the script at index `0` receives `"0"`, the script at
 * index `1` receives `"1"` etc.
 * This does not normalize the script coverages.
 *
 * @param processCov Process coverage to normalize.
 */
export function normalizeProcessCov(processCov) {
    processCov.result.sort(compareScriptCovs);
    for (const [scriptId, scriptCov] of processCov.result.entries()) {
        scriptCov.scriptId = scriptId.toString(10);
    }
}
/**
 * Normalizes a process coverage deeply.
 *
 * Normalizes the script coverages deeply, then normalizes the process coverage
 * itself.
 *
 * @param processCov Process coverage to normalize.
 */
export function deepNormalizeProcessCov(processCov) {
    for (const scriptCov of processCov.result) {
        deepNormalizeScriptCov(scriptCov);
    }
    normalizeProcessCov(processCov);
}
/**
 * Normalizes a script coverage.
 *
 * Sorts the function by root range (pre-order sort).
 * This does not normalize the function coverages.
 *
 * @param scriptCov Script coverage to normalize.
 */
export function normalizeScriptCov(scriptCov) {
    scriptCov.functions.sort(compareFunctionCovs);
}
/**
 * Normalizes a script coverage deeply.
 *
 * Normalizes the function coverages deeply, then normalizes the script coverage
 * itself.
 *
 * @param scriptCov Script coverage to normalize.
 */
export function deepNormalizeScriptCov(scriptCov) {
    for (const funcCov of scriptCov.functions) {
        normalizeFunctionCov(funcCov);
    }
    normalizeScriptCov(scriptCov);
}
/**
 * Normalizes a function coverage.
 *
 * Sorts the ranges (pre-order sort).
 * TODO: Tree-based normalization of the ranges.
 *
 * @param funcCov Function coverage to normalize.
 */
export function normalizeFunctionCov(funcCov) {
    funcCov.ranges.sort(compareRangeCovs);
    const tree = RangeTree.fromSortedRanges(funcCov.ranges);
    normalizeRangeTree(tree);
    funcCov.ranges = tree.toRanges();
}
/**
 * @internal
 */
export function normalizeRangeTree(tree) {
    tree.normalize();
}

//# sourceMappingURL=data:application/json;charset=utf8;base64,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
