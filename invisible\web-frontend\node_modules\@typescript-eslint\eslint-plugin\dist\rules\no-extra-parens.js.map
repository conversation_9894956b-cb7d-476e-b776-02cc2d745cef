{"version": 3, "file": "no-extra-parens.js", "sourceRoot": "", "sources": ["../../src/rules/no-extra-parens.ts"], "names": [], "mappings": ";AAAA,oEAAoE;AACpE,gGAAgG;;;;;;;;;;;;;;;;;;;;;;;;;AAGhG,oDAA0D;AAE1D,8CAAgC;AAChC,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,iBAAiB,CAAC,CAAC;AAKtD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,kCAAkC;YAC/C,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,KAAK,CAAC;IACvB,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,SAAS,CAChB,IAA4D;YAE5D,MAAM,IAAI,GAAG,KAAK,CAAC,gBAA4C,CAAC;YAEhE,wDAAwD;YACxD,MAAM,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,MAAM,oBAAoB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9D,IAAI,mBAAmB,IAAI,oBAAoB,EAAE;gBAC/C,OAAO,CAAC,SAAS;aAClB;YACD,IAAI,mBAAmB,EAAE;gBACvB,OAAO,IAAI,iCACN,IAAI,KACP,IAAI,kCACC,IAAI,CAAC,IAAI,KACZ,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YACD,IAAI,oBAAoB,EAAE;gBACxB,OAAO,IAAI,iCACN,IAAI,KACP,KAAK,kCACA,IAAI,CAAC,KAAK,KACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,OAAO,CACd,IAAsD;;YAEtD,MAAM,IAAI,GAAG,KAAK,CAAC,cAA0C,CAAC;YAE9D,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;gBACrC,+EAA+E;gBAC/E,OAAO,IAAI,iCACN,IAAI,KACP,MAAM,kCACD,IAAI,CAAC,MAAM,KACd,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YAED,IACE,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC;iBAC3B,MAAA,IAAI,CAAC,cAAc,0CAAE,MAAM,CAAC,IAAI,CAC9B,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY;oBAC1C,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAC5C,CAAA,EACD;gBACA,OAAO,IAAI,iCACN,IAAI,KACP,SAAS,EAAE;wDAEJ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,KACpB,IAAI,EAAE,sBAAc,CAAC,kBAAyB;qBAEjD,IACD,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,qBAAqB,CAC5B,IAA0D;YAE1D,MAAM,IAAI,GAAG,KAAK,CAAC,eAA2C,CAAC;YAE/D,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACvC,+EAA+E;gBAC/E,OAAO,IAAI,iCACN,IAAI,KACP,QAAQ,kCACH,IAAI,CAAC,QAAQ,KAChB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,SAAS,GAA0B;YACvC,kBAAkB;YAClB,uBAAuB,CAAC,IAAI;gBAC1B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACpC,OAAO,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;iBAC5C;YACH,CAAC;YACD,uBAAuB;YACvB,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACvC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,eAAe,iCACvB,IAAI,KACP,QAAQ,kCACH,IAAI,CAAC,QAAQ,KAChB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;YACD,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,OAAO;YACvB,gBAAgB,CAAC,IAAI;;gBACnB,IAAI,CAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,EAAE;oBAC3D,OAAO,KAAK,CAAC,gBAAgB,iCACxB,IAAI,KACP,UAAU,kCACL,IAAI,CAAC,UAAU,KAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,eAAe,CAAC,IAAI;;gBAClB,IAAI,CAAA,MAAA,IAAI,CAAC,UAAU,0CAAE,IAAI,MAAK,sBAAc,CAAC,cAAc,EAAE;oBAC3D,OAAO,KAAK,CAAC,eAAe,iCACvB,IAAI,KACP,UAAU,kCACL,IAAI,CAAC,UAAU,KAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YACrC,CAAC;YACD,qBAAqB,CAAC,IAAI;gBACxB,+EAA+E;gBAC/E,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACnC,OAAO,KAAK,CAAC,qBAAqB,iCAC7B,IAAI,KACP,IAAI,kCACC,IAAI,CAAC,IAAI,KACZ,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;oBACzC,OAAO,KAAK,CAAC,qBAAqB,iCAC7B,IAAI,KACP,UAAU,kCACL,IAAI,CAAC,UAAU,KAClB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;oBACxC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,qBAAqB,iCAC7B,IAAI,KACP,SAAS,kCACJ,IAAI,CAAC,SAAS,KACjB,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,OAAO,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YACD,mBAAmB;YACnB,gDAAgD;YAChD,YAAY,CAAC,IAAI;gBACf,uDAAuD;gBACvD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChD,OAAO,KAAK,CAAC,YAAY,iCACpB,IAAI,KACP,IAAI,EAAE,IAAI,IACV,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAChD,OAAO,KAAK,CAAC,YAAY,iCACpB,IAAI,KACP,IAAI,EAAE,IAAI,IACV,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACpD,OAAO,KAAK,CAAC,YAAY,iCACpB,IAAI,KACP,MAAM,EAAE,IAAI,IACZ,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,4BAA4B,CAAC,IAAmB;gBAC9C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;oBAC/B,OAAO,KAAK,CAAC,4BAA4B,CAAC,CAAC,IAAI,CAAC,CAAC;iBAClD;YACH,CAAC;YACD,cAAc;YACd,iBAAiB,EAAE,SAAS;YAC5B,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACrC,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,gBAAgB,iCACxB,IAAI,KACP,MAAM,kCACD,IAAI,CAAC,MAAM,KACd,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,EAAE,OAAO;YACtB,mBAAmB;YACnB,kBAAkB;YAClB,qBAAqB;YACrB,aAAa,CAAC,IAAI;gBAChB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACxC,OAAO,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBAClC;YACH,CAAC;YACD,UAAU,CAAC,IAAI;gBACb,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBACjD,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBAC/B;YACH,CAAC;YACD,kBAAkB;YAClB,cAAc,CAAC,IAAI;gBACjB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACzD,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;iBACnC;YACH,CAAC;YACD,eAAe,EAAE,qBAAqB;YACtC,gBAAgB,EAAE,qBAAqB;YACvC,qBAAqB;YACrB,iBAAiB;YACjB,iGAAiG;YACjG,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;oBACzD,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;iBACpC;YACH,CAAC;SACF,CAAC;QACF,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,cAAc,EAAE;YAChD,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI;gBACvC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACpC,sEAAsE;oBACtE,mCAAmC;oBACnC,OAAO;iBACR;gBAED,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;YACF,SAAS,CAAC,cAAc,GAAG,UAAU,IAAI;gBACvC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACpC,4CAA4C;oBAC5C,OAAO,KAAK,CAAC,cAAc,iCACtB,IAAI,KACP,IAAI,EAAE,sBAAc,CAAC,cAAc,EACnC,KAAK,kCACA,IAAI,CAAC,KAAK,KACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YACpC,CAAC,CAAC;SACH;aAAM;YACL,SAAS,CAAC,gCAAgC,CAAC,GAAG,UAC5C,IAAuD;gBAEvD,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;oBACpC,4CAA4C;oBAC5C,OAAO,KAAK,CAAC,gCAAgC,CAAC,iCACzC,IAAI,KACP,IAAI,EAAE,sBAAc,CAAC,cAAqB,EAC1C,KAAK,kCACA,IAAI,CAAC,KAAK,KACb,IAAI,EAAE,sBAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC,CAAC;SACH;QACD,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC,CAAC"}