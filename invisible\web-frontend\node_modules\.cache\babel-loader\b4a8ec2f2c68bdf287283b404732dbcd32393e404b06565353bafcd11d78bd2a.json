{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Codepen = createLucideIcon(\"Codepen\", [[\"polygon\", {\n  points: \"12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2\",\n  key: \"srzb37\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"15.5\",\n  key: \"1t73f2\"\n}], [\"polyline\", {\n  points: \"22 8.5 12 15.5 2 8.5\",\n  key: \"ajlxae\"\n}], [\"polyline\", {\n  points: \"2 15.5 12 8.5 22 15.5\",\n  key: \"susrui\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"8.5\",\n  key: \"2cldga\"\n}]]);\nexport { Codepen as default };", "map": {"version": 3, "names": ["Codepen", "createLucideIcon", "points", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\codepen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Codepen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cG9seWdvbiBwb2ludHM9IjEyIDIgMjIgOC41IDIyIDE1LjUgMTIgMjIgMiAxNS41IDIgOC41IDEyIDIiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIyMiIgeTI9IjE1LjUiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMjIgOC41IDEyIDE1LjUgMiA4LjUiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMiAxNS41IDEyIDguNSAyMiAxNS41IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMiIgeTI9IjguNSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/codepen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Codepen = createLucideIcon('Codepen', [\n  [\n    'polygon',\n    { points: '12 2 22 8.5 22 15.5 12 22 2 15.5 2 8.5 12 2', key: 'srzb37' },\n  ],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '15.5', key: '1t73f2' }],\n  ['polyline', { points: '22 8.5 12 15.5 2 8.5', key: 'ajlxae' }],\n  ['polyline', { points: '2 15.5 12 8.5 22 15.5', key: 'susrui' }],\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '8.5', key: '2cldga' }],\n]);\n\nexport default Codepen;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,WACA;EAAEC,MAAA,EAAQ,6CAA+C;EAAAC,GAAA,EAAK;AAAS,EACzE,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,MAAQ;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,UAAY;EAAED,MAAA,EAAQ,sBAAwB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9D,CAAC,UAAY;EAAED,MAAA,EAAQ,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,KAAO;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}