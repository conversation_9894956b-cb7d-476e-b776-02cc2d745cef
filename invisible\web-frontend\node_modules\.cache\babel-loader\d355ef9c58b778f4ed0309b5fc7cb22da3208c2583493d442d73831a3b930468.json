{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Bug = createLucideIcon(\"Bug\", [[\"rect\", {\n  width: \"8\",\n  height: \"14\",\n  x: \"8\",\n  y: \"6\",\n  rx: \"4\",\n  key: \"hq8nra\"\n}], [\"path\", {\n  d: \"m19 7-3 2\",\n  key: \"fmg8ec\"\n}], [\"path\", {\n  d: \"m5 7 3 2\",\n  key: \"dkxqes\"\n}], [\"path\", {\n  d: \"m19 19-3-2\",\n  key: \"1hbhi4\"\n}], [\"path\", {\n  d: \"m5 19 3-2\",\n  key: \"dvt2ee\"\n}], [\"path\", {\n  d: \"M20 13h-4\",\n  key: \"1agfp2\"\n}], [\"path\", {\n  d: \"M4 13h4\",\n  key: \"1bwh8b\"\n}], [\"path\", {\n  d: \"m10 4 1 2\",\n  key: \"1pot59\"\n}], [\"path\", {\n  d: \"m14 4-1 2\",\n  key: \"m8sn0o\"\n}]]);\nexport { Bug as default };", "map": {"version": 3, "names": ["Bug", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\bug.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Bug\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iOCIgaGVpZ2h0PSIxNCIgeD0iOCIgeT0iNiIgcng9IjQiIC8+CiAgPHBhdGggZD0ibTE5IDctMyAyIiAvPgogIDxwYXRoIGQ9Im01IDcgMyAyIiAvPgogIDxwYXRoIGQ9Im0xOSAxOS0zLTIiIC8+CiAgPHBhdGggZD0ibTUgMTkgMy0yIiAvPgogIDxwYXRoIGQ9Ik0yMCAxM2gtNCIgLz4KICA8cGF0aCBkPSJNNCAxM2g0IiAvPgogIDxwYXRoIGQ9Im0xMCA0IDEgMiIgLz4KICA8cGF0aCBkPSJtMTQgNC0xIDIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bug\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bug = createLucideIcon('Bug', [\n  [\n    'rect',\n    { width: '8', height: '14', x: '8', y: '6', rx: '4', key: 'hq8nra' },\n  ],\n  ['path', { d: 'm19 7-3 2', key: 'fmg8ec' }],\n  ['path', { d: 'm5 7 3 2', key: 'dkxqes' }],\n  ['path', { d: 'm19 19-3-2', key: '1hbhi4' }],\n  ['path', { d: 'm5 19 3-2', key: 'dvt2ee' }],\n  ['path', { d: 'M20 13h-4', key: '1agfp2' }],\n  ['path', { d: 'M4 13h4', key: '1bwh8b' }],\n  ['path', { d: 'm10 4 1 2', key: '1pot59' }],\n  ['path', { d: 'm14 4-1 2', key: 'm8sn0o' }],\n]);\n\nexport default Bug;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EAAEC,KAAA,EAAO,GAAK;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}