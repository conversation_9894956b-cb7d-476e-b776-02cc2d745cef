const mongoose = require('mongoose');

const paymentSchema = new mongoose.Schema({
    // 🔑 PAYMENT IDENTIFICATION
    paymentId: {
        type: String,
        required: true,
        unique: true
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    licenseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'License',
        required: false // Set after license creation
    },

    // 💳 PAYMENT DETAILS
    paymentMethod: {
        type: String,
        enum: ['paypal', 'stripe', 'crypto', 'razorpay'],
        required: true
    },
    amount: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        default: 'INR'
    },

    // 📊 PAYMENT STATUS
    status: {
        type: String,
        enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
        default: 'pending'
    },

    // 🎯 SUBSCRIPTION DETAILS
    tier: {
        type: Number,
        required: true,
        enum: [1, 2] // 1 = First purchase, 2 = Renewal
    },
    screenshotsIncluded: {
        type: Number,
        required: true
    },

    // 📅 DATES
    paymentDate: {
        type: Date,
        default: Date.now
    },
    completedDate: {
        type: Date,
        required: false
    },

    // 🔄 PAYPAL SPECIFIC
    paypalDetails: {
        payerId: String,
        paymentId: String,
        token: String,
        executeUrl: String
    },

    // 📝 ADDITIONAL INFO
    description: {
        type: String,
        default: function () {
            return `Invisible Assessment Tool - Tier ${this.tier} (${this.screenshotsIncluded} screenshots)`;
        }
    },

    // 📊 METADATA
    metadata: {
        ipAddress: String,
        userAgent: String,
        referrer: String
    }
}, {
    timestamps: true
});

// ✅ MARK AS COMPLETED
paymentSchema.methods.markCompleted = async function () {
    this.status = 'completed';
    this.completedDate = new Date();
    await this.save();
    return this;
};

// ❌ MARK AS FAILED
paymentSchema.methods.markFailed = async function () {
    this.status = 'failed';
    await this.save();
    return this;
};

module.exports = mongoose.model('Payment', paymentSchema);
