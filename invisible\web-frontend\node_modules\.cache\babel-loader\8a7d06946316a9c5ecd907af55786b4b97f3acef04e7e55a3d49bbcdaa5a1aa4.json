{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Beaker = createLucideIcon(\"Beaker\", [[\"path\", {\n  d: \"M4.5 3h15\",\n  key: \"c7n0jr\"\n}], [\"path\", {\n  d: \"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3\",\n  key: \"m1uhx7\"\n}], [\"path\", {\n  d: \"M6 14h12\",\n  key: \"4cwo0f\"\n}]]);\nexport { Beaker as default };", "map": {"version": 3, "names": ["Beaker", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\beaker.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Beaker\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNC41IDNoMTUiIC8+CiAgPHBhdGggZD0iTTYgM3YxNmEyIDIgMCAwIDAgMiAyaDhhMiAyIDAgMCAwIDItMlYzIiAvPgogIDxwYXRoIGQ9Ik02IDE0aDEyIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/beaker\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Beaker = createLucideIcon('Beaker', [\n  ['path', { d: 'M4.5 3h15', key: 'c7n0jr' }],\n  ['path', { d: 'M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3', key: 'm1uhx7' }],\n  ['path', { d: 'M6 14h12', key: '4cwo0f' }],\n]);\n\nexport default Beaker;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,yCAA2C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxE,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}