{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Lamp = createLucideIcon(\"Lamp\", [[\"path\", {\n  d: \"M8 2h8l4 10H4L8 2Z\",\n  key: \"9dma5w\"\n}], [\"path\", {\n  d: \"M12 12v6\",\n  key: \"3ahymv\"\n}], [\"path\", {\n  d: \"M8 22v-2c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v2H8Z\",\n  key: \"mwf4oh\"\n}]]);\nexport { Lamp as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\lamp.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Lamp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAyaDhsNCAxMEg0TDggMloiIC8+CiAgPHBhdGggZD0iTTEyIDEydjYiIC8+CiAgPHBhdGggZD0iTTggMjJ2LTJjMC0xLjEuOS0yIDItMmg0YTIgMiAwIDAgMSAyIDJ2Mkg4WiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/lamp\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Lamp = createLucideIcon('Lamp', [\n  ['path', { d: 'M8 2h8l4 10H4L8 2Z', key: '9dma5w' }],\n  ['path', { d: 'M12 12v6', key: '3ahymv' }],\n  ['path', { d: 'M8 22v-2c0-1.1.9-2 2-2h4a2 2 0 0 1 2 2v2H8Z', key: 'mwf4oh' }],\n]);\n\nexport default Lamp;\n"], "mappings": ";;;;;AAaM,MAAAA,IAAA,GAAOC,gBAAA,CAAiB,MAAQ,GACpC,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}