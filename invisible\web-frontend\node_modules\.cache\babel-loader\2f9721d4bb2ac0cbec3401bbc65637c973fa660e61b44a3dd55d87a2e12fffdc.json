{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Download.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Download as DownloadIcon, Shield, Key, Monitor, CheckCircle, AlertCircle } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst DownloadContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = DownloadContainer;\nconst DownloadContent = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n`;\n_c2 = DownloadContent;\nconst SuccessHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n  padding: 40px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(76, 175, 80, 0.3);\n`;\n_c3 = SuccessHeader;\nconst SuccessIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n_c4 = SuccessIcon;\nconst SuccessTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n_c5 = SuccessTitle;\nconst SuccessMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n_c6 = SuccessMessage;\nconst LicenseInfo = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c7 = LicenseInfo;\nconst LicenseTitle = styled.h2`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  color: #4CAF50;\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n_c8 = LicenseTitle;\nconst LicenseDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n_c9 = LicenseDetails;\nconst LicenseDetail = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  \n  .label {\n    font-size: 0.9rem;\n    opacity: 0.7;\n    margin-bottom: 5px;\n  }\n  \n  .value {\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: #4CAF50;\n  }\n`;\n_c0 = LicenseDetail;\nconst LicenseKey = styled.div`\n  background: rgba(0, 0, 0, 0.3);\n  padding: 15px;\n  border-radius: 10px;\n  font-family: 'Courier New', monospace;\n  font-size: 1.1rem;\n  text-align: center;\n  margin-top: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  .copy-btn {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    background: #4CAF50;\n    color: white;\n    border: none;\n    padding: 5px 10px;\n    border-radius: 5px;\n    cursor: pointer;\n    font-size: 0.8rem;\n  }\n`;\n_c1 = LicenseKey;\nconst DownloadSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c10 = DownloadSection;\nconst DownloadButton = styled.button`\n  width: 100%;\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n  border: none;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n_c11 = DownloadButton;\nconst InstructionsSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c12 = InstructionsSection;\nconst InstructionsList = styled.ol`\n  margin-left: 20px;\n  \n  li {\n    margin-bottom: 15px;\n    line-height: 1.6;\n    \n    strong {\n      color: #4CAF50;\n    }\n  }\n`;\n_c13 = InstructionsList;\nconst Download = () => {\n  _s();\n  var _licenseData$plan, _licenseData$plan2;\n  const {\n    user\n  } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [downloading, setDownloading] = useState(false);\n  const [licenseData, setLicenseData] = useState(null);\n  const [downloadStatus, setDownloadStatus] = useState(null);\n  useEffect(() => {\n    var _location$state;\n    // Get license data from navigation state or fetch from API\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.licenseKey) {\n      setLicenseData({\n        licenseKey: location.state.licenseKey,\n        plan: location.state.plan\n      });\n    } else {\n      fetchLatestLicense();\n    }\n\n    // Fetch download status\n    fetchDownloadStatus();\n  }, [location.state]);\n  const fetchDownloadStatus = async () => {\n    try {\n      const response = await axios.get('/download/status');\n      if (response.data.success) {\n        setDownloadStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching download status:', error);\n    }\n  };\n  const fetchLatestLicense = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success && response.data.data.licenses.length > 0) {\n        const latestLicense = response.data.data.licenses[0];\n        setLicenseData({\n          licenseKey: latestLicense.licenseKey,\n          plan: {\n            title: latestLicense.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',\n            screenshots: latestLicense.screenshotsLimit\n          }\n        });\n      } else {\n        toast.error('No valid license found. Please purchase a license first.');\n        navigate('/purchase');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch license information');\n      navigate('/purchase');\n    }\n  };\n  const copyLicenseKey = () => {\n    if (licenseData !== null && licenseData !== void 0 && licenseData.licenseKey) {\n      navigator.clipboard.writeText(licenseData.licenseKey);\n      toast.success('License key copied to clipboard!');\n    }\n  };\n  const downloadSoftware = async () => {\n    try {\n      setDownloading(true);\n\n      // Create secure download link\n      const response = await axios.post('/download/create-link', {\n        licenseKey: licenseData.licenseKey\n      });\n      if (response.data.success) {\n        const {\n          downloadUrl,\n          fileName\n        } = response.data.data;\n\n        // Create download link\n        const link = document.createElement('a');\n        link.href = `http://localhost:5002${downloadUrl}`;\n        link.setAttribute('download', fileName);\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast.success('Download started! Check your downloads folder.');\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Download failed. Please try again or contact support.');\n    } finally {\n      setDownloading(false);\n    }\n  };\n  if (!licenseData) {\n    return /*#__PURE__*/_jsxDEV(DownloadContainer, {\n      children: /*#__PURE__*/_jsxDEV(DownloadContent, {\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            padding: '60px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"loading-spinner\",\n            style: {\n              margin: '0 auto 20px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 280,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Loading license information...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(DownloadContainer, {\n    children: /*#__PURE__*/_jsxDEV(DownloadContent, {\n      children: [/*#__PURE__*/_jsxDEV(SuccessHeader, {\n        children: [/*#__PURE__*/_jsxDEV(SuccessIcon, {\n          children: /*#__PURE__*/_jsxDEV(CheckCircle, {\n            size: 64\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SuccessTitle, {\n          children: \"Payment Successful!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(SuccessMessage, {\n          children: \"Your license has been activated and you can now download the software.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LicenseInfo, {\n        children: [/*#__PURE__*/_jsxDEV(LicenseTitle, {\n          children: [/*#__PURE__*/_jsxDEV(Key, {\n            size: 24\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), \"License Information\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LicenseDetails, {\n          children: [/*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Plan\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: (_licenseData$plan = licenseData.plan) === null || _licenseData$plan === void 0 ? void 0 : _licenseData$plan.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Screenshots\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: [(_licenseData$plan2 = licenseData.plan) === null || _licenseData$plan2 === void 0 ? void 0 : _licenseData$plan2.screenshots, \"/month\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: \"Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseDetail, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"label\",\n              children: \"User\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"value\",\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"Your License Key:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(LicenseKey, {\n            children: [licenseData.licenseKey, /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"copy-btn\",\n              onClick: copyLicenseKey,\n              children: \"Copy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DownloadSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            marginBottom: '20px',\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(DownloadIcon, {\n            size: 24,\n            style: {\n              marginRight: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 339,\n            columnNumber: 13\n          }, this), \"Download Software\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(DownloadButton, {\n          onClick: downloadSoftware,\n          disabled: downloading,\n          children: downloading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"loading-spinner\",\n              style: {\n                width: '20px',\n                height: '20px',\n                marginRight: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this), \"Preparing Download...\"]\n          }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(DownloadIcon, {\n              size: 24\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 17\n            }, this), \"Download Invisible Assessment Tool\"]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            opacity: 0.8\n          },\n          children: [/*#__PURE__*/_jsxDEV(Shield, {\n            size: 16,\n            style: {\n              marginRight: '5px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n            children: \"Secure download \\u2022 Hardware-bound installation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 360,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InstructionsSection, {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            marginBottom: '20px',\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Monitor, {\n            size: 24,\n            style: {\n              marginRight: '10px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 368,\n            columnNumber: 13\n          }, this), \"Installation Instructions\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(InstructionsList, {\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Download\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 373,\n              columnNumber: 17\n            }, this), \" the software using the button above\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Run\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 17\n            }, this), \" the installer as administrator on your target computer\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 374,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Enter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this), \" your license key when prompted during installation\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Complete\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 376,\n              columnNumber: 17\n            }, this), \" the hardware binding process (one-time setup)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Launch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 17\n            }, this), \" the tool - it will start completely invisible\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 377,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+B\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 378,\n              columnNumber: 17\n            }, this), \" to show/hide the interface\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+H\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 17\n            }, this), \" to take screenshots\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"Use Ctrl+Enter\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 17\n            }, this), \" to analyze screenshots with AI\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 372,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '20px',\n            padding: '15px',\n            background: 'rgba(255, 193, 7, 0.1)',\n            borderRadius: '10px',\n            border: '1px solid rgba(255, 193, 7, 0.3)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              marginBottom: '10px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n              size: 20,\n              style: {\n                marginRight: '10px',\n                color: '#FFC107'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"strong\", {\n              style: {\n                color: '#FFC107'\n              },\n              children: \"Important Notes:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            style: {\n              marginLeft: '30px',\n              lineHeight: '1.6'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"The software is bound to your hardware and cannot be transferred\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Keep your license key safe - you'll need it for support\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"The tool works completely in stealth mode during assessments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Contact support if you encounter any installation issues\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 383,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 290,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 289,\n    columnNumber: 5\n  }, this);\n};\n_s(Download, \"GJS0+k36Uxq0bcX1O/5BwEVOU2A=\", false, function () {\n  return [useAuth, useLocation, useNavigate];\n});\n_c14 = Download;\nexport default Download;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14;\n$RefreshReg$(_c, \"DownloadContainer\");\n$RefreshReg$(_c2, \"DownloadContent\");\n$RefreshReg$(_c3, \"SuccessHeader\");\n$RefreshReg$(_c4, \"SuccessIcon\");\n$RefreshReg$(_c5, \"SuccessTitle\");\n$RefreshReg$(_c6, \"SuccessMessage\");\n$RefreshReg$(_c7, \"LicenseInfo\");\n$RefreshReg$(_c8, \"LicenseTitle\");\n$RefreshReg$(_c9, \"LicenseDetails\");\n$RefreshReg$(_c0, \"LicenseDetail\");\n$RefreshReg$(_c1, \"LicenseKey\");\n$RefreshReg$(_c10, \"DownloadSection\");\n$RefreshReg$(_c11, \"DownloadButton\");\n$RefreshReg$(_c12, \"InstructionsSection\");\n$RefreshReg$(_c13, \"InstructionsList\");\n$RefreshReg$(_c14, \"Download\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useLocation", "useNavigate", "styled", "toast", "axios", "useAuth", "Download", "DownloadIcon", "Shield", "Key", "Monitor", "CheckCircle", "AlertCircle", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "DownloadContainer", "div", "_c", "DownloadContent", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "_c3", "SuccessIcon", "_c4", "SuccessTitle", "h1", "_c5", "SuccessMessage", "p", "_c6", "LicenseInfo", "_c7", "LicenseTitle", "h2", "_c8", "LicenseDetails", "_c9", "LicenseDetail", "_c0", "LicenseKey", "_c1", "DownloadSection", "_c10", "DownloadButton", "button", "_c11", "InstructionsSection", "_c12", "InstructionsList", "ol", "_c13", "_s", "_licenseData$plan", "_licenseData$plan2", "user", "location", "navigate", "downloading", "setDownloading", "licenseData", "setLicenseData", "downloadStatus", "setDownloadStatus", "_location$state", "state", "licenseKey", "plan", "fetchLatestLicense", "fetchDownloadStatus", "response", "get", "data", "success", "error", "console", "licenses", "length", "latestLicense", "title", "tier", "screenshots", "screenshotsLimit", "copyLicenseKey", "navigator", "clipboard", "writeText", "downloadSoftware", "post", "downloadUrl", "fileName", "link", "document", "createElement", "href", "setAttribute", "style", "display", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "Error", "message", "_error$response", "_error$response$data", "children", "textAlign", "padding", "className", "margin", "_jsxFileName", "lineNumber", "columnNumber", "size", "name", "onClick", "marginBottom", "alignItems", "marginRight", "disabled", "width", "height", "justifyContent", "opacity", "marginTop", "background", "borderRadius", "border", "color", "marginLeft", "lineHeight", "_c14", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Download.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useLocation, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Download as DownloadIcon, Shield, Key, Monitor, CheckCircle, AlertCircle } from 'lucide-react';\n\nconst DownloadContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst DownloadContent = styled.div`\n  max-width: 800px;\n  margin: 0 auto;\n`;\n\nconst SuccessHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n  padding: 40px;\n  background: rgba(76, 175, 80, 0.1);\n  border-radius: 20px;\n  border: 1px solid rgba(76, 175, 80, 0.3);\n`;\n\nconst SuccessIcon = styled.div`\n  font-size: 4rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n\nconst SuccessTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 15px;\n  color: #4CAF50;\n`;\n\nconst SuccessMessage = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n`;\n\nconst LicenseInfo = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst LicenseTitle = styled.h2`\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n  color: #4CAF50;\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n\nconst LicenseDetails = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 20px;\n`;\n\nconst LicenseDetail = styled.div`\n  text-align: center;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n  \n  .label {\n    font-size: 0.9rem;\n    opacity: 0.7;\n    margin-bottom: 5px;\n  }\n  \n  .value {\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: #4CAF50;\n  }\n`;\n\nconst LicenseKey = styled.div`\n  background: rgba(0, 0, 0, 0.3);\n  padding: 15px;\n  border-radius: 10px;\n  font-family: 'Courier New', monospace;\n  font-size: 1.1rem;\n  text-align: center;\n  margin-top: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  .copy-btn {\n    position: absolute;\n    right: 10px;\n    top: 50%;\n    transform: translateY(-50%);\n    background: #4CAF50;\n    color: white;\n    border: none;\n    padding: 5px 10px;\n    border-radius: 5px;\n    cursor: pointer;\n    font-size: 0.8rem;\n  }\n`;\n\nconst DownloadSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  margin-bottom: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst DownloadButton = styled.button`\n  width: 100%;\n  background: linear-gradient(45deg, #4CAF50, #45a049);\n  color: white;\n  border: none;\n  padding: 20px;\n  border-radius: 15px;\n  font-size: 1.2rem;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20px;\n  \n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);\n  }\n  \n  &:disabled {\n    opacity: 0.6;\n    cursor: not-allowed;\n  }\n  \n  svg {\n    margin-right: 10px;\n  }\n`;\n\nconst InstructionsSection = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  padding: 30px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst InstructionsList = styled.ol`\n  margin-left: 20px;\n  \n  li {\n    margin-bottom: 15px;\n    line-height: 1.6;\n    \n    strong {\n      color: #4CAF50;\n    }\n  }\n`;\n\nconst Download = () => {\n  const { user } = useAuth();\n  const location = useLocation();\n  const navigate = useNavigate();\n  const [downloading, setDownloading] = useState(false);\n  const [licenseData, setLicenseData] = useState(null);\n  const [downloadStatus, setDownloadStatus] = useState(null);\n\n  useEffect(() => {\n    // Get license data from navigation state or fetch from API\n    if (location.state?.licenseKey) {\n      setLicenseData({\n        licenseKey: location.state.licenseKey,\n        plan: location.state.plan\n      });\n    } else {\n      fetchLatestLicense();\n    }\n\n    // Fetch download status\n    fetchDownloadStatus();\n  }, [location.state]);\n\n  const fetchDownloadStatus = async () => {\n    try {\n      const response = await axios.get('/download/status');\n      if (response.data.success) {\n        setDownloadStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error fetching download status:', error);\n    }\n  };\n\n  const fetchLatestLicense = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success && response.data.data.licenses.length > 0) {\n        const latestLicense = response.data.data.licenses[0];\n        setLicenseData({\n          licenseKey: latestLicense.licenseKey,\n          plan: {\n            title: latestLicense.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',\n            screenshots: latestLicense.screenshotsLimit\n          }\n        });\n      } else {\n        toast.error('No valid license found. Please purchase a license first.');\n        navigate('/purchase');\n      }\n    } catch (error) {\n      toast.error('Failed to fetch license information');\n      navigate('/purchase');\n    }\n  };\n\n  const copyLicenseKey = () => {\n    if (licenseData?.licenseKey) {\n      navigator.clipboard.writeText(licenseData.licenseKey);\n      toast.success('License key copied to clipboard!');\n    }\n  };\n\n  const downloadSoftware = async () => {\n    try {\n      setDownloading(true);\n\n      // Create secure download link\n      const response = await axios.post('/download/create-link', {\n        licenseKey: licenseData.licenseKey\n      });\n\n      if (response.data.success) {\n        const { downloadUrl, fileName } = response.data.data;\n\n        // Create download link\n        const link = document.createElement('a');\n        link.href = `http://localhost:5002${downloadUrl}`;\n        link.setAttribute('download', fileName);\n        link.style.display = 'none';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n\n        toast.success('Download started! Check your downloads folder.');\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Download failed. Please try again or contact support.');\n    } finally {\n      setDownloading(false);\n    }\n  };\n\n  if (!licenseData) {\n    return (\n      <DownloadContainer>\n        <DownloadContent>\n          <div style={{ textAlign: 'center', padding: '60px 0' }}>\n            <div className=\"loading-spinner\" style={{ margin: '0 auto 20px' }}></div>\n            <p>Loading license information...</p>\n          </div>\n        </DownloadContent>\n      </DownloadContainer>\n    );\n  }\n\n  return (\n    <DownloadContainer>\n      <DownloadContent>\n        <SuccessHeader>\n          <SuccessIcon>\n            <CheckCircle size={64} />\n          </SuccessIcon>\n          <SuccessTitle>Payment Successful!</SuccessTitle>\n          <SuccessMessage>\n            Your license has been activated and you can now download the software.\n          </SuccessMessage>\n        </SuccessHeader>\n\n        <LicenseInfo>\n          <LicenseTitle>\n            <Key size={24} />\n            License Information\n          </LicenseTitle>\n\n          <LicenseDetails>\n            <LicenseDetail>\n              <div className=\"label\">Plan</div>\n              <div className=\"value\">{licenseData.plan?.title}</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">Screenshots</div>\n              <div className=\"value\">{licenseData.plan?.screenshots}/month</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">Status</div>\n              <div className=\"value\">Active</div>\n            </LicenseDetail>\n            <LicenseDetail>\n              <div className=\"label\">User</div>\n              <div className=\"value\">{user?.name}</div>\n            </LicenseDetail>\n          </LicenseDetails>\n\n          <div>\n            <strong>Your License Key:</strong>\n            <LicenseKey>\n              {licenseData.licenseKey}\n              <button className=\"copy-btn\" onClick={copyLicenseKey}>\n                Copy\n              </button>\n            </LicenseKey>\n          </div>\n        </LicenseInfo>\n\n        <DownloadSection>\n          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>\n            <DownloadIcon size={24} style={{ marginRight: '10px' }} />\n            Download Software\n          </h2>\n\n          <DownloadButton\n            onClick={downloadSoftware}\n            disabled={downloading}\n          >\n            {downloading ? (\n              <>\n                <div className=\"loading-spinner\" style={{ width: '20px', height: '20px', marginRight: '10px' }}></div>\n                Preparing Download...\n              </>\n            ) : (\n              <>\n                <DownloadIcon size={24} />\n                Download Invisible Assessment Tool\n              </>\n            )}\n          </DownloadButton>\n\n          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', opacity: 0.8 }}>\n            <Shield size={16} style={{ marginRight: '5px' }} />\n            <small>Secure download • Hardware-bound installation</small>\n          </div>\n        </DownloadSection>\n\n        <InstructionsSection>\n          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>\n            <Monitor size={24} style={{ marginRight: '10px' }} />\n            Installation Instructions\n          </h2>\n\n          <InstructionsList>\n            <li><strong>Download</strong> the software using the button above</li>\n            <li><strong>Run</strong> the installer as administrator on your target computer</li>\n            <li><strong>Enter</strong> your license key when prompted during installation</li>\n            <li><strong>Complete</strong> the hardware binding process (one-time setup)</li>\n            <li><strong>Launch</strong> the tool - it will start completely invisible</li>\n            <li><strong>Use Ctrl+B</strong> to show/hide the interface</li>\n            <li><strong>Use Ctrl+H</strong> to take screenshots</li>\n            <li><strong>Use Ctrl+Enter</strong> to analyze screenshots with AI</li>\n          </InstructionsList>\n\n          <div style={{\n            marginTop: '20px',\n            padding: '15px',\n            background: 'rgba(255, 193, 7, 0.1)',\n            borderRadius: '10px',\n            border: '1px solid rgba(255, 193, 7, 0.3)'\n          }}>\n            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>\n              <AlertCircle size={20} style={{ marginRight: '10px', color: '#FFC107' }} />\n              <strong style={{ color: '#FFC107' }}>Important Notes:</strong>\n            </div>\n            <ul style={{ marginLeft: '30px', lineHeight: '1.6' }}>\n              <li>The software is bound to your hardware and cannot be transferred</li>\n              <li>Keep your license key safe - you'll need it for support</li>\n              <li>The tool works completely in stealth mode during assessments</li>\n              <li>Contact support if you encounter any installation issues</li>\n            </ul>\n          </div>\n        </InstructionsSection>\n      </DownloadContent>\n    </DownloadContainer>\n  );\n};\n\nexport default Download;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,QAAQ,IAAIC,YAAY,EAAEC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,WAAW,EAAEC,WAAW,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAExG,MAAMC,iBAAiB,GAAGf,MAAM,CAACgB,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,eAAe,GAAGlB,MAAM,CAACgB,GAAG;AAClC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,eAAe;AAKrB,MAAME,aAAa,GAAGpB,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GAPID,aAAa;AASnB,MAAME,WAAW,GAAGtB,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACO,GAAA,GAJID,WAAW;AAMjB,MAAME,YAAY,GAAGxB,MAAM,CAACyB,EAAE;AAC9B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,YAAY;AAMlB,MAAMG,cAAc,GAAG3B,MAAM,CAAC4B,CAAC;AAC/B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,cAAc;AAKpB,MAAMG,WAAW,GAAG9B,MAAM,CAACgB,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAPID,WAAW;AASjB,MAAME,YAAY,GAAGhC,MAAM,CAACiC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GATIF,YAAY;AAWlB,MAAMG,cAAc,GAAGnC,MAAM,CAACgB,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACoB,GAAA,GALID,cAAc;AAOpB,MAAME,aAAa,GAAGrC,MAAM,CAACgB,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAjBID,aAAa;AAmBnB,MAAME,UAAU,GAAGvC,MAAM,CAACgB,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACwB,GAAA,GAxBID,UAAU;AA0BhB,MAAME,eAAe,GAAGzC,MAAM,CAACgB,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC0B,IAAA,GAPID,eAAe;AASrB,MAAME,cAAc,GAAG3C,MAAM,CAAC4C,MAAM;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GA7BIF,cAAc;AA+BpB,MAAMG,mBAAmB,GAAG9C,MAAM,CAACgB,GAAG;AACtC;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC+B,IAAA,GANID,mBAAmB;AAQzB,MAAME,gBAAgB,GAAGhD,MAAM,CAACiD,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAXIF,gBAAgB;AAatB,MAAM5C,QAAQ,GAAGA,CAAA,KAAM;EAAA+C,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAGnD,OAAO,CAAC,CAAC;EAC1B,MAAMoD,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAC9B,MAAM0D,QAAQ,GAAGzD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAAC0D,WAAW,EAAEC,cAAc,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+D,WAAW,EAAEC,cAAc,CAAC,GAAGhE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACiE,cAAc,EAAEC,iBAAiB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAE1DC,SAAS,CAAC,MAAM;IAAA,IAAAkE,eAAA;IACd;IACA,KAAAA,eAAA,GAAIR,QAAQ,CAACS,KAAK,cAAAD,eAAA,eAAdA,eAAA,CAAgBE,UAAU,EAAE;MAC9BL,cAAc,CAAC;QACbK,UAAU,EAAEV,QAAQ,CAACS,KAAK,CAACC,UAAU;QACrCC,IAAI,EAAEX,QAAQ,CAACS,KAAK,CAACE;MACvB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLC,kBAAkB,CAAC,CAAC;IACtB;;IAEA;IACAC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACb,QAAQ,CAACS,KAAK,CAAC,CAAC;EAEpB,MAAMI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMnE,KAAK,CAACoE,GAAG,CAAC,kBAAkB,CAAC;MACpD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBV,iBAAiB,CAACO,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMN,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF,MAAME,QAAQ,GAAG,MAAMnE,KAAK,CAACoE,GAAG,CAAC,uBAAuB,CAAC;MACzD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,QAAQ,CAACC,MAAM,GAAG,CAAC,EAAE;QACnE,MAAMC,aAAa,GAAGR,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC;QACpDf,cAAc,CAAC;UACbK,UAAU,EAAEY,aAAa,CAACZ,UAAU;UACpCC,IAAI,EAAE;YACJY,KAAK,EAAED,aAAa,CAACE,IAAI,KAAK,CAAC,GAAG,kBAAkB,GAAG,iBAAiB;YACxEC,WAAW,EAAEH,aAAa,CAACI;UAC7B;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACLhF,KAAK,CAACwE,KAAK,CAAC,0DAA0D,CAAC;QACvEjB,QAAQ,CAAC,WAAW,CAAC;MACvB;IACF,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdxE,KAAK,CAACwE,KAAK,CAAC,qCAAqC,CAAC;MAClDjB,QAAQ,CAAC,WAAW,CAAC;IACvB;EACF,CAAC;EAED,MAAM0B,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIvB,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEM,UAAU,EAAE;MAC3BkB,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC1B,WAAW,CAACM,UAAU,CAAC;MACrDhE,KAAK,CAACuE,OAAO,CAAC,kCAAkC,CAAC;IACnD;EACF,CAAC;EAED,MAAMc,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF5B,cAAc,CAAC,IAAI,CAAC;;MAEpB;MACA,MAAMW,QAAQ,GAAG,MAAMnE,KAAK,CAACqF,IAAI,CAAC,uBAAuB,EAAE;QACzDtB,UAAU,EAAEN,WAAW,CAACM;MAC1B,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAEgB,WAAW;UAAEC;QAAS,CAAC,GAAGpB,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAEpD;QACA,MAAMmB,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAG,wBAAwBL,WAAW,EAAE;QACjDE,IAAI,CAACI,YAAY,CAAC,UAAU,EAAEL,QAAQ,CAAC;QACvCC,IAAI,CAACK,KAAK,CAACC,OAAO,GAAG,MAAM;QAC3BL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;QAE/BzF,KAAK,CAACuE,OAAO,CAAC,gDAAgD,CAAC;MACjE,CAAC,MAAM;QACL,MAAM,IAAI6B,KAAK,CAAChC,QAAQ,CAACE,IAAI,CAAC+B,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;MAAA,IAAA8B,eAAA,EAAAC,oBAAA;MACdvG,KAAK,CAACwE,KAAK,CAAC,EAAA8B,eAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBhC,IAAI,cAAAiC,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,uDAAuD,CAAC;IACvG,CAAC,SAAS;MACR5C,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC;EAED,IAAI,CAACC,WAAW,EAAE;IAChB,oBACE/C,OAAA,CAACG,iBAAiB;MAAA0F,QAAA,eAChB7F,OAAA,CAACM,eAAe;QAAAuF,QAAA,eACd7F,OAAA;UAAKmF,KAAK,EAAE;YAAEW,SAAS,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAS,CAAE;UAAAF,QAAA,gBACrD7F,OAAA;YAAKgG,SAAS,EAAC,iBAAiB;YAACb,KAAK,EAAE;cAAEc,MAAM,EAAE;YAAc;UAAE;YAAApB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACzEpG,OAAA;YAAA6F,QAAA,EAAG;UAA8B;YAAAhB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC;MAAC;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS;IAAC;MAAAvB,QAAA,EAAAqB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAExB;EAEA,oBACEpG,OAAA,CAACG,iBAAiB;IAAA0F,QAAA,eAChB7F,OAAA,CAACM,eAAe;MAAAuF,QAAA,gBACd7F,OAAA,CAACQ,aAAa;QAAAqF,QAAA,gBACZ7F,OAAA,CAACU,WAAW;UAAAmF,QAAA,eACV7F,OAAA,CAACH,WAAW;YAACwG,IAAI,EAAE;UAAG;YAAAxB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACdpG,OAAA,CAACY,YAAY;UAAAiF,QAAA,EAAC;QAAmB;UAAAhB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAChDpG,OAAA,CAACe,cAAc;UAAA8E,QAAA,EAAC;QAEhB;UAAAhB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAgB,CAAC;MAAA;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAEhBpG,OAAA,CAACkB,WAAW;QAAA2E,QAAA,gBACV7F,OAAA,CAACoB,YAAY;UAAAyE,QAAA,gBACX7F,OAAA,CAACL,GAAG;YAAC0G,IAAI,EAAE;UAAG;YAAAxB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,uBAEnB;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAc,CAAC,eAEfpG,OAAA,CAACuB,cAAc;UAAAsE,QAAA,gBACb7F,OAAA,CAACyB,aAAa;YAAAoE,QAAA,gBACZ7F,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAI;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjCpG,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,GAAArD,iBAAA,GAAEO,WAAW,CAACO,IAAI,cAAAd,iBAAA,uBAAhBA,iBAAA,CAAkB0B;YAAK;cAAAW,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eAChBpG,OAAA,CAACyB,aAAa;YAAAoE,QAAA,gBACZ7F,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAW;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxCpG,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,IAAApD,kBAAA,GAAEM,WAAW,CAACO,IAAI,cAAAb,kBAAA,uBAAhBA,kBAAA,CAAkB2B,WAAW,EAAC,QAAM;YAAA;cAAAS,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eAChBpG,OAAA,CAACyB,aAAa;YAAAoE,QAAA,gBACZ7F,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAM;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACnCpG,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAM;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB,CAAC,eAChBpG,OAAA,CAACyB,aAAa;YAAAoE,QAAA,gBACZ7F,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAC;YAAI;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjCpG,OAAA;cAAKgG,SAAS,EAAC,OAAO;cAAAH,QAAA,EAAEnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE4D;YAAI;cAAAzB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEjBpG,OAAA;UAAA6F,QAAA,gBACE7F,OAAA;YAAA6F,QAAA,EAAQ;UAAiB;YAAAhB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClCpG,OAAA,CAAC2B,UAAU;YAAAkE,QAAA,GACR9C,WAAW,CAACM,UAAU,eACvBrD,OAAA;cAAQgG,SAAS,EAAC,UAAU;cAACO,OAAO,EAAEjC,cAAe;cAAAuB,QAAA,EAAC;YAEtD;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAEdpG,OAAA,CAAC6B,eAAe;QAAAgE,QAAA,gBACd7F,OAAA;UAAImF,KAAK,EAAE;YAAEqB,YAAY,EAAE,MAAM;YAAEpB,OAAO,EAAE,MAAM;YAAEqB,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBACzE7F,OAAA,CAACP,YAAY;YAAC4G,IAAI,EAAE,EAAG;YAAClB,KAAK,EAAE;cAAEuB,WAAW,EAAE;YAAO;UAAE;YAAA7B,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,qBAE5D;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELpG,OAAA,CAAC+B,cAAc;UACbwE,OAAO,EAAE7B,gBAAiB;UAC1BiC,QAAQ,EAAE9D,WAAY;UAAAgD,QAAA,EAErBhD,WAAW,gBACV7C,OAAA,CAAAE,SAAA;YAAA2F,QAAA,gBACE7F,OAAA;cAAKgG,SAAS,EAAC,iBAAiB;cAACb,KAAK,EAAE;gBAAEyB,KAAK,EAAE,MAAM;gBAAEC,MAAM,EAAE,MAAM;gBAAEH,WAAW,EAAE;cAAO;YAAE;cAAA7B,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,yBAExG;UAAA,eAAE,CAAC,gBAEHpG,OAAA,CAAAE,SAAA;YAAA2F,QAAA,gBACE7F,OAAA,CAACP,YAAY;cAAC4G,IAAI,EAAE;YAAG;cAAAxB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,sCAE5B;UAAA,eAAE;QACH;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACa,CAAC,eAEjBpG,OAAA;UAAKmF,KAAK,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEqB,UAAU,EAAE,QAAQ;YAAEK,cAAc,EAAE,QAAQ;YAAEC,OAAO,EAAE;UAAI,CAAE;UAAAlB,QAAA,gBAC5F7F,OAAA,CAACN,MAAM;YAAC2G,IAAI,EAAE,EAAG;YAAClB,KAAK,EAAE;cAAEuB,WAAW,EAAE;YAAM;UAAE;YAAA7B,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDpG,OAAA;YAAA6F,QAAA,EAAO;UAA6C;YAAAhB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACS,CAAC,eAElBpG,OAAA,CAACkC,mBAAmB;QAAA2D,QAAA,gBAClB7F,OAAA;UAAImF,KAAK,EAAE;YAAEqB,YAAY,EAAE,MAAM;YAAEpB,OAAO,EAAE,MAAM;YAAEqB,UAAU,EAAE;UAAS,CAAE;UAAAZ,QAAA,gBACzE7F,OAAA,CAACJ,OAAO;YAACyG,IAAI,EAAE,EAAG;YAAClB,KAAK,EAAE;cAAEuB,WAAW,EAAE;YAAO;UAAE;YAAA7B,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEvD;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELpG,OAAA,CAACoC,gBAAgB;UAAAyD,QAAA,gBACf7F,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAQ;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wCAAoC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtEpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAG;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,2DAAuD;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpFpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAK;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,uDAAmD;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClFpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAQ;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kDAA8C;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAChFpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAM;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,kDAA8C;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAU;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,+BAA2B;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/DpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAU;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,wBAAoB;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxDpG,OAAA;YAAA6F,QAAA,gBAAI7F,OAAA;cAAA6F,QAAA,EAAQ;YAAc;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,mCAA+B;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eAEnBpG,OAAA;UAAKmF,KAAK,EAAE;YACV6B,SAAS,EAAE,MAAM;YACjBjB,OAAO,EAAE,MAAM;YACfkB,UAAU,EAAE,wBAAwB;YACpCC,YAAY,EAAE,MAAM;YACpBC,MAAM,EAAE;UACV,CAAE;UAAAtB,QAAA,gBACA7F,OAAA;YAAKmF,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEqB,UAAU,EAAE,QAAQ;cAAED,YAAY,EAAE;YAAO,CAAE;YAAAX,QAAA,gBAC1E7F,OAAA,CAACF,WAAW;cAACuG,IAAI,EAAE,EAAG;cAAClB,KAAK,EAAE;gBAAEuB,WAAW,EAAE,MAAM;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAvC,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3EpG,OAAA;cAAQmF,KAAK,EAAE;gBAAEiC,KAAK,EAAE;cAAU,CAAE;cAAAvB,QAAA,EAAC;YAAgB;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNpG,OAAA;YAAImF,KAAK,EAAE;cAAEkC,UAAU,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAM,CAAE;YAAAzB,QAAA,gBACnD7F,OAAA;cAAA6F,QAAA,EAAI;YAAgE;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACzEpG,OAAA;cAAA6F,QAAA,EAAI;YAAuD;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEpG,OAAA;cAAA6F,QAAA,EAAI;YAA4D;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrEpG,OAAA;cAAA6F,QAAA,EAAI;YAAwD;cAAAhB,QAAA,EAAAqB,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAvB,QAAA,EAAAqB,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAvB,QAAA,EAAAqB,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAvB,QAAA,EAAAqB,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACa,CAAC;IAAA;MAAAvB,QAAA,EAAAqB,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP;EAAC;IAAAvB,QAAA,EAAAqB,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAExB,CAAC;AAAC7D,EAAA,CAjOI/C,QAAQ;EAAA,QACKD,OAAO,EACPL,WAAW,EACXC,WAAW;AAAA;AAAAoI,IAAA,GAHxB/H,QAAQ;AAmOd,eAAeA,QAAQ;AAAC,IAAAa,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAiF,IAAA;AAAAC,YAAA,CAAAnH,EAAA;AAAAmH,YAAA,CAAAjH,GAAA;AAAAiH,YAAA,CAAA/G,GAAA;AAAA+G,YAAA,CAAA7G,GAAA;AAAA6G,YAAA,CAAA1G,GAAA;AAAA0G,YAAA,CAAAvG,GAAA;AAAAuG,YAAA,CAAArG,GAAA;AAAAqG,YAAA,CAAAlG,GAAA;AAAAkG,YAAA,CAAAhG,GAAA;AAAAgG,YAAA,CAAA9F,GAAA;AAAA8F,YAAA,CAAA5F,GAAA;AAAA4F,YAAA,CAAA1F,IAAA;AAAA0F,YAAA,CAAAvF,IAAA;AAAAuF,YAAA,CAAArF,IAAA;AAAAqF,YAAA,CAAAlF,IAAA;AAAAkF,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}