{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, LogOut, ShoppingCart, Download } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst NavbarContainer = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  padding: 15px 20px;\n  z-index: 1000;\n`;\n_c = NavbarContainer;\nconst NavbarContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n_c2 = NavbarContent;\nconst Logo = styled(Link)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  \n  &:hover {\n    color: #4CAF50;\n    text-decoration: none;\n  }\n`;\n_c3 = Logo;\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    gap: 10px;\n  }\n`;\n_c4 = NavLinks;\nconst NavLink = styled(Link)`\n  color: white;\n  text-decoration: none;\n  font-weight: 500;\n  padding: 8px 16px;\n  border-radius: 20px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    color: #4CAF50;\n    text-decoration: none;\n  }\n`;\n_c5 = NavLink;\nconst UserMenu = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n_c6 = UserMenu;\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: white;\n  font-weight: 500;\n  \n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n_c7 = UserInfo;\nconst LogoutButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  padding: 8px 16px;\n  border-radius: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-1px);\n  }\n`;\n_c8 = LogoutButton;\nconst Navbar = () => {\n  _s();\n  const {\n    user,\n    logout,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(NavbarContainer, {\n    children: /*#__PURE__*/_jsxDEV(NavbarContent, {\n      children: [/*#__PURE__*/_jsxDEV(Logo, {\n        to: \"/\",\n        children: \"\\uD83D\\uDD75\\uFE0F\\u200D\\u2642\\uFE0F Invisible Assessment Tool\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavLinks, {\n        children: isAuthenticated ? /*#__PURE__*/_jsxDEV(UserMenu, {\n          children: [/*#__PURE__*/_jsxDEV(NavLink, {\n            to: \"/dashboard\",\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 17\n            }, this), \"Dashboard\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            to: \"/purchase\",\n            children: [/*#__PURE__*/_jsxDEV(ShoppingCart, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 17\n            }, this), \"Purchase\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            to: \"/download\",\n            children: [/*#__PURE__*/_jsxDEV(Download, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 17\n            }, this), \"Download\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(UserInfo, {\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 17\n            }, this), user === null || user === void 0 ? void 0 : user.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(LogoutButton, {\n            onClick: handleLogout,\n            children: [/*#__PURE__*/_jsxDEV(LogOut, {\n              size: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), \"Logout\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(NavLink, {\n            to: \"/login\",\n            children: \"Login\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n            to: \"/register\",\n            className: \"btn-primary\",\n            style: {\n              background: 'linear-gradient(45deg, #4CAF50, #45a049)',\n              border: 'none'\n            },\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 5\n  }, this);\n};\n_s(Navbar, \"no8SRGBJjVU8Mxe3uwjsHY/Wmig=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c9 = Navbar;\nexport default Navbar;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9;\n$RefreshReg$(_c, \"NavbarContainer\");\n$RefreshReg$(_c2, \"NavbarContent\");\n$RefreshReg$(_c3, \"Logo\");\n$RefreshReg$(_c4, \"NavLinks\");\n$RefreshReg$(_c5, \"NavLink\");\n$RefreshReg$(_c6, \"UserMenu\");\n$RefreshReg$(_c7, \"UserInfo\");\n$RefreshReg$(_c8, \"LogoutButton\");\n$RefreshReg$(_c9, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "styled", "useAuth", "User", "LogOut", "ShoppingCart", "Download", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "NavbarContainer", "nav", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "div", "_c2", "Logo", "_c3", "NavLinks", "_c4", "NavLink", "_c5", "UserMenu", "_c6", "UserInfo", "_c7", "LogoutButton", "button", "_c8", "<PERSON><PERSON><PERSON>", "_s", "user", "logout", "isAuthenticated", "navigate", "handleLogout", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "name", "onClick", "className", "style", "background", "border", "_c9", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, LogOut, ShoppingCart, Download } from 'lucide-react';\n\nconst NavbarContainer = styled.nav`\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.2);\n  padding: 15px 20px;\n  z-index: 1000;\n`;\n\nconst NavbarContent = styled.div`\n  max-width: 1200px;\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n`;\n\nconst Logo = styled(Link)`\n  font-size: 1.5rem;\n  font-weight: 700;\n  color: white;\n  text-decoration: none;\n  display: flex;\n  align-items: center;\n  \n  &:hover {\n    color: #4CAF50;\n    text-decoration: none;\n  }\n`;\n\nconst NavLinks = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  \n  @media (max-width: 768px) {\n    gap: 10px;\n  }\n`;\n\nconst NavLink = styled(Link)`\n  color: white;\n  text-decoration: none;\n  font-weight: 500;\n  padding: 8px 16px;\n  border-radius: 20px;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.1);\n    color: #4CAF50;\n    text-decoration: none;\n  }\n`;\n\nconst UserMenu = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n`;\n\nconst UserInfo = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: white;\n  font-weight: 500;\n  \n  @media (max-width: 768px) {\n    display: none;\n  }\n`;\n\nconst LogoutButton = styled.button`\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n  border: 1px solid rgba(255, 255, 255, 0.3);\n  padding: 8px 16px;\n  border-radius: 20px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  font-weight: 500;\n  \n  &:hover {\n    background: rgba(255, 255, 255, 0.2);\n    transform: translateY(-1px);\n  }\n`;\n\nconst Navbar = () => {\n  const { user, logout, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  return (\n    <NavbarContainer>\n      <NavbarContent>\n        <Logo to=\"/\">\n          🕵️‍♂️ Invisible Assessment Tool\n        </Logo>\n        \n        <NavLinks>\n          {isAuthenticated ? (\n            <UserMenu>\n              <NavLink to=\"/dashboard\">\n                <User size={18} />\n                Dashboard\n              </NavLink>\n              <NavLink to=\"/purchase\">\n                <ShoppingCart size={18} />\n                Purchase\n              </NavLink>\n              <NavLink to=\"/download\">\n                <Download size={18} />\n                Download\n              </NavLink>\n              <UserInfo>\n                <User size={18} />\n                {user?.name}\n              </UserInfo>\n              <LogoutButton onClick={handleLogout}>\n                <LogOut size={18} />\n                Logout\n              </LogoutButton>\n            </UserMenu>\n          ) : (\n            <>\n              <NavLink to=\"/login\">Login</NavLink>\n              <NavLink to=\"/register\" className=\"btn-primary\" style={{\n                background: 'linear-gradient(45deg, #4CAF50, #45a049)',\n                border: 'none'\n              }}>\n                Get Started\n              </NavLink>\n            </>\n          )}\n        </NavLinks>\n      </NavbarContent>\n    </NavbarContainer>\n  );\n};\n\nexport default Navbar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,IAAI,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,eAAe,GAAGV,MAAM,CAACW,GAAG;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GAVIF,eAAe;AAYrB,MAAMG,aAAa,GAAGb,MAAM,CAACc,GAAG;AAChC;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GANIF,aAAa;AAQnB,MAAMG,IAAI,GAAGhB,MAAM,CAACF,IAAI,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACmB,GAAA,GAZID,IAAI;AAcV,MAAME,QAAQ,GAAGlB,MAAM,CAACc,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GARID,QAAQ;AAUd,MAAME,OAAO,GAAGpB,MAAM,CAACF,IAAI,CAAC;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAhBID,OAAO;AAkBb,MAAME,QAAQ,GAAGtB,MAAM,CAACc,GAAG;AAC3B;AACA;AACA;AACA,CAAC;AAACS,GAAA,GAJID,QAAQ;AAMd,MAAME,QAAQ,GAAGxB,MAAM,CAACc,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAVID,QAAQ;AAYd,MAAME,YAAY,GAAG1B,MAAM,CAAC2B,MAAM;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAjBIF,YAAY;AAmBlB,MAAMG,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM;IAAEC,IAAI;IAAEC,MAAM;IAAEC;EAAgB,CAAC,GAAGhC,OAAO,CAAC,CAAC;EACnD,MAAMiC,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAE9B,MAAMoC,YAAY,GAAGA,CAAA,KAAM;IACzBH,MAAM,CAAC,CAAC;IACRE,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACE3B,OAAA,CAACG,eAAe;IAAA0B,QAAA,eACd7B,OAAA,CAACM,aAAa;MAAAuB,QAAA,gBACZ7B,OAAA,CAACS,IAAI;QAACqB,EAAE,EAAC,GAAG;QAAAD,QAAA,EAAC;MAEb;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEPlC,OAAA,CAACW,QAAQ;QAAAkB,QAAA,EACNH,eAAe,gBACd1B,OAAA,CAACe,QAAQ;UAAAc,QAAA,gBACP7B,OAAA,CAACa,OAAO;YAACiB,EAAE,EAAC,YAAY;YAAAD,QAAA,gBACtB7B,OAAA,CAACL,IAAI;cAACwC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAEpB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVlC,OAAA,CAACa,OAAO;YAACiB,EAAE,EAAC,WAAW;YAAAD,QAAA,gBACrB7B,OAAA,CAACH,YAAY;cAACsC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAE5B;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVlC,OAAA,CAACa,OAAO;YAACiB,EAAE,EAAC,WAAW;YAAAD,QAAA,gBACrB7B,OAAA,CAACF,QAAQ;cAACqC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,YAExB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACVlC,OAAA,CAACiB,QAAQ;YAAAY,QAAA,gBACP7B,OAAA,CAACL,IAAI;cAACwC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EACjBV,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACXlC,OAAA,CAACmB,YAAY;YAACkB,OAAO,EAAET,YAAa;YAAAC,QAAA,gBAClC7B,OAAA,CAACJ,MAAM;cAACuC,IAAI,EAAE;YAAG;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,UAEtB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,gBAEXlC,OAAA,CAAAE,SAAA;UAAA2B,QAAA,gBACE7B,OAAA,CAACa,OAAO;YAACiB,EAAE,EAAC,QAAQ;YAAAD,QAAA,EAAC;UAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,eACpClC,OAAA,CAACa,OAAO;YAACiB,EAAE,EAAC,WAAW;YAACQ,SAAS,EAAC,aAAa;YAACC,KAAK,EAAE;cACrDC,UAAU,EAAE,0CAA0C;cACtDC,MAAM,EAAE;YACV,CAAE;YAAAZ,QAAA,EAAC;UAEH;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC;QAAA,eACV;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEtB,CAAC;AAACX,EAAA,CAvDID,MAAM;EAAA,QACgC5B,OAAO,EAChCF,WAAW;AAAA;AAAAkD,GAAA,GAFxBpB,MAAM;AAyDZ,eAAeA,MAAM;AAAC,IAAAjB,EAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAqB,GAAA;AAAAC,YAAA,CAAAtC,EAAA;AAAAsC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}