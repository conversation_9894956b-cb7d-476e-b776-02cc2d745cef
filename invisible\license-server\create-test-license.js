const mongoose = require('mongoose');
const License = require('./models/License');
const User = require('./models/User');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/invisible-assessment', {
    useNewUrlParser: true,
    useUnifiedTopology: true
});

async function createTestLicense() {
    try {
        console.log('🔑 Creating test license...');
        
        // Find or create test user
        let testUser = await User.findOne({ email: '<EMAIL>' });
        
        if (!testUser) {
            testUser = new User({
                email: '<EMAIL>',
                password: 'hashedpassword123',
                name: 'Test User'
            });
            await testUser.save();
            console.log('✅ Test user created');
        }
        
        // Create test license
        const testLicense = new License({
            userId: testUser._id,
            tier: 1,
            expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
            screenshotsLimit: 50,
            paymentId: 'test_payment_123',
            paymentMethod: 'razorpay',
            amountPaid: 199,
            currency: 'INR'
        });
        
        await testLicense.save();
        
        console.log('🎉 Test license created successfully!');
        console.log('📋 License Details:');
        console.log('   License Key:', testLicense.licenseKey);
        console.log('   User Email:', testUser.email);
        console.log('   Expires:', testLicense.expiryDate.toLocaleDateString());
        console.log('   Screenshots:', testLicense.screenshotsLimit);
        console.log('');
        console.log('🔑 USE THIS LICENSE KEY IN YOUR APP:');
        console.log('   ' + testLicense.licenseKey);
        console.log('');
        console.log('💡 Copy this license key and paste it when the app asks for it!');
        
        process.exit(0);
        
    } catch (error) {
        console.error('❌ Error creating test license:', error);
        process.exit(1);
    }
}

createTestLicense();
