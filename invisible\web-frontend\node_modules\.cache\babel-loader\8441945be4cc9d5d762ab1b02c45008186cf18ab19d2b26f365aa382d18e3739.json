{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Home.js\";\nimport React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Shield, Eye, Zap, Lock, Download, CreditCard } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  overflow-x: hidden;\n`;\n_c = HomeContainer;\nconst HeroSection = styled.section`\n  padding: 120px 20px 80px;\n  text-align: center;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n_c2 = HeroSection;\nconst HeroTitle = styled.h1`\n  font-size: 4rem;\n  font-weight: 700;\n  margin-bottom: 20px;\n  background: linear-gradient(45deg, #fff, #f0f0f0);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n_c3 = HeroTitle;\nconst HeroSubtitle = styled.p`\n  font-size: 1.5rem;\n  margin-bottom: 40px;\n  opacity: 0.9;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  \n  @media (max-width: 768px) {\n    font-size: 1.2rem;\n  }\n`;\n_c4 = HeroSubtitle;\nconst CTAButtons = styled.div`\n  display: flex;\n  gap: 20px;\n  justify-content: center;\n  margin-bottom: 60px;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n  }\n`;\n_c5 = CTAButtons;\nconst PricingSection = styled.section`\n  padding: 80px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n_c6 = PricingSection;\nconst PricingTitle = styled.h2`\n  text-align: center;\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 600;\n`;\n_c7 = PricingTitle;\nconst PricingSubtitle = styled.p`\n  text-align: center;\n  font-size: 1.2rem;\n  margin-bottom: 60px;\n  opacity: 0.8;\n`;\n_c8 = PricingSubtitle;\nconst PricingCards = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 30px;\n  margin-bottom: 60px;\n`;\n_c9 = PricingCards;\nconst PricingCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  position: relative;\n  \n  &:hover {\n    transform: translateY(-10px);\n    box-shadow: 0 20px 40px rgba(0,0,0,0.2);\n  }\n  \n  &.popular {\n    border: 2px solid #4CAF50;\n  }\n`;\n_c0 = PricingCard;\nconst PopularBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n_c1 = PopularBadge;\nconst PricingAmount = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin: 20px 0;\n  color: #4CAF50;\n`;\n_c10 = PricingAmount;\nconst FeaturesList = styled.ul`\n  list-style: none;\n  margin: 30px 0;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    \n    &:before {\n      content: \"✅ \";\n      margin-right: 10px;\n    }\n  }\n`;\n_c11 = FeaturesList;\nconst FeaturesSection = styled.section`\n  padding: 80px 20px;\n  background: rgba(255, 255, 255, 0.05);\n`;\n_c12 = FeaturesSection;\nconst FeaturesGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 40px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n_c13 = FeaturesGrid;\nconst FeatureCard = styled.div`\n  text-align: center;\n  padding: 30px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: transform 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n  }\n`;\n_c14 = FeatureCard;\nconst FeatureIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n_c15 = FeatureIcon;\nconst Home = () => {\n  return /*#__PURE__*/_jsxDEV(HomeContainer, {\n    children: [/*#__PURE__*/_jsxDEV(HeroSection, {\n      children: [/*#__PURE__*/_jsxDEV(HeroTitle, {\n        children: \"\\uD83D\\uDD75\\uFE0F\\u200D\\u2642\\uFE0F Invisible Assessment Tool\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(HeroSubtitle, {\n        children: \"The Ultimate Stealth Solution for Campus Assessments\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CTAButtons, {\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"btn-primary\",\n          children: \"Get Started - \\u20B95,000\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"btn-secondary\",\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PricingSection, {\n      children: [/*#__PURE__*/_jsxDEV(PricingTitle, {\n        children: \"\\uD83D\\uDCB0 Simple Pricing\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PricingSubtitle, {\n        children: \"Choose the plan that works for you\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PricingCards, {\n        children: [/*#__PURE__*/_jsxDEV(PricingCard, {\n          className: \"popular\",\n          children: [/*#__PURE__*/_jsxDEV(PopularBadge, {\n            children: \"MOST POPULAR\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Initial Purchase\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PricingAmount, {\n            children: \"\\u20B95,000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeaturesList, {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"50 Screenshots per month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"AI-powered problem analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Complete stealth integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Hardware binding security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Global keyboard shortcuts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"All programming languages\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Lifetime software access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/register\",\n            className: \"btn-primary\",\n            style: {\n              width: '100%'\n            },\n            children: \"Purchase Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PricingCard, {\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Monthly Renewal\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(PricingAmount, {\n            children: \"\\u20B9250\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeaturesList, {\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"45 Screenshots per month\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"AI-powered problem analysis\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Complete stealth integration\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Hardware binding security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Global keyboard shortcuts\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"All programming languages\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: \"Continued access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"btn-secondary\",\n            style: {\n              width: '100%'\n            },\n            children: \"Renew License\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {\n      children: [/*#__PURE__*/_jsxDEV(PricingTitle, {\n        children: \"\\uD83D\\uDE80 Why Choose Our Solution?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeaturesGrid, {\n        children: [/*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(Eye, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"100% Invisible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Completely stealth operation with no visible traces on the system\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(Shield, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Secure Integration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Hardware-bound installation prevents unauthorized sharing\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(Zap, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 252,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"AI-Powered\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"ChatGPT integration for instant problem solving and code generation\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(Lock, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 258,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Anti-Piracy\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Advanced licensing system prevents unauthorized distribution\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(Download, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 264,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Secure Download\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Download only after payment verification and user authentication\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FeatureCard, {\n          children: [/*#__PURE__*/_jsxDEV(FeatureIcon, {\n            children: /*#__PURE__*/_jsxDEV(CreditCard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 26\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Flexible Payments\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"PayPal integration with Indian Rupee support for easy transactions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 176,\n    columnNumber: 5\n  }, this);\n};\n_c16 = Home;\nexport default Home;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12, _c13, _c14, _c15, _c16;\n$RefreshReg$(_c, \"HomeContainer\");\n$RefreshReg$(_c2, \"HeroSection\");\n$RefreshReg$(_c3, \"HeroTitle\");\n$RefreshReg$(_c4, \"HeroSubtitle\");\n$RefreshReg$(_c5, \"CTAButtons\");\n$RefreshReg$(_c6, \"PricingSection\");\n$RefreshReg$(_c7, \"PricingTitle\");\n$RefreshReg$(_c8, \"PricingSubtitle\");\n$RefreshReg$(_c9, \"PricingCards\");\n$RefreshReg$(_c0, \"PricingCard\");\n$RefreshReg$(_c1, \"PopularBadge\");\n$RefreshReg$(_c10, \"PricingAmount\");\n$RefreshReg$(_c11, \"FeaturesList\");\n$RefreshReg$(_c12, \"FeaturesSection\");\n$RefreshReg$(_c13, \"FeaturesGrid\");\n$RefreshReg$(_c14, \"FeatureCard\");\n$RefreshReg$(_c15, \"FeatureIcon\");\n$RefreshReg$(_c16, \"Home\");", "map": {"version": 3, "names": ["React", "Link", "styled", "Shield", "Eye", "Zap", "Lock", "Download", "CreditCard", "jsxDEV", "_jsxDEV", "HomeContainer", "div", "_c", "HeroSection", "section", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "h1", "_c3", "HeroSubtitle", "p", "_c4", "CTAButtons", "_c5", "PricingSection", "_c6", "PricingTitle", "h2", "_c7", "PricingSubtitle", "_c8", "PricingCards", "_c9", "PricingCard", "_c0", "PopularBadge", "_c1", "PricingAmount", "_c10", "FeaturesList", "ul", "_c11", "FeaturesSection", "_c12", "FeaturesGrid", "_c13", "FeatureCard", "_c14", "FeatureIcon", "_c15", "Home", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "className", "style", "width", "_c16", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Home.js"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport styled from 'styled-components';\nimport { Shield, Eye, Zap, Lock, Download, CreditCard } from 'lucide-react';\n\nconst HomeContainer = styled.div`\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  overflow-x: hidden;\n`;\n\nconst HeroSection = styled.section`\n  padding: 120px 20px 80px;\n  text-align: center;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst HeroTitle = styled.h1`\n  font-size: 4rem;\n  font-weight: 700;\n  margin-bottom: 20px;\n  background: linear-gradient(45deg, #fff, #f0f0f0);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n  \n  @media (max-width: 768px) {\n    font-size: 2.5rem;\n  }\n`;\n\nconst HeroSubtitle = styled.p`\n  font-size: 1.5rem;\n  margin-bottom: 40px;\n  opacity: 0.9;\n  max-width: 600px;\n  margin-left: auto;\n  margin-right: auto;\n  \n  @media (max-width: 768px) {\n    font-size: 1.2rem;\n  }\n`;\n\nconst CTAButtons = styled.div`\n  display: flex;\n  gap: 20px;\n  justify-content: center;\n  margin-bottom: 60px;\n  \n  @media (max-width: 768px) {\n    flex-direction: column;\n    align-items: center;\n  }\n`;\n\nconst PricingSection = styled.section`\n  padding: 80px 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst PricingTitle = styled.h2`\n  text-align: center;\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 600;\n`;\n\nconst PricingSubtitle = styled.p`\n  text-align: center;\n  font-size: 1.2rem;\n  margin-bottom: 60px;\n  opacity: 0.8;\n`;\n\nconst PricingCards = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n  gap: 30px;\n  margin-bottom: 60px;\n`;\n\nconst PricingCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  text-align: center;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n  position: relative;\n  \n  &:hover {\n    transform: translateY(-10px);\n    box-shadow: 0 20px 40px rgba(0,0,0,0.2);\n  }\n  \n  &.popular {\n    border: 2px solid #4CAF50;\n  }\n`;\n\nconst PopularBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n\nconst PricingAmount = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin: 20px 0;\n  color: #4CAF50;\n`;\n\nconst FeaturesList = styled.ul`\n  list-style: none;\n  margin: 30px 0;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    \n    &:before {\n      content: \"✅ \";\n      margin-right: 10px;\n    }\n  }\n`;\n\nconst FeaturesSection = styled.section`\n  padding: 80px 20px;\n  background: rgba(255, 255, 255, 0.05);\n`;\n\nconst FeaturesGrid = styled.div`\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 40px;\n  max-width: 1200px;\n  margin: 0 auto;\n`;\n\nconst FeatureCard = styled.div`\n  text-align: center;\n  padding: 30px;\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 15px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  transition: transform 0.3s ease;\n  \n  &:hover {\n    transform: translateY(-5px);\n  }\n`;\n\nconst FeatureIcon = styled.div`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  color: #4CAF50;\n`;\n\nconst Home = () => {\n  return (\n    <HomeContainer>\n      <HeroSection>\n        <HeroTitle>🕵️‍♂️ Invisible Assessment Tool</HeroTitle>\n        <HeroSubtitle>\n          The Ultimate Stealth Solution for Campus Assessments\n        </HeroSubtitle>\n        \n        <CTAButtons>\n          <Link to=\"/register\" className=\"btn-primary\">\n            Get Started - ₹5,000\n          </Link>\n          <Link to=\"/login\" className=\"btn-secondary\">\n            Login\n          </Link>\n        </CTAButtons>\n      </HeroSection>\n\n      <PricingSection>\n        <PricingTitle>💰 Simple Pricing</PricingTitle>\n        <PricingSubtitle>Choose the plan that works for you</PricingSubtitle>\n        \n        <PricingCards>\n          <PricingCard className=\"popular\">\n            <PopularBadge>MOST POPULAR</PopularBadge>\n            <h3>Initial Purchase</h3>\n            <PricingAmount>₹5,000</PricingAmount>\n            <FeaturesList>\n              <li>50 Screenshots per month</li>\n              <li>AI-powered problem analysis</li>\n              <li>Complete stealth integration</li>\n              <li>Hardware binding security</li>\n              <li>Global keyboard shortcuts</li>\n              <li>All programming languages</li>\n              <li>Lifetime software access</li>\n            </FeaturesList>\n            <Link to=\"/register\" className=\"btn-primary\" style={{width: '100%'}}>\n              Purchase Now\n            </Link>\n          </PricingCard>\n\n          <PricingCard>\n            <h3>Monthly Renewal</h3>\n            <PricingAmount>₹250</PricingAmount>\n            <FeaturesList>\n              <li>45 Screenshots per month</li>\n              <li>AI-powered problem analysis</li>\n              <li>Complete stealth integration</li>\n              <li>Hardware binding security</li>\n              <li>Global keyboard shortcuts</li>\n              <li>All programming languages</li>\n              <li>Continued access</li>\n            </FeaturesList>\n            <Link to=\"/login\" className=\"btn-secondary\" style={{width: '100%'}}>\n              Renew License\n            </Link>\n          </PricingCard>\n        </PricingCards>\n      </PricingSection>\n\n      <FeaturesSection>\n        <PricingTitle>🚀 Why Choose Our Solution?</PricingTitle>\n        \n        <FeaturesGrid>\n          <FeatureCard>\n            <FeatureIcon><Eye /></FeatureIcon>\n            <h3>100% Invisible</h3>\n            <p>Completely stealth operation with no visible traces on the system</p>\n          </FeatureCard>\n          \n          <FeatureCard>\n            <FeatureIcon><Shield /></FeatureIcon>\n            <h3>Secure Integration</h3>\n            <p>Hardware-bound installation prevents unauthorized sharing</p>\n          </FeatureCard>\n          \n          <FeatureCard>\n            <FeatureIcon><Zap /></FeatureIcon>\n            <h3>AI-Powered</h3>\n            <p>ChatGPT integration for instant problem solving and code generation</p>\n          </FeatureCard>\n          \n          <FeatureCard>\n            <FeatureIcon><Lock /></FeatureIcon>\n            <h3>Anti-Piracy</h3>\n            <p>Advanced licensing system prevents unauthorized distribution</p>\n          </FeatureCard>\n          \n          <FeatureCard>\n            <FeatureIcon><Download /></FeatureIcon>\n            <h3>Secure Download</h3>\n            <p>Download only after payment verification and user authentication</p>\n          </FeatureCard>\n          \n          <FeatureCard>\n            <FeatureIcon><CreditCard /></FeatureIcon>\n            <h3>Flexible Payments</h3>\n            <p>PayPal integration with Indian Rupee support for easy transactions</p>\n          </FeatureCard>\n        </FeaturesGrid>\n      </FeaturesSection>\n    </HomeContainer>\n  );\n};\n\nexport default Home;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,MAAM,EAAEC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,aAAa,GAAGT,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,aAAa;AAOnB,MAAMG,WAAW,GAAGZ,MAAM,CAACa,OAAO;AAClC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,WAAW;AAOjB,MAAMG,SAAS,GAAGf,MAAM,CAACgB,EAAE;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAZIF,SAAS;AAcf,MAAMG,YAAY,GAAGlB,MAAM,CAACmB,CAAC;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAXIF,YAAY;AAalB,MAAMG,UAAU,GAAGrB,MAAM,CAACU,GAAG;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACY,GAAA,GAVID,UAAU;AAYhB,MAAME,cAAc,GAAGvB,MAAM,CAACa,OAAO;AACrC;AACA;AACA;AACA,CAAC;AAACW,GAAA,GAJID,cAAc;AAMpB,MAAME,YAAY,GAAGzB,MAAM,CAAC0B,EAAE;AAC9B;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,YAAY;AAOlB,MAAMG,eAAe,GAAG5B,MAAM,CAACmB,CAAC;AAChC;AACA;AACA;AACA;AACA,CAAC;AAACU,GAAA,GALID,eAAe;AAOrB,MAAME,YAAY,GAAG9B,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA,CAAC;AAACqB,GAAA,GALID,YAAY;AAOlB,MAAME,WAAW,GAAGhC,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACuB,GAAA,GAlBID,WAAW;AAoBjB,MAAME,YAAY,GAAGlC,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACyB,GAAA,GAXID,YAAY;AAalB,MAAME,aAAa,GAAGpC,MAAM,CAACU,GAAG;AAChC;AACA;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GALID,aAAa;AAOnB,MAAME,YAAY,GAAGtC,MAAM,CAACuC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,IAAA,GAbIF,YAAY;AAelB,MAAMG,eAAe,GAAGzC,MAAM,CAACa,OAAO;AACtC;AACA;AACA,CAAC;AAAC6B,IAAA,GAHID,eAAe;AAKrB,MAAME,YAAY,GAAG3C,MAAM,CAACU,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACkC,IAAA,GANID,YAAY;AAQlB,MAAME,WAAW,GAAG7C,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACoC,IAAA,GAZID,WAAW;AAcjB,MAAME,WAAW,GAAG/C,MAAM,CAACU,GAAG;AAC9B;AACA;AACA;AACA,CAAC;AAACsC,IAAA,GAJID,WAAW;AAMjB,MAAME,IAAI,GAAGA,CAAA,KAAM;EACjB,oBACEzC,OAAA,CAACC,aAAa;IAAAyC,QAAA,gBACZ1C,OAAA,CAACI,WAAW;MAAAsC,QAAA,gBACV1C,OAAA,CAACO,SAAS;QAAAmC,QAAA,EAAC;MAAgC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAW,CAAC,eACvD9C,OAAA,CAACU,YAAY;QAAAgC,QAAA,EAAC;MAEd;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAEf9C,OAAA,CAACa,UAAU;QAAA6B,QAAA,gBACT1C,OAAA,CAACT,IAAI;UAACwD,EAAE,EAAC,WAAW;UAACC,SAAS,EAAC,aAAa;UAAAN,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACP9C,OAAA,CAACT,IAAI;UAACwD,EAAE,EAAC,QAAQ;UAACC,SAAS,EAAC,eAAe;UAAAN,QAAA,EAAC;QAE5C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEd9C,OAAA,CAACe,cAAc;MAAA2B,QAAA,gBACb1C,OAAA,CAACiB,YAAY;QAAAyB,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAC9C9C,OAAA,CAACoB,eAAe;QAAAsB,QAAA,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CAAC,eAErE9C,OAAA,CAACsB,YAAY;QAAAoB,QAAA,gBACX1C,OAAA,CAACwB,WAAW;UAACwB,SAAS,EAAC,SAAS;UAAAN,QAAA,gBAC9B1C,OAAA,CAAC0B,YAAY;YAAAgB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc,CAAC,eACzC9C,OAAA;YAAA0C,QAAA,EAAI;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzB9C,OAAA,CAAC4B,aAAa;YAAAc,QAAA,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eACrC9C,OAAA,CAAC8B,YAAY;YAAAY,QAAA,gBACX1C,OAAA;cAAA0C,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9C,OAAA;cAAA0C,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpC9C,OAAA;cAAA0C,QAAA,EAAI;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,eACf9C,OAAA,CAACT,IAAI;YAACwD,EAAE,EAAC,WAAW;YAACC,SAAS,EAAC,aAAa;YAACC,KAAK,EAAE;cAACC,KAAK,EAAE;YAAM,CAAE;YAAAR,QAAA,EAAC;UAErE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAEd9C,OAAA,CAACwB,WAAW;UAAAkB,QAAA,gBACV1C,OAAA;YAAA0C,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB9C,OAAA,CAAC4B,aAAa;YAAAc,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eACnC9C,OAAA,CAAC8B,YAAY;YAAAY,QAAA,gBACX1C,OAAA;cAAA0C,QAAA,EAAI;YAAwB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjC9C,OAAA;cAAA0C,QAAA,EAAI;YAA2B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpC9C,OAAA;cAAA0C,QAAA,EAAI;YAA4B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClC9C,OAAA;cAAA0C,QAAA,EAAI;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC,eACf9C,OAAA,CAACT,IAAI;YAACwD,EAAE,EAAC,QAAQ;YAACC,SAAS,EAAC,eAAe;YAACC,KAAK,EAAE;cAACC,KAAK,EAAE;YAAM,CAAE;YAAAR,QAAA,EAAC;UAEpE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEjB9C,OAAA,CAACiC,eAAe;MAAAS,QAAA,gBACd1C,OAAA,CAACiB,YAAY;QAAAyB,QAAA,EAAC;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAc,CAAC,eAExD9C,OAAA,CAACmC,YAAY;QAAAO,QAAA,gBACX1C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACN,GAAG;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClC9C,OAAA;YAAA0C,QAAA,EAAI;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACvB9C,OAAA;YAAA0C,QAAA,EAAG;UAAiE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eAEd9C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACP,MAAM;cAAAkD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACrC9C,OAAA;YAAA0C,QAAA,EAAI;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3B9C,OAAA;YAAA0C,QAAA,EAAG;UAAyD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eAEd9C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACL,GAAG;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eAClC9C,OAAA;YAAA0C,QAAA,EAAI;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnB9C,OAAA;YAAA0C,QAAA,EAAG;UAAmE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC,eAEd9C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACJ,IAAI;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACnC9C,OAAA;YAAA0C,QAAA,EAAI;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpB9C,OAAA;YAAA0C,QAAA,EAAG;UAA4D;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAEd9C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACH,QAAQ;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACvC9C,OAAA;YAAA0C,QAAA,EAAI;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxB9C,OAAA;YAAA0C,QAAA,EAAG;UAAgE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5D,CAAC,eAEd9C,OAAA,CAACqC,WAAW;UAAAK,QAAA,gBACV1C,OAAA,CAACuC,WAAW;YAAAG,QAAA,eAAC1C,OAAA,CAACF,UAAU;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACzC9C,OAAA;YAAA0C,QAAA,EAAI;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1B9C,OAAA;YAAA0C,QAAA,EAAG;UAAkE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEpB,CAAC;AAACK,IAAA,GAxGIV,IAAI;AA0GV,eAAeA,IAAI;AAAC,IAAAtC,EAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,IAAA,EAAAG,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAE,IAAA,EAAAW,IAAA;AAAAC,YAAA,CAAAjD,EAAA;AAAAiD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAA3C,GAAA;AAAA2C,YAAA,CAAAxC,GAAA;AAAAwC,YAAA,CAAAtC,GAAA;AAAAsC,YAAA,CAAApC,GAAA;AAAAoC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAA7B,GAAA;AAAA6B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,IAAA;AAAAuB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAlB,IAAA;AAAAkB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAd,IAAA;AAAAc,YAAA,CAAAZ,IAAA;AAAAY,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}