{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst UserX2 = createLucideIcon(\"UserX2\", [[\"path\", {\n  d: \"M14 19a6 6 0 0 0-12 0\",\n  key: \"vej9p1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"9\",\n  r: \"4\",\n  key: \"143rtg\"\n}], [\"line\", {\n  x1: \"17\",\n  x2: \"22\",\n  y1: \"8\",\n  y2: \"13\",\n  key: \"3nzzx3\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"17\",\n  y1: \"8\",\n  y2: \"13\",\n  key: \"1swrse\"\n}]]);\nexport { UserX2 as default };", "map": {"version": 3, "names": ["UserX2", "createLucideIcon", "d", "key", "cx", "cy", "r", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\user-x-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name UserX2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQgMTlhNiA2IDAgMCAwLTEyIDAiIC8+CiAgPGNpcmNsZSBjeD0iOCIgY3k9IjkiIHI9IjQiIC8+CiAgPGxpbmUgeDE9IjE3IiB4Mj0iMjIiIHkxPSI4IiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjIyIiB4Mj0iMTciIHkxPSI4IiB5Mj0iMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/user-x-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserX2 = createLucideIcon('UserX2', [\n  ['path', { d: 'M14 19a6 6 0 0 0-12 0', key: 'vej9p1' }],\n  ['circle', { cx: '8', cy: '9', r: '4', key: '143rtg' }],\n  ['line', { x1: '17', x2: '22', y1: '8', y2: '13', key: '3nzzx3' }],\n  ['line', { x1: '22', x2: '17', y1: '8', y2: '13', key: '1swrse' }],\n]);\n\nexport default UserX2;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAP,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAQ;EAAEI,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAP,GAAA,EAAK;AAAA,CAAU,EAClE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}