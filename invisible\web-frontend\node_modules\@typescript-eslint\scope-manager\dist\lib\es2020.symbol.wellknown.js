"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/scope-manager
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2020_symbol_wellknown = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_symbol_1 = require("./es2015.symbol");
exports.es2020_symbol_wellknown = Object.assign(Object.assign(Object.assign({}, es2015_iterable_1.es2015_iterable), es2015_symbol_1.es2015_symbol), { SymbolConstructor: base_config_1.TYPE, RegExp: base_config_1.TYPE });
//# sourceMappingURL=es2020.symbol.wellknown.js.map