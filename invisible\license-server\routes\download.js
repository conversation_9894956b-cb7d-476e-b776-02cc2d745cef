const express = require('express');
const path = require('path');
const fs = require('fs');
const { verifyToken } = require('../middleware/auth');
const License = require('../models/License');
const User = require('../models/User');

const router = express.Router();

// 📁 DOWNLOAD DIRECTORY
const DOWNLOADS_DIR = path.join(__dirname, '../downloads');

// Ensure downloads directory exists
if (!fs.existsSync(DOWNLOADS_DIR)) {
    fs.mkdirSync(DOWNLOADS_DIR, { recursive: true });
    console.log('📁 Created downloads directory:', DOWNLOADS_DIR);
}

// 📥 GET DOWNLOAD LINK
router.post('/create-link', verifyToken, async (req, res) => {
    try {
        const { licenseKey } = req.body;

        if (!licenseKey) {
            return res.status(400).json({
                success: false,
                message: 'License key is required'
            });
        }

        // Find and validate license
        const license = await License.findOne({
            licenseKey,
            userId: req.userId
        });

        if (!license) {
            return res.status(404).json({
                success: false,
                message: 'License not found'
            });
        }

        if (!license.isValid()) {
            return res.status(403).json({
                success: false,
                message: 'License is expired or invalid'
            });
        }

        // Check if software file exists
        const softwareFileName = 'InvisibleAssessmentTool.exe';
        const softwareFilePath = path.join(DOWNLOADS_DIR, softwareFileName);

        if (!fs.existsSync(softwareFilePath)) {
            return res.status(404).json({
                success: false,
                message: 'Software file not found. Please contact support.'
            });
        }

        // Generate secure download token (valid for 1 hour)
        const downloadToken = Buffer.from(JSON.stringify({
            licenseKey,
            userId: req.userId,
            timestamp: Date.now(),
            expires: Date.now() + (60 * 60 * 1000) // 1 hour
        })).toString('base64');

        res.json({
            success: true,
            message: 'Download link created successfully',
            data: {
                downloadUrl: `/api/download/software/${downloadToken}`,
                fileName: softwareFileName,
                expiresIn: '1 hour',
                fileSize: fs.statSync(softwareFilePath).size
            }
        });

    } catch (error) {
        console.error('❌ Download link creation error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to create download link'
        });
    }
});

// 📥 DOWNLOAD SOFTWARE
router.get('/software/:token', async (req, res) => {
    try {
        const { token } = req.params;

        // Decode and validate token
        let tokenData;
        try {
            tokenData = JSON.parse(Buffer.from(token, 'base64').toString());
        } catch (error) {
            return res.status(400).json({
                success: false,
                message: 'Invalid download token'
            });
        }

        // Check if token is expired
        if (Date.now() > tokenData.expires) {
            return res.status(410).json({
                success: false,
                message: 'Download link has expired'
            });
        }

        // Validate license
        const license = await License.findOne({
            licenseKey: tokenData.licenseKey,
            userId: tokenData.userId
        });

        if (!license || !license.isValid()) {
            return res.status(403).json({
                success: false,
                message: 'Invalid or expired license'
            });
        }

        // Check if software file exists
        const softwareFileName = 'InvisibleAssessmentTool.exe';
        const softwareFilePath = path.join(DOWNLOADS_DIR, softwareFileName);

        if (!fs.existsSync(softwareFilePath)) {
            return res.status(404).json({
                success: false,
                message: 'Software file not found'
            });
        }

        // Set download headers for .exe file
        res.setHeader('Content-Disposition', `attachment; filename="${softwareFileName}"`);
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Length', fs.statSync(softwareFilePath).size);
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('X-Content-Type-Options', 'nosniff');

        // Stream the file
        const fileStream = fs.createReadStream(softwareFilePath);
        fileStream.on('error', (err) => {
            console.error('❌ File stream error:', err);
            if (!res.headersSent) {
                res.status(500).json({
                    success: false,
                    message: 'File streaming error'
                });
            }
        });

        fileStream.pipe(res);

        // Log download
        console.log(`📥 Software downloaded by user ${tokenData.userId} with license ${tokenData.licenseKey}`);

    } catch (error) {
        console.error('❌ Software download error:', error);
        res.status(500).json({
            success: false,
            message: 'Download failed'
        });
    }
});

// 📊 GET DOWNLOAD STATUS
router.get('/status', verifyToken, async (req, res) => {
    try {
        const user = await User.findById(req.userId);
        const licenses = await License.find({ userId: req.userId });

        const validLicenses = licenses.filter(license => license.isValid());

        res.json({
            success: true,
            data: {
                hasValidLicense: validLicenses.length > 0,
                totalLicenses: licenses.length,
                validLicenses: validLicenses.length,
                licenses: validLicenses.map(license => ({
                    licenseKey: license.licenseKey,
                    tier: license.tier,
                    screenshotsRemaining: license.getRemainingScreenshots(),
                    daysRemaining: license.getDaysRemaining(),
                    canDownload: license.isValid()
                }))
            }
        });

    } catch (error) {
        console.error('❌ Download status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get download status'
        });
    }
});

module.exports = router;
