{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BookOpen = createLucideIcon(\"BookOpen\", [[\"path\", {\n  d: \"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z\",\n  key: \"vv98re\"\n}], [\"path\", {\n  d: \"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z\",\n  key: \"1cyq3y\"\n}]]);\nexport { BookOpen as default };", "map": {"version": 3, "names": ["BookOpen", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\book-open.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BookOpen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAzaDZhNCA0IDAgMCAxIDQgNHYxNGEzIDMgMCAwIDAtMy0zSDJ6IiAvPgogIDxwYXRoIGQ9Ik0yMiAzaC02YTQgNCAwIDAgMC00IDR2MTRhMyAzIDAgMCAxIDMtM2g3eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/book-open\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BookOpen = createLucideIcon('BookOpen', [\n  ['path', { d: 'M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z', key: 'vv98re' }],\n  ['path', { d: 'M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z', key: '1cyq3y' }],\n]);\n\nexport default BookOpen;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CAAC,MAAQ;EAAED,CAAA,EAAG,4CAA8C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}