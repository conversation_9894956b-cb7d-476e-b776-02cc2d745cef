{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Loader = createLucideIcon(\"Loader\", [[\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"2\",\n  y2: \"6\",\n  key: \"gza1u7\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"18\",\n  y2: \"22\",\n  key: \"1qhbu9\"\n}], [\"line\", {\n  x1: \"4.93\",\n  x2: \"7.76\",\n  y1: \"4.93\",\n  y2: \"7.76\",\n  key: \"xae44r\"\n}], [\"line\", {\n  x1: \"16.24\",\n  x2: \"19.07\",\n  y1: \"16.24\",\n  y2: \"19.07\",\n  key: \"bxnmvf\"\n}], [\"line\", {\n  x1: \"2\",\n  x2: \"6\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"89khin\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"22\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"pb8tfm\"\n}], [\"line\", {\n  x1: \"4.93\",\n  x2: \"7.76\",\n  y1: \"19.07\",\n  y2: \"16.24\",\n  key: \"1uxjnu\"\n}], [\"line\", {\n  x1: \"16.24\",\n  x2: \"19.07\",\n  y1: \"7.76\",\n  y2: \"4.93\",\n  key: \"6duxfx\"\n}]]);\nexport { Loader as default };", "map": {"version": 3, "names": ["Loader", "createLucideIcon", "x1", "x2", "y1", "y2", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\loader.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Loader\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTgiIHkyPSIyMiIgLz4KICA8bGluZSB4MT0iNC45MyIgeDI9IjcuNzYiIHkxPSI0LjkzIiB5Mj0iNy43NiIgLz4KICA8bGluZSB4MT0iMTYuMjQiIHgyPSIxOS4wNyIgeTE9IjE2LjI0IiB5Mj0iMTkuMDciIC8+CiAgPGxpbmUgeDE9IjIiIHgyPSI2IiB5MT0iMTIiIHkyPSIxMiIgLz4KICA8bGluZSB4MT0iMTgiIHgyPSIyMiIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGxpbmUgeDE9IjQuOTMiIHgyPSI3Ljc2IiB5MT0iMTkuMDciIHkyPSIxNi4yNCIgLz4KICA8bGluZSB4MT0iMTYuMjQiIHgyPSIxOS4wNyIgeTE9IjcuNzYiIHkyPSI0LjkzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/loader\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Loader = createLucideIcon('Loader', [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '6', key: 'gza1u7' }],\n  ['line', { x1: '12', x2: '12', y1: '18', y2: '22', key: '1qhbu9' }],\n  ['line', { x1: '4.93', x2: '7.76', y1: '4.93', y2: '7.76', key: 'xae44r' }],\n  [\n    'line',\n    { x1: '16.24', x2: '19.07', y1: '16.24', y2: '19.07', key: 'bxnmvf' },\n  ],\n  ['line', { x1: '2', x2: '6', y1: '12', y2: '12', key: '89khin' }],\n  ['line', { x1: '18', x2: '22', y1: '12', y2: '12', key: 'pb8tfm' }],\n  ['line', { x1: '4.93', x2: '7.76', y1: '19.07', y2: '16.24', key: '1uxjnu' }],\n  ['line', { x1: '16.24', x2: '19.07', y1: '7.76', y2: '4.93', key: '6duxfx' }],\n]);\n\nexport default Loader;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CACE,QACA;EAAEJ,EAAI;EAASC,EAAI;EAASC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,QAAQ;EAAEJ,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEJ,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC5E,CAAC,QAAQ;EAAEJ,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,OAAS;EAAAC,EAAA,EAAI,MAAQ;EAAAC,EAAA,EAAI,MAAQ;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}