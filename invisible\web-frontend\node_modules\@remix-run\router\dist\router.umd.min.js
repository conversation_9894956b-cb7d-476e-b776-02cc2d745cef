/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).RemixRouter={})}(this,(function(e){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},t.apply(this,arguments)}let r=function(e){return e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE",e}({});const a="popstate";function n(e,t){if(!1===e||null==e)throw new Error(t)}function o(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(e){}}}function i(e,t){return{usr:e.state,key:e.key,idx:t}}function s(e,r,a,n){return void 0===a&&(a=null),t({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof r?u(r):r,{state:a,key:r&&r.key||n||Math.random().toString(36).substr(2,8)})}function l(e){let{pathname:t="/",search:r="",hash:a=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),a&&"#"!==a&&(t+="#"===a.charAt(0)?a:"#"+a),t}function u(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let a=e.indexOf("?");a>=0&&(t.search=e.substr(a),e=e.substr(0,a)),e&&(t.pathname=e)}return t}function c(e,o,u,c){void 0===c&&(c={});let{window:d=document.defaultView,v5Compat:h=!1}=c,f=d.history,p=r.Pop,m=null,y=v();function v(){return(f.state||{idx:null}).idx}function g(){p=r.Pop;let e=v(),t=null==e?null:e-y;y=e,m&&m({action:p,location:w.location,delta:t})}function b(e){let t="null"!==d.location.origin?d.location.origin:d.location.href,r="string"==typeof e?e:l(e);return r=r.replace(/ $/,"%20"),n(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}null==y&&(y=0,f.replaceState(t({},f.state,{idx:y}),""));let w={get action(){return p},get location(){return e(d,f)},listen(e){if(m)throw new Error("A history only accepts one active listener");return d.addEventListener(a,g),m=e,()=>{d.removeEventListener(a,g),m=null}},createHref:e=>o(d,e),createURL:b,encodeLocation(e){let t=b(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){p=r.Push;let a=s(w.location,e,t);u&&u(a,e),y=v()+1;let n=i(a,y),o=w.createHref(a);try{f.pushState(n,"",o)}catch(e){if(e instanceof DOMException&&"DataCloneError"===e.name)throw e;d.location.assign(o)}h&&m&&m({action:p,location:w.location,delta:1})},replace:function(e,t){p=r.Replace;let a=s(w.location,e,t);u&&u(a,e),y=v();let n=i(a,y),o=w.createHref(a);f.replaceState(n,"",o),h&&m&&m({action:p,location:w.location,delta:0})},go:e=>f.go(e)};return w}let d=function(e){return e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error",e}({});const h=new Set(["lazy","caseSensitive","path","id","index","children"]);function f(e,r,a,o){return void 0===a&&(a=[]),void 0===o&&(o={}),e.map(((e,i)=>{let s=[...a,String(i)],l="string"==typeof e.id?e.id:s.join("-");if(n(!0!==e.index||!e.children,"Cannot specify children on an index route"),n(!o[l],'Found a route id collision on id "'+l+"\".  Route id's must be globally unique within Data Router usages"),function(e){return!0===e.index}(e)){let a=t({},e,r(e),{id:l});return o[l]=a,a}{let a=t({},e,r(e),{id:l,children:void 0});return o[l]=a,e.children&&(a.children=f(e.children,r,s,o)),a}}))}function p(e,t,r){return void 0===r&&(r="/"),m(e,t,r,!1)}function m(e,t,r,a){let n=P(("string"==typeof t?u(t):t).pathname||"/",r);if(null==n)return null;let o=v(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){return e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]))?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let e=0;null==i&&e<o.length;++e){let t=E(n);i=D(o[e],t,a)}return i}function y(e,t){let{route:r,pathname:a,params:n}=e;return{id:r.id,pathname:a,params:n,data:t[r.id],handle:r.handle}}function v(e,t,r,a){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===a&&(a="");let o=(e,o,i)=>{let s={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};s.relativePath.startsWith("/")&&(n(s.relativePath.startsWith(a),'Absolute route path "'+s.relativePath+'" nested under path "'+a+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(a.length));let l=k([a,s.relativePath]),u=r.concat(s);e.children&&e.children.length>0&&(n(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+l+'".'),v(e.children,t,u,l)),(null!=e.path||e.index)&&t.push({path:l,score:S(l,e.index),routesMeta:u})};return e.forEach(((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let r of g(e.path))o(e,t,r);else o(e,t)})),t}function g(e){let t=e.split("/");if(0===t.length)return[];let[r,...a]=t,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(0===a.length)return n?[o,""]:[o];let i=g(a.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),n&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}const b=/^:[\w-]+$/,w=e=>"*"===e;function S(e,t){let r=e.split("/"),a=r.length;return r.some(w)&&(a+=-2),t&&(a+=2),r.filter((e=>!w(e))).reduce(((e,t)=>e+(b.test(t)?3:""===t?1:10)),a)}function D(e,t,r){void 0===r&&(r=!1);let{routesMeta:a}=e,n={},o="/",i=[];for(let e=0;e<a.length;++e){let s=a[e],l=e===a.length-1,u="/"===o?t:t.slice(o.length)||"/",c=R({path:s.relativePath,caseSensitive:s.caseSensitive,end:l},u),d=s.route;if(!c&&l&&r&&!a[a.length-1].route.index&&(c=R({path:s.relativePath,caseSensitive:s.caseSensitive,end:!1},u)),!c)return null;Object.assign(n,c.params),i.push({params:n,pathname:k([o,c.pathname]),pathnameBase:C(k([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=k([o,c.pathnameBase]))}return i}function R(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=function(e,t,r){void 0===t&&(t=!1);void 0===r&&(r=!0);o("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(a.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(a.push({paramName:"*"}),n+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":""!==e&&"/"!==e&&(n+="(?:(?=\\/|$))");return[new RegExp(n,t?void 0:"i"),a]}(e.path,e.caseSensitive,e.end),n=t.match(r);if(!n)return null;let i=n[0],s=i.replace(/(.)\/+$/,"$1"),l=n.slice(1);return{params:a.reduce(((e,t,r)=>{let{paramName:a,isOptional:n}=t;if("*"===a){let e=l[r]||"";s=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const o=l[r];return e[a]=n&&!o?void 0:(o||"").replace(/%2F/g,"/"),e}),{}),pathname:i,pathnameBase:s,pattern:e}}function E(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return o(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function P(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,a=e.charAt(r);return a&&"/"!==a?null:e.slice(r)||"/"}function x(e,t){void 0===t&&(t="/");let{pathname:r,search:a="",hash:n=""}="string"==typeof e?u(e):e,o=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:o,search:_(a),hash:T(n)}}function L(e,t,r,a){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(a)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function A(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function M(e,t){let r=A(e);return t?r.map(((e,t)=>t===r.length-1?e.pathname:e.pathnameBase)):r.map((e=>e.pathnameBase))}function j(e,r,a,o){let i;void 0===o&&(o=!1),"string"==typeof e?i=u(e):(i=t({},e),n(!i.pathname||!i.pathname.includes("?"),L("?","pathname","search",i)),n(!i.pathname||!i.pathname.includes("#"),L("#","pathname","hash",i)),n(!i.search||!i.search.includes("#"),L("#","search","hash",i)));let s,l=""===e||""===i.pathname,c=l?"/":i.pathname;if(null==c)s=a;else{let e=r.length-1;if(!o&&c.startsWith("..")){let t=c.split("/");for(;".."===t[0];)t.shift(),e-=1;i.pathname=t.join("/")}s=e>=0?r[e]:"/"}let d=x(i,s),h=c&&"/"!==c&&c.endsWith("/"),f=(l||"."===c)&&a.endsWith("/");return d.pathname.endsWith("/")||!h&&!f||(d.pathname+="/"),d}const k=e=>e.join("/").replace(/\/\/+/g,"/"),C=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),_=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",T=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";class U{constructor(e,t){this.type="DataWithResponseInit",this.data=e,this.init=t||null}}class O extends Error{}class H{constructor(e,t){let r;this.pendingKeysSet=new Set,this.subscribers=new Set,this.deferredKeys=[],n(e&&"object"==typeof e&&!Array.isArray(e),"defer() only accepts plain objects"),this.abortPromise=new Promise(((e,t)=>r=t)),this.controller=new AbortController;let a=()=>r(new O("Deferred data aborted"));this.unlistenAbortSignal=()=>this.controller.signal.removeEventListener("abort",a),this.controller.signal.addEventListener("abort",a),this.data=Object.entries(e).reduce(((e,t)=>{let[r,a]=t;return Object.assign(e,{[r]:this.trackPromise(r,a)})}),{}),this.done&&this.unlistenAbortSignal(),this.init=t}trackPromise(e,t){if(!(t instanceof Promise))return t;this.deferredKeys.push(e),this.pendingKeysSet.add(e);let r=Promise.race([t,this.abortPromise]).then((t=>this.onSettle(r,e,void 0,t)),(t=>this.onSettle(r,e,t)));return r.catch((()=>{})),Object.defineProperty(r,"_tracked",{get:()=>!0}),r}onSettle(e,t,r,a){if(this.controller.signal.aborted&&r instanceof O)return this.unlistenAbortSignal(),Object.defineProperty(e,"_error",{get:()=>r}),Promise.reject(r);if(this.pendingKeysSet.delete(t),this.done&&this.unlistenAbortSignal(),void 0===r&&void 0===a){let r=new Error('Deferred data for key "'+t+'" resolved/rejected with `undefined`, you must resolve/reject with a value or `null`.');return Object.defineProperty(e,"_error",{get:()=>r}),this.emit(!1,t),Promise.reject(r)}return void 0===a?(Object.defineProperty(e,"_error",{get:()=>r}),this.emit(!1,t),Promise.reject(r)):(Object.defineProperty(e,"_data",{get:()=>a}),this.emit(!1,t),a)}emit(e,t){this.subscribers.forEach((r=>r(e,t)))}subscribe(e){return this.subscribers.add(e),()=>this.subscribers.delete(e)}cancel(){this.controller.abort(),this.pendingKeysSet.forEach(((e,t)=>this.pendingKeysSet.delete(t))),this.emit(!0)}async resolveData(e){let t=!1;if(!this.done){let r=()=>this.cancel();e.addEventListener("abort",r),t=await new Promise((t=>{this.subscribe((a=>{e.removeEventListener("abort",r),(a||this.done)&&t(a)}))}))}return t}get done(){return 0===this.pendingKeysSet.size}get unwrappedData(){return n(null!==this.data&&this.done,"Can only unwrap data on initialized and settled deferreds"),Object.entries(this.data).reduce(((e,t)=>{let[r,a]=t;return Object.assign(e,{[r]:I(a)})}),{})}get pendingKeys(){return Array.from(this.pendingKeysSet)}}function I(e){if(!function(e){return e instanceof Promise&&!0===e._tracked}(e))return e;if(e._error)throw e._error;return e._data}const z=function(e,r){void 0===r&&(r=302);let a=r;"number"==typeof a?a={status:a}:void 0===a.status&&(a.status=302);let n=new Headers(a.headers);return n.set("Location",e),new Response(null,t({},a,{headers:n}))};class F{constructor(e,t,r,a){void 0===a&&(a=!1),this.status=e,this.statusText=t||"",this.internal=a,r instanceof Error?(this.data=r.toString(),this.error=r):this.data=r}}function N(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const B=["post","put","patch","delete"],W=new Set(B),$=["get",...B],q=new Set($),K=new Set([301,302,303,307,308]),Y=new Set([307,308]),J={state:"idle",location:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},V={state:"idle",data:void 0,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0},X={state:"unblocked",proceed:void 0,reset:void 0,location:void 0},G=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Q=e=>({hasErrorBoundary:Boolean(e.hasErrorBoundary)}),Z="remix-router-transitions";const ee=Symbol("deferred");function te(e,t,r){if(r.v7_throwAbortReason&&void 0!==e.signal.reason)throw e.signal.reason;throw new Error((t?"queryRoute":"query")+"() call aborted: "+e.method+" "+e.url)}function re(e,t,r,a,n,o,i,s){let u,c;if(i){u=[];for(let e of t)if(u.push(e),e.route.id===i){c=e;break}}else u=t,c=t[t.length-1];let d=j(n||".",M(u,o),P(e.pathname,r)||e.pathname,"path"===s);if(null==n&&(d.search=e.search,d.hash=e.hash),(null==n||""===n||"."===n)&&c){let e=Fe(d.search);if(c.route.index&&!e)d.search=d.search?d.search.replace(/^\?/,"?index&"):"?index";else if(!c.route.index&&e){let e=new URLSearchParams(d.search),t=e.getAll("index");e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let r=e.toString();d.search=r?"?"+r:""}}return a&&"/"!==r&&(d.pathname="/"===d.pathname?r:k([r,d.pathname])),l(d)}function ae(e,t,r,a){if(!a||!function(e){return null!=e&&("formData"in e&&null!=e.formData||"body"in e&&void 0!==e.body)}(a))return{path:r};if(a.formMethod&&!Ue(a.formMethod))return{path:r,error:Pe(405,{method:a.formMethod})};let o,i,s=()=>({path:r,error:Pe(400,{type:"invalid-body"})}),c=a.formMethod||"get",d=e?c.toUpperCase():c.toLowerCase(),h=Le(r);if(void 0!==a.body){if("text/plain"===a.formEncType){if(!Oe(d))return s();let e="string"==typeof a.body?a.body:a.body instanceof FormData||a.body instanceof URLSearchParams?Array.from(a.body.entries()).reduce(((e,t)=>{let[r,a]=t;return""+e+r+"="+a+"\n"}),""):String(a.body);return{path:r,submission:{formMethod:d,formAction:h,formEncType:a.formEncType,formData:void 0,json:void 0,text:e}}}if("application/json"===a.formEncType){if(!Oe(d))return s();try{let e="string"==typeof a.body?JSON.parse(a.body):a.body;return{path:r,submission:{formMethod:d,formAction:h,formEncType:a.formEncType,formData:void 0,json:e,text:void 0}}}catch(e){return s()}}}if(n("function"==typeof FormData,"FormData is not available in this environment"),a.formData)o=ve(a.formData),i=a.formData;else if(a.body instanceof FormData)o=ve(a.body),i=a.body;else if(a.body instanceof URLSearchParams)o=a.body,i=ge(o);else if(null==a.body)o=new URLSearchParams,i=new FormData;else try{o=new URLSearchParams(a.body),i=ge(o)}catch(e){return s()}let f={formMethod:d,formAction:h,formEncType:a&&a.formEncType||"application/x-www-form-urlencoded",formData:i,json:void 0,text:void 0};if(Oe(f.formMethod))return{path:r,submission:f};let p=u(r);return t&&p.search&&Fe(p.search)&&o.append("index",""),p.search="?"+o,{path:l(p),submission:f}}function ne(e,t,r){void 0===r&&(r=!1);let a=e.findIndex((e=>e.route.id===t));return a>=0?e.slice(0,r?a+1:a):e}function oe(e,r,a,n,o,i,s,l,u,c,d,h,f,m,y,v){let g=v?je(v[1])?v[1].error:v[1].data:void 0,b=e.createURL(r.location),w=e.createURL(o),S=a;i&&r.errors?S=ne(a,Object.keys(r.errors)[0],!0):v&&je(v[1])&&(S=ne(a,v[0]));let D=v?v[1].statusCode:void 0,R=s&&D&&D>=400,E=S.filter(((e,a)=>{let{route:o}=e;if(o.lazy)return!0;if(null==o.loader)return!1;if(i)return ie(o,r.loaderData,r.errors);if(function(e,t,r){let a=!t||r.route.id!==t.route.id,n=void 0===e[r.route.id];return a||n}(r.loaderData,r.matches[a],e)||u.some((t=>t===e.route.id)))return!0;let s=r.matches[a],c=e;return le(e,t({currentUrl:b,currentParams:s.params,nextUrl:w,nextParams:c.params},n,{actionResult:g,actionStatus:D,defaultShouldRevalidate:!R&&(l||b.pathname+b.search===w.pathname+w.search||b.search!==w.search||se(s,c))}))})),P=[];return h.forEach(((e,o)=>{if(i||!a.some((t=>t.route.id===e.routeId))||d.has(o))return;let s=p(m,e.path,y);if(!s)return void P.push({key:o,routeId:e.routeId,path:e.path,matches:null,match:null,controller:null});let u=r.fetchers.get(o),h=Ne(s,e.path),v=!1;f.has(o)?v=!1:c.has(o)?(c.delete(o),v=!0):v=u&&"idle"!==u.state&&void 0===u.data?l:le(h,t({currentUrl:b,currentParams:r.matches[r.matches.length-1].params,nextUrl:w,nextParams:a[a.length-1].params},n,{actionResult:g,actionStatus:D,defaultShouldRevalidate:!R&&l})),v&&P.push({key:o,routeId:e.routeId,path:e.path,matches:s,match:h,controller:new AbortController})})),[E,P]}function ie(e,t,r){if(e.lazy)return!0;if(!e.loader)return!1;let a=null!=t&&void 0!==t[e.id],n=null!=r&&void 0!==r[e.id];return!(!a&&n)&&("function"==typeof e.loader&&!0===e.loader.hydrate||!a&&!n)}function se(e,t){let r=e.route.path;return e.pathname!==t.pathname||null!=r&&r.endsWith("*")&&e.params["*"]!==t.params["*"]}function le(e,t){if(e.route.shouldRevalidate){let r=e.route.shouldRevalidate(t);if("boolean"==typeof r)return r}return t.defaultShouldRevalidate}function ue(e,t,r,a,o){var i;let s;if(e){let t=a[e];n(t,"No route found to patch children into: routeId = "+e),t.children||(t.children=[]),s=t.children}else s=r;let l=f(t.filter((e=>!s.some((t=>ce(e,t))))),o,[e||"_","patch",String((null==(i=s)?void 0:i.length)||"0")],a);s.push(...l)}function ce(e,t){return"id"in e&&"id"in t&&e.id===t.id||e.index===t.index&&e.path===t.path&&e.caseSensitive===t.caseSensitive&&(!(e.children&&0!==e.children.length||t.children&&0!==t.children.length)||e.children.every(((e,r)=>{var a;return null==(a=t.children)?void 0:a.some((t=>ce(e,t)))})))}async function de(e){let{matches:t}=e,r=t.filter((e=>e.shouldLoad));return(await Promise.all(r.map((e=>e.resolve())))).reduce(((e,t,a)=>Object.assign(e,{[r[a].route.id]:t})),{})}async function he(e,r,a,i,s,l,u,c,f,p){let m=l.map((e=>e.route.lazy?async function(e,r,a){if(!e.lazy)return;let i=await e.lazy();if(!e.lazy)return;let s=a[e.id];n(s,"No route found in manifest");let l={};for(let e in i){let t=void 0!==s[e]&&"hasErrorBoundary"!==e;o(!t,'Route "'+s.id+'" has a static property "'+e+'" defined but its lazy function is also returning a value for this property. The lazy route property "'+e+'" will be ignored.'),t||h.has(e)||(l[e]=i[e])}Object.assign(s,l),Object.assign(s,t({},r(s),{lazy:void 0}))}(e.route,f,c):void 0)),y=l.map(((e,a)=>{let o=m[a],l=s.some((t=>t.route.id===e.route.id));return t({},e,{shouldLoad:l,resolve:async t=>(t&&"GET"===i.method&&(e.route.lazy||e.route.loader)&&(l=!0),l?async function(e,t,r,a,o,i){let s,l,u=a=>{let n,s=new Promise(((e,t)=>n=t));l=()=>n(),t.signal.addEventListener("abort",l);let u=n=>"function"!=typeof a?Promise.reject(new Error('You cannot call the handler for a route which defines a boolean "'+e+'" [routeId: '+r.route.id+"]")):a({request:t,params:r.params,context:i},...void 0!==n?[n]:[]),c=(async()=>{try{return{type:"data",result:await(o?o((e=>u(e))):u())}}catch(e){return{type:"error",result:e}}})();return Promise.race([c,s])};try{let o=r.route[e];if(a)if(o){let e,[t]=await Promise.all([u(o).catch((t=>{e=t})),a]);if(void 0!==e)throw e;s=t}else{if(await a,o=r.route[e],!o){if("action"===e){let e=new URL(t.url),a=e.pathname+e.search;throw Pe(405,{method:t.method,pathname:a,routeId:r.route.id})}return{type:d.data,result:void 0}}s=await u(o)}else{if(!o){let e=new URL(t.url);throw Pe(404,{pathname:e.pathname+e.search})}s=await u(o)}n(void 0!==s.result,"You defined "+("action"===e?"an action":"a loader")+' for route "'+r.route.id+"\" but didn't return anything from your `"+e+"` function. Please return a value or `null`.")}catch(e){return{type:d.error,result:e}}finally{l&&t.signal.removeEventListener("abort",l)}return s}(r,i,e,o,t,p):Promise.resolve({type:d.data,result:void 0}))})})),v=await e({matches:y,request:i,params:l[0].params,fetcherKey:u,context:p});try{await Promise.all(m)}catch(e){}return v}async function fe(e){let{result:t,type:r}=e;if(Te(t)){let e;try{let r=t.headers.get("Content-Type");e=r&&/\bapplication\/json\b/.test(r)?null==t.body?null:await t.json():await t.text()}catch(e){return{type:d.error,error:e}}return r===d.error?{type:d.error,error:new F(t.status,t.statusText,e),statusCode:t.status,headers:t.headers}:{type:d.data,data:e,statusCode:t.status,headers:t.headers}}var a,n,o,i,s,l,u,c;return r===d.error?Ce(t)?t.data instanceof Error?{type:d.error,error:t.data,statusCode:null==(o=t.init)?void 0:o.status,headers:null!=(i=t.init)&&i.headers?new Headers(t.init.headers):void 0}:{type:d.error,error:new F((null==(a=t.init)?void 0:a.status)||500,void 0,t.data),statusCode:N(t)?t.status:void 0,headers:null!=(n=t.init)&&n.headers?new Headers(t.init.headers):void 0}:{type:d.error,error:t,statusCode:N(t)?t.status:void 0}:_e(t)?{type:d.deferred,deferredData:t,statusCode:null==(s=t.init)?void 0:s.status,headers:(null==(l=t.init)?void 0:l.headers)&&new Headers(t.init.headers)}:Ce(t)?{type:d.data,data:t.data,statusCode:null==(u=t.init)?void 0:u.status,headers:null!=(c=t.init)&&c.headers?new Headers(t.init.headers):void 0}:{type:d.data,data:t}}function pe(e,t,r,a,o,i){let s=e.headers.get("Location");if(n(s,"Redirects returned/thrown from loaders/actions must have a Location header"),!G.test(s)){let n=a.slice(0,a.findIndex((e=>e.route.id===r))+1);s=re(new URL(t.url),n,o,!0,s,i),e.headers.set("Location",s)}return e}function me(e,t,r){if(G.test(e)){let a=e,n=a.startsWith("//")?new URL(t.protocol+a):new URL(a),o=null!=P(n.pathname,r);if(n.origin===t.origin&&o)return n.pathname+n.search+n.hash}return e}function ye(e,t,r,a){let n=e.createURL(Le(t)).toString(),o={signal:r};if(a&&Oe(a.formMethod)){let{formMethod:e,formEncType:t}=a;o.method=e.toUpperCase(),"application/json"===t?(o.headers=new Headers({"Content-Type":t}),o.body=JSON.stringify(a.json)):"text/plain"===t?o.body=a.text:"application/x-www-form-urlencoded"===t&&a.formData?o.body=ve(a.formData):o.body=a.formData}return new Request(n,o)}function ve(e){let t=new URLSearchParams;for(let[r,a]of e.entries())t.append(r,"string"==typeof a?a:a.name);return t}function ge(e){let t=new FormData;for(let[r,a]of e.entries())t.append(r,a);return t}function be(e,t,r,a,o){let i,s={},l=null,u=!1,c={},d=r&&je(r[1])?r[1].error:void 0;return e.forEach((r=>{if(!(r.route.id in t))return;let h=r.route.id,f=t[h];if(n(!ke(f),"Cannot handle redirect results in processLoaderData"),je(f)){let t=f.error;if(void 0!==d&&(t=d,d=void 0),l=l||{},o)l[h]=t;else{let r=Re(e,h);null==l[r.route.id]&&(l[r.route.id]=t)}s[h]=void 0,u||(u=!0,i=N(f.error)?f.error.status:500),f.headers&&(c[h]=f.headers)}else Me(f)?(a.set(h,f.deferredData),s[h]=f.deferredData.data,null==f.statusCode||200===f.statusCode||u||(i=f.statusCode),f.headers&&(c[h]=f.headers)):(s[h]=f.data,f.statusCode&&200!==f.statusCode&&!u&&(i=f.statusCode),f.headers&&(c[h]=f.headers))})),void 0!==d&&r&&(l={[r[0]]:d},s[r[0]]=void 0),{loaderData:s,errors:l,statusCode:i||200,loaderHeaders:c}}function we(e,r,a,o,i,s,l){let{loaderData:u,errors:c}=be(r,a,o,l,!1);return i.forEach((r=>{let{key:a,match:o,controller:i}=r,l=s[a];if(n(l,"Did not find corresponding fetcher result"),!i||!i.signal.aborted)if(je(l)){let r=Re(e.matches,null==o?void 0:o.route.id);c&&c[r.route.id]||(c=t({},c,{[r.route.id]:l.error})),e.fetchers.delete(a)}else if(ke(l))n(!1,"Unhandled fetcher revalidation redirect");else if(Me(l))n(!1,"Unhandled fetcher deferred data");else{let t=Ke(l.data);e.fetchers.set(a,t)}})),{loaderData:u,errors:c}}function Se(e,r,a,n){let o=t({},r);for(let t of a){let a=t.route.id;if(r.hasOwnProperty(a)?void 0!==r[a]&&(o[a]=r[a]):void 0!==e[a]&&t.route.loader&&(o[a]=e[a]),n&&n.hasOwnProperty(a))break}return o}function De(e){return e?je(e[1])?{actionData:{}}:{actionData:{[e[0]]:e[1].data}}:{}}function Re(e,t){return(t?e.slice(0,e.findIndex((e=>e.route.id===t))+1):[...e]).reverse().find((e=>!0===e.route.hasErrorBoundary))||e[0]}function Ee(e){let t=1===e.length?e[0]:e.find((e=>e.index||!e.path||"/"===e.path))||{id:"__shim-error-route__"};return{matches:[{params:{},pathname:"",pathnameBase:"",route:t}],route:t}}function Pe(e,t){let{pathname:r,routeId:a,method:n,type:o,message:i}=void 0===t?{}:t,s="Unknown Server Error",l="Unknown @remix-run/router error";return 400===e?(s="Bad Request",n&&r&&a?l="You made a "+n+' request to "'+r+'" but did not provide a `loader` for route "'+a+'", so there is no way to handle the request.':"defer-action"===o?l="defer() is not supported in actions":"invalid-body"===o&&(l="Unable to encode submission body")):403===e?(s="Forbidden",l='Route "'+a+'" does not match URL "'+r+'"'):404===e?(s="Not Found",l='No route matches URL "'+r+'"'):405===e&&(s="Method Not Allowed",n&&r&&a?l="You made a "+n.toUpperCase()+' request to "'+r+'" but did not provide an `action` for route "'+a+'", so there is no way to handle the request.':n&&(l='Invalid request method "'+n.toUpperCase()+'"')),new F(e||500,s,new Error(l),!0)}function xe(e){let t=Object.entries(e);for(let e=t.length-1;e>=0;e--){let[r,a]=t[e];if(ke(a))return{key:r,result:a}}}function Le(e){return l(t({},"string"==typeof e?u(e):e,{hash:""}))}function Ae(e){return Te(e.result)&&K.has(e.result.status)}function Me(e){return e.type===d.deferred}function je(e){return e.type===d.error}function ke(e){return(e&&e.type)===d.redirect}function Ce(e){return"object"==typeof e&&null!=e&&"type"in e&&"data"in e&&"init"in e&&"DataWithResponseInit"===e.type}function _e(e){let t=e;return t&&"object"==typeof t&&"object"==typeof t.data&&"function"==typeof t.subscribe&&"function"==typeof t.cancel&&"function"==typeof t.resolveData}function Te(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"object"==typeof e.headers&&void 0!==e.body}function Ue(e){return q.has(e.toLowerCase())}function Oe(e){return W.has(e.toLowerCase())}async function He(e,t,r,a,n){let o=Object.entries(t);for(let i=0;i<o.length;i++){let[s,l]=o[i],u=e.find((e=>(null==e?void 0:e.route.id)===s));if(!u)continue;let c=a.find((e=>e.route.id===u.route.id)),d=null!=c&&!se(c,u)&&void 0!==(n&&n[u.route.id]);Me(l)&&d&&await ze(l,r,!1).then((e=>{e&&(t[s]=e)}))}}async function Ie(e,t,r){for(let a=0;a<r.length;a++){let{key:o,routeId:i,controller:s}=r[a],l=t[o];e.find((e=>(null==e?void 0:e.route.id)===i))&&(Me(l)&&(n(s,"Expected an AbortController for revalidating fetcher deferred result"),await ze(l,s.signal,!0).then((e=>{e&&(t[o]=e)}))))}}async function ze(e,t,r){if(void 0===r&&(r=!1),!await e.deferredData.resolveData(t)){if(r)try{return{type:d.data,data:e.deferredData.unwrappedData}}catch(e){return{type:d.error,error:e}}return{type:d.data,data:e.deferredData.data}}}function Fe(e){return new URLSearchParams(e).getAll("index").some((e=>""===e))}function Ne(e,t){let r="string"==typeof t?u(t).search:t.search;if(e[e.length-1].route.index&&Fe(r||""))return e[e.length-1];let a=A(e);return a[a.length-1]}function Be(e){let{formMethod:t,formAction:r,formEncType:a,text:n,formData:o,json:i}=e;if(t&&r&&a)return null!=n?{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:void 0,text:n}:null!=o?{formMethod:t,formAction:r,formEncType:a,formData:o,json:void 0,text:void 0}:void 0!==i?{formMethod:t,formAction:r,formEncType:a,formData:void 0,json:i,text:void 0}:void 0}function We(e,t){if(t){return{state:"loading",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}return{state:"loading",location:e,formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0}}function $e(e,t){return{state:"submitting",location:e,formMethod:t.formMethod,formAction:t.formAction,formEncType:t.formEncType,formData:t.formData,json:t.json,text:t.text}}function qe(e,t){if(e){return{state:"loading",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t}}return{state:"loading",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:t}}function Ke(e){return{state:"idle",formMethod:void 0,formAction:void 0,formEncType:void 0,formData:void 0,json:void 0,text:void 0,data:e}}e.AbortedDeferredError=O,e.Action=r,e.IDLE_BLOCKER=X,e.IDLE_FETCHER=V,e.IDLE_NAVIGATION=J,e.UNSAFE_DEFERRED_SYMBOL=ee,e.UNSAFE_DeferredData=H,e.UNSAFE_ErrorResponseImpl=F,e.UNSAFE_convertRouteMatchToUiMatch=y,e.UNSAFE_convertRoutesToDataRoutes=f,e.UNSAFE_decodePath=E,e.UNSAFE_getResolveToMatches=M,e.UNSAFE_invariant=n,e.UNSAFE_warning=o,e.createBrowserHistory=function(e){return void 0===e&&(e={}),c((function(e,t){let{pathname:r,search:a,hash:n}=e.location;return s("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:l(t)}),null,e)},e.createHashHistory=function(e){return void 0===e&&(e={}),c((function(e,t){let{pathname:r="/",search:a="",hash:n=""}=u(e.location.hash.substr(1));return r.startsWith("/")||r.startsWith(".")||(r="/"+r),s("",{pathname:r,search:a,hash:n},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let r=e.document.querySelector("base"),a="";if(r&&r.getAttribute("href")){let t=e.location.href,r=t.indexOf("#");a=-1===r?t:t.slice(0,r)}return a+"#"+("string"==typeof t?t:l(t))}),(function(e,t){o("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")}),e)},e.createMemoryHistory=function(e){void 0===e&&(e={});let t,{initialEntries:a=["/"],initialIndex:n,v5Compat:i=!1}=e;t=a.map(((e,t)=>m(e,"string"==typeof e?null:e.state,0===t?"default":void 0)));let c=f(null==n?t.length-1:n),d=r.Pop,h=null;function f(e){return Math.min(Math.max(e,0),t.length-1)}function p(){return t[c]}function m(e,r,a){void 0===r&&(r=null);let n=s(t?p().pathname:"/",e,r,a);return o("/"===n.pathname.charAt(0),"relative pathnames are not supported in memory history: "+JSON.stringify(e)),n}function y(e){return"string"==typeof e?e:l(e)}return{get index(){return c},get action(){return d},get location(){return p()},createHref:y,createURL:e=>new URL(y(e),"http://localhost"),encodeLocation(e){let t="string"==typeof e?u(e):e;return{pathname:t.pathname||"",search:t.search||"",hash:t.hash||""}},push(e,a){d=r.Push;let n=m(e,a);c+=1,t.splice(c,t.length,n),i&&h&&h({action:d,location:n,delta:1})},replace(e,a){d=r.Replace;let n=m(e,a);t[c]=n,i&&h&&h({action:d,location:n,delta:0})},go(e){d=r.Pop;let a=f(c+e),n=t[a];c=a,h&&h({action:d,location:n,delta:e})},listen:e=>(h=e,()=>{h=null})}},e.createPath=l,e.createRouter=function(e){const a=e.window?e.window:"undefined"!=typeof window?window:void 0,i=void 0!==a&&void 0!==a.document&&void 0!==a.document.createElement,l=!i;let u;if(n(e.routes.length>0,"You must provide a non-empty routes array to createRouter"),e.mapRouteProperties)u=e.mapRouteProperties;else if(e.detectErrorBoundary){let t=e.detectErrorBoundary;u=e=>({hasErrorBoundary:t(e)})}else u=Q;let c,h,v,g={},b=f(e.routes,u,void 0,g),w=e.basename||"/",S=e.dataStrategy||de,D=e.patchRoutesOnNavigation,R=t({v7_fetcherPersist:!1,v7_normalizeFormMethod:!1,v7_partialHydration:!1,v7_prependBasename:!1,v7_relativeSplatPath:!1,v7_skipActionErrorRevalidation:!1},e.future),E=null,x=new Set,L=null,A=null,M=null,j=null!=e.hydrationData,k=p(b,e.history.location,w),C=!1,_=null;if(null==k&&!D){let t=Pe(404,{pathname:e.history.location.pathname}),{matches:r,route:a}=Ee(b);k=r,_={[a.id]:t}}if(k&&!e.hydrationData){dt(k,b,e.history.location.pathname).active&&(k=null)}if(k)if(k.some((e=>e.route.lazy)))h=!1;else if(k.some((e=>e.route.loader)))if(R.v7_partialHydration){let t=e.hydrationData?e.hydrationData.loaderData:null,r=e.hydrationData?e.hydrationData.errors:null;if(r){let e=k.findIndex((e=>void 0!==r[e.route.id]));h=k.slice(0,e+1).every((e=>!ie(e.route,t,r)))}else h=k.every((e=>!ie(e.route,t,r)))}else h=null!=e.hydrationData;else h=!0;else if(h=!1,k=[],R.v7_partialHydration){let t=dt(null,b,e.history.location.pathname);t.active&&t.matches&&(C=!0,k=t.matches)}let T,U,O={historyAction:e.history.action,location:e.history.location,matches:k,initialized:h,navigation:J,restoreScrollPosition:null==e.hydrationData&&null,preventScrollReset:!1,revalidation:"idle",loaderData:e.hydrationData&&e.hydrationData.loaderData||{},actionData:e.hydrationData&&e.hydrationData.actionData||null,errors:e.hydrationData&&e.hydrationData.errors||_,fetchers:new Map,blockers:new Map},H=r.Pop,I=!1,z=!1,F=new Map,B=null,W=!1,$=!1,q=[],K=new Set,ee=new Map,te=0,ne=-1,se=new Map,le=new Set,ce=new Map,ve=new Map,ge=new Set,be=new Map,Le=new Map;function Ce(e,r){void 0===r&&(r={}),O=t({},O,e);let a=[],n=[];R.v7_fetcherPersist&&O.fetchers.forEach(((e,t)=>{"idle"===e.state&&(ge.has(t)?n.push(t):a.push(t))})),ge.forEach((e=>{O.fetchers.has(e)||ee.has(e)||n.push(e)})),[...x].forEach((e=>e(O,{deletedFetchers:n,viewTransitionOpts:r.viewTransitionOpts,flushSync:!0===r.flushSync}))),R.v7_fetcherPersist?(a.forEach((e=>O.fetchers.delete(e))),n.forEach((e=>Ze(e)))):n.forEach((e=>ge.delete(e)))}function _e(a,n,o){var i,s;let l,{flushSync:u}=void 0===o?{}:o,d=null!=O.actionData&&null!=O.navigation.formMethod&&Oe(O.navigation.formMethod)&&"loading"===O.navigation.state&&!0!==(null==(i=a.state)?void 0:i._isRedirect);l=n.actionData?Object.keys(n.actionData).length>0?n.actionData:null:d?O.actionData:null;let h=n.loaderData?Se(O.loaderData,n.loaderData,n.matches||[],n.errors):O.loaderData,f=O.blockers;f.size>0&&(f=new Map(f),f.forEach(((e,t)=>f.set(t,X))));let p,m=!0===I||null!=O.navigation.formMethod&&Oe(O.navigation.formMethod)&&!0!==(null==(s=a.state)?void 0:s._isRedirect);if(c&&(b=c,c=void 0),W||H===r.Pop||(H===r.Push?e.history.push(a,a.state):H===r.Replace&&e.history.replace(a,a.state)),H===r.Pop){let e=F.get(O.location.pathname);e&&e.has(a.pathname)?p={currentLocation:O.location,nextLocation:a}:F.has(a.pathname)&&(p={currentLocation:a,nextLocation:O.location})}else if(z){let e=F.get(O.location.pathname);e?e.add(a.pathname):(e=new Set([a.pathname]),F.set(O.location.pathname,e)),p={currentLocation:O.location,nextLocation:a}}Ce(t({},n,{actionData:l,loaderData:h,historyAction:H,location:a,initialized:!0,navigation:J,revalidation:"idle",restoreScrollPosition:ct(a,n.matches||O.matches),preventScrollReset:m,blockers:f}),{viewTransitionOpts:p,flushSync:!0===u}),H=r.Pop,I=!1,z=!1,W=!1,$=!1,q=[]}async function Te(a,n,o){T&&T.abort(),T=null,H=a,W=!0===(o&&o.startUninterruptedRevalidation),function(e,t){if(L&&M){let r=ut(e,t);L[r]=M()}}(O.location,O.matches),I=!0===(o&&o.preventScrollReset),z=!0===(o&&o.enableViewTransition);let i=c||b,s=o&&o.overrideNavigation,l=null!=o&&o.initialHydration&&O.matches&&O.matches.length>0&&!C?O.matches:p(i,n,w),u=!0===(o&&o.flushSync);if(l&&O.initialized&&!$&&function(e,t){if(e.pathname!==t.pathname||e.search!==t.search)return!1;if(""===e.hash)return""!==t.hash;if(e.hash===t.hash)return!0;if(""!==t.hash)return!0;return!1}(O.location,n)&&!(o&&o.submission&&Oe(o.submission.formMethod)))return void _e(n,{matches:l},{flushSync:u});let h=dt(l,i,n.pathname);if(h.active&&h.matches&&(l=h.matches),!l){let{error:e,notFoundMatches:t,route:r}=st(n.pathname);return void _e(n,{matches:t,loaderData:{},errors:{[r.id]:e}},{flushSync:u})}T=new AbortController;let f,m=ye(e.history,n,T.signal,o&&o.submission);if(o&&o.pendingError)f=[Re(l).route.id,{type:d.error,error:o.pendingError}];else if(o&&o.submission&&Oe(o.submission.formMethod)){let t=await async function(e,t,a,n,o,i){void 0===i&&(i={});let s;if(Ve(),Ce({navigation:$e(t,a)},{flushSync:!0===i.flushSync}),o){let r=await ht(n,t.pathname,e.signal);if("aborted"===r.type)return{shortCircuited:!0};if("error"===r.type){let e=Re(r.partialMatches).route.id;return{matches:r.partialMatches,pendingActionResult:[e,{type:d.error,error:r.error}]}}if(!r.matches){let{notFoundMatches:e,error:r,route:a}=st(t.pathname);return{matches:e,pendingActionResult:[a.id,{type:d.error,error:r}]}}n=r.matches}let l=Ne(n,t);if(l.route.action||l.route.lazy){if(s=(await Ye("action",O,e,[l],n,null))[l.route.id],e.signal.aborted)return{shortCircuited:!0}}else s={type:d.error,error:Pe(405,{method:e.method,pathname:t.pathname,routeId:l.route.id})};if(ke(s)){let t;if(i&&null!=i.replace)t=i.replace;else{t=me(s.response.headers.get("Location"),new URL(e.url),w)===O.location.pathname+O.location.search}return await Fe(e,s,!0,{submission:a,replace:t}),{shortCircuited:!0}}if(Me(s))throw Pe(400,{type:"defer-action"});if(je(s)){let e=Re(n,l.route.id);return!0!==(i&&i.replace)&&(H=r.Push),{matches:n,pendingActionResult:[e.route.id,s]}}return{matches:n,pendingActionResult:[l.route.id,s]}}(m,n,o.submission,l,h.active,{replace:o.replace,flushSync:u});if(t.shortCircuited)return;if(t.pendingActionResult){let[e,r]=t.pendingActionResult;if(je(r)&&N(r.error)&&404===r.error.status)return T=null,void _e(n,{matches:t.matches,loaderData:{},errors:{[e]:r.error}})}l=t.matches||l,f=t.pendingActionResult,s=We(n,o.submission),u=!1,h.active=!1,m=ye(e.history,m.url,m.signal)}let{shortCircuited:y,matches:v,loaderData:g,errors:S}=await async function(r,a,n,o,i,s,l,u,d,h,f){let p=i||We(a,s),m=s||l||Be(p),y=!(W||R.v7_partialHydration&&d);if(o){if(y){let e=Ue(f);Ce(t({navigation:p},void 0!==e?{actionData:e}:{}),{flushSync:h})}let e=await ht(n,a.pathname,r.signal);if("aborted"===e.type)return{shortCircuited:!0};if("error"===e.type){let t=Re(e.partialMatches).route.id;return{matches:e.partialMatches,loaderData:{},errors:{[t]:e.error}}}if(!e.matches){let{error:e,notFoundMatches:t,route:r}=st(a.pathname);return{matches:t,loaderData:{},errors:{[r.id]:e}}}n=e.matches}let v=c||b,[g,S]=oe(e.history,O,n,m,a,R.v7_partialHydration&&!0===d,R.v7_skipActionErrorRevalidation,$,q,K,ge,ce,le,v,w,f);if(lt((e=>!(n&&n.some((t=>t.route.id===e)))||g&&g.some((t=>t.route.id===e)))),ne=++te,0===g.length&&0===S.length){let e=rt();return _e(a,t({matches:n,loaderData:{},errors:f&&je(f[1])?{[f[0]]:f[1].error}:null},De(f),e?{fetchers:new Map(O.fetchers)}:{}),{flushSync:h}),{shortCircuited:!0}}if(y){let e={};if(!o){e.navigation=p;let t=Ue(f);void 0!==t&&(e.actionData=t)}S.length>0&&(e.fetchers=function(e){return e.forEach((e=>{let t=O.fetchers.get(e.key),r=qe(void 0,t?t.data:void 0);O.fetchers.set(e.key,r)})),new Map(O.fetchers)}(S)),Ce(e,{flushSync:h})}S.forEach((e=>{et(e.key),e.controller&&ee.set(e.key,e.controller)}));let D=()=>S.forEach((e=>et(e.key)));T&&T.signal.addEventListener("abort",D);let{loaderResults:E,fetcherResults:P}=await Je(O,n,g,S,r);if(r.signal.aborted)return{shortCircuited:!0};T&&T.signal.removeEventListener("abort",D);S.forEach((e=>ee.delete(e.key)));let x=xe(E);if(x)return await Fe(r,x.result,!0,{replace:u}),{shortCircuited:!0};if(x=xe(P),x)return le.add(x.key),await Fe(r,x.result,!0,{replace:u}),{shortCircuited:!0};let{loaderData:L,errors:A}=we(O,n,E,f,S,P,be);be.forEach(((e,t)=>{e.subscribe((r=>{(r||e.done)&&be.delete(t)}))})),R.v7_partialHydration&&d&&O.errors&&(A=t({},O.errors,A));let M=rt(),j=at(ne),k=M||j||S.length>0;return t({matches:n,loaderData:L,errors:A},k?{fetchers:new Map(O.fetchers)}:{})}(m,n,l,h.active,s,o&&o.submission,o&&o.fetcherSubmission,o&&o.replace,o&&!0===o.initialHydration,u,f);y||(T=null,_e(n,t({matches:v||l},De(f),{loaderData:g,errors:S})))}function Ue(e){return e&&!je(e[1])?{[e[0]]:e[1].data}:O.actionData?0===Object.keys(O.actionData).length?null:O.actionData:void 0}async function Fe(o,l,u,c){let{submission:d,fetcherSubmission:h,preventScrollReset:f,replace:p}=void 0===c?{}:c;l.response.headers.has("X-Remix-Revalidate")&&($=!0);let m=l.response.headers.get("Location");n(m,"Expected a Location header on the redirect Response"),m=me(m,new URL(o.url),w);let y=s(O.location,m,{_isRedirect:!0});if(i){let t=!1;if(l.response.headers.has("X-Remix-Reload-Document"))t=!0;else if(G.test(m)){const r=e.history.createURL(m);t=r.origin!==a.location.origin||null==P(r.pathname,w)}if(t)return void(p?a.location.replace(m):a.location.assign(m))}T=null;let v=!0===p||l.response.headers.has("X-Remix-Replace")?r.Replace:r.Push,{formMethod:g,formAction:b,formEncType:S}=O.navigation;!d&&!h&&g&&b&&S&&(d=Be(O.navigation));let D=d||h;if(Y.has(l.response.status)&&D&&Oe(D.formMethod))await Te(v,y,{submission:t({},D,{formAction:m}),preventScrollReset:f||I,enableViewTransition:u?z:void 0});else{let e=We(y,d);await Te(v,y,{overrideNavigation:e,fetcherSubmission:h,preventScrollReset:f||I,enableViewTransition:u?z:void 0})}}async function Ye(e,t,r,a,n,o){let i,s={};try{i=await he(S,e,t,r,a,n,o,g,u)}catch(e){return a.forEach((t=>{s[t.route.id]={type:d.error,error:e}})),s}for(let[e,t]of Object.entries(i))if(Ae(t)){let a=t.result;s[e]={type:d.redirect,response:pe(a,r,e,n,w,R.v7_relativeSplatPath)}}else s[e]=await fe(t);return s}async function Je(t,r,a,n,o){let i=t.matches,s=Ye("loader",t,o,a,r,null),l=Promise.all(n.map((async r=>{if(r.matches&&r.match&&r.controller){let a=(await Ye("loader",t,ye(e.history,r.path,r.controller.signal),[r.match],r.matches,r.key))[r.match.route.id];return{[r.key]:a}}return Promise.resolve({[r.key]:{type:d.error,error:Pe(404,{pathname:r.path})}})}))),u=await s,c=(await l).reduce(((e,t)=>Object.assign(e,t)),{});return await Promise.all([He(r,u,o.signal,i,t.loaderData),Ie(r,c,n)]),{loaderResults:u,fetcherResults:c}}function Ve(){$=!0,q.push(...lt()),ce.forEach(((e,t)=>{ee.has(t)&&K.add(t),et(t)}))}function Xe(e,t,r){void 0===r&&(r={}),O.fetchers.set(e,t),Ce({fetchers:new Map(O.fetchers)},{flushSync:!0===(r&&r.flushSync)})}function Ge(e,t,r,a){void 0===a&&(a={});let n=Re(O.matches,t);Ze(e),Ce({errors:{[n.route.id]:r},fetchers:new Map(O.fetchers)},{flushSync:!0===(a&&a.flushSync)})}function Qe(e){return ve.set(e,(ve.get(e)||0)+1),ge.has(e)&&ge.delete(e),O.fetchers.get(e)||V}function Ze(e){let t=O.fetchers.get(e);!ee.has(e)||t&&"loading"===t.state&&se.has(e)||et(e),ce.delete(e),se.delete(e),le.delete(e),R.v7_fetcherPersist&&ge.delete(e),K.delete(e),O.fetchers.delete(e)}function et(e){let t=ee.get(e);t&&(t.abort(),ee.delete(e))}function tt(e){for(let t of e){let e=Ke(Qe(t).data);O.fetchers.set(t,e)}}function rt(){let e=[],t=!1;for(let r of le){let a=O.fetchers.get(r);n(a,"Expected fetcher: "+r),"loading"===a.state&&(le.delete(r),e.push(r),t=!0)}return tt(e),t}function at(e){let t=[];for(let[r,a]of se)if(a<e){let e=O.fetchers.get(r);n(e,"Expected fetcher: "+r),"loading"===e.state&&(et(r),se.delete(r),t.push(r))}return tt(t),t.length>0}function nt(e){O.blockers.delete(e),Le.delete(e)}function ot(e,t){let r=O.blockers.get(e)||X;n("unblocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"blocked"===t.state||"blocked"===r.state&&"proceeding"===t.state||"blocked"===r.state&&"unblocked"===t.state||"proceeding"===r.state&&"unblocked"===t.state,"Invalid blocker state transition: "+r.state+" -> "+t.state);let a=new Map(O.blockers);a.set(e,t),Ce({blockers:a})}function it(e){let{currentLocation:t,nextLocation:r,historyAction:a}=e;if(0===Le.size)return;Le.size>1&&o(!1,"A router only supports one blocker at a time");let n=Array.from(Le.entries()),[i,s]=n[n.length-1],l=O.blockers.get(i);return l&&"proceeding"===l.state?void 0:s({currentLocation:t,nextLocation:r,historyAction:a})?i:void 0}function st(e){let t=Pe(404,{pathname:e}),r=c||b,{matches:a,route:n}=Ee(r);return lt(),{notFoundMatches:a,route:n,error:t}}function lt(e){let t=[];return be.forEach(((r,a)=>{e&&!e(a)||(r.cancel(),t.push(a),be.delete(a))})),t}function ut(e,t){if(A){return A(e,t.map((e=>y(e,O.loaderData))))||e.key}return e.key}function ct(e,t){if(L){let r=ut(e,t),a=L[r];if("number"==typeof a)return a}return null}function dt(e,t,r){if(D){if(!e){return{active:!0,matches:m(t,r,w,!0)||[]}}if(Object.keys(e[0].params).length>0){return{active:!0,matches:m(t,r,w,!0)}}}return{active:!1,matches:null}}async function ht(e,t,r,a){if(!D)return{type:"success",matches:e};let n=e;for(;;){let e=null==c,o=c||b,i=g;try{await D({signal:r,path:t,matches:n,fetcherKey:a,patch:(e,t)=>{r.aborted||ue(e,t,o,i,u)}})}catch(e){return{type:"error",error:e,partialMatches:n}}finally{e&&!r.aborted&&(b=[...b])}if(r.aborted)return{type:"aborted"};let s=p(o,t,w);if(s)return{type:"success",matches:s};let l=m(o,t,w,!0);if(!l||n.length===l.length&&n.every(((e,t)=>e.route.id===l[t].route.id)))return{type:"success",matches:null};n=l}}return v={get basename(){return w},get future(){return R},get state(){return O},get routes(){return b},get window(){return a},initialize:function(){if(E=e.history.listen((t=>{let{action:r,location:a,delta:n}=t;if(U)return U(),void(U=void 0);o(0===Le.size||null!=n,"You are trying to use a blocker on a POP navigation to a location that was not created by @remix-run/router. This will fail silently in production. This can happen if you are navigating outside the router via `window.history.pushState`/`window.location.hash` instead of using router navigation APIs.  This can also happen if you are using createHashRouter and the user manually changes the URL.");let i=it({currentLocation:O.location,nextLocation:a,historyAction:r});if(i&&null!=n){let t=new Promise((e=>{U=e}));return e.history.go(-1*n),void ot(i,{state:"blocked",location:a,proceed(){ot(i,{state:"proceeding",proceed:void 0,reset:void 0,location:a}),t.then((()=>e.history.go(n)))},reset(){let e=new Map(O.blockers);e.set(i,X),Ce({blockers:e})}})}return Te(r,a)})),i){!function(e,t){try{let r=e.sessionStorage.getItem(Z);if(r){let e=JSON.parse(r);for(let[r,a]of Object.entries(e||{}))a&&Array.isArray(a)&&t.set(r,new Set(a||[]))}}catch(e){}}(a,F);let e=()=>function(e,t){if(t.size>0){let r={};for(let[e,a]of t)r[e]=[...a];try{e.sessionStorage.setItem(Z,JSON.stringify(r))}catch(e){o(!1,"Failed to save applied view transitions in sessionStorage ("+e+").")}}}(a,F);a.addEventListener("pagehide",e),B=()=>a.removeEventListener("pagehide",e)}return O.initialized||Te(r.Pop,O.location,{initialHydration:!0}),v},subscribe:function(e){return x.add(e),()=>x.delete(e)},enableScrollRestoration:function(e,t,r){if(L=e,M=t,A=r||null,!j&&O.navigation===J){j=!0;let e=ct(O.location,O.matches);null!=e&&Ce({restoreScrollPosition:e})}return()=>{L=null,M=null,A=null}},navigate:async function a(n,o){if("number"==typeof n)return void e.history.go(n);let i=re(O.location,O.matches,w,R.v7_prependBasename,n,R.v7_relativeSplatPath,null==o?void 0:o.fromRouteId,null==o?void 0:o.relative),{path:l,submission:u,error:c}=ae(R.v7_normalizeFormMethod,!1,i,o),d=O.location,h=s(O.location,l,o&&o.state);h=t({},h,e.history.encodeLocation(h));let f=o&&null!=o.replace?o.replace:void 0,p=r.Push;!0===f?p=r.Replace:!1===f||null!=u&&Oe(u.formMethod)&&u.formAction===O.location.pathname+O.location.search&&(p=r.Replace);let m=o&&"preventScrollReset"in o?!0===o.preventScrollReset:void 0,y=!0===(o&&o.flushSync),v=it({currentLocation:d,nextLocation:h,historyAction:p});if(!v)return await Te(p,h,{submission:u,pendingError:c,preventScrollReset:m,replace:o&&o.replace,enableViewTransition:o&&o.viewTransition,flushSync:y});ot(v,{state:"blocked",location:h,proceed(){ot(v,{state:"proceeding",proceed:void 0,reset:void 0,location:h}),a(n,o)},reset(){let e=new Map(O.blockers);e.set(v,X),Ce({blockers:e})}})},fetch:function(t,r,a,o){if(l)throw new Error("router.fetch() was called during the server render, but it shouldn't be. You are likely calling a useFetcher() method in the body of your component. Try moving it to a useEffect or a callback.");et(t);let i=!0===(o&&o.flushSync),s=c||b,u=re(O.location,O.matches,w,R.v7_prependBasename,a,R.v7_relativeSplatPath,r,null==o?void 0:o.relative),d=p(s,u,w),h=dt(d,s,u);if(h.active&&h.matches&&(d=h.matches),!d)return void Ge(t,r,Pe(404,{pathname:u}),{flushSync:i});let{path:f,submission:m,error:y}=ae(R.v7_normalizeFormMethod,!0,u,o);if(y)return void Ge(t,r,y,{flushSync:i});let v=Ne(d,f),g=!0===(o&&o.preventScrollReset);m&&Oe(m.formMethod)?async function(t,r,a,o,i,s,l,u,d){function h(e){if(!e.route.action&&!e.route.lazy){let e=Pe(405,{method:d.formMethod,pathname:a,routeId:r});return Ge(t,r,e,{flushSync:l}),!0}return!1}if(Ve(),ce.delete(t),!s&&h(o))return;let f=O.fetchers.get(t);Xe(t,function(e,t){return{state:"submitting",formMethod:e.formMethod,formAction:e.formAction,formEncType:e.formEncType,formData:e.formData,json:e.json,text:e.text,data:t?t.data:void 0}}(d,f),{flushSync:l});let m=new AbortController,y=ye(e.history,a,m.signal,d);if(s){let e=await ht(i,new URL(y.url).pathname,y.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void Ge(t,r,e.error,{flushSync:l});if(!e.matches)return void Ge(t,r,Pe(404,{pathname:a}),{flushSync:l});if(h(o=Ne(i=e.matches,a)))return}ee.set(t,m);let v=te,g=(await Ye("action",O,y,[o],i,t))[o.route.id];if(y.signal.aborted)return void(ee.get(t)===m&&ee.delete(t));if(R.v7_fetcherPersist&&ge.has(t)){if(ke(g)||je(g))return void Xe(t,Ke(void 0))}else{if(ke(g))return ee.delete(t),ne>v?void Xe(t,Ke(void 0)):(le.add(t),Xe(t,qe(d)),Fe(y,g,!1,{fetcherSubmission:d,preventScrollReset:u}));if(je(g))return void Ge(t,r,g.error)}if(Me(g))throw Pe(400,{type:"defer-action"});let S=O.navigation.location||O.location,D=ye(e.history,S,m.signal),E=c||b,P="idle"!==O.navigation.state?p(E,O.navigation.location,w):O.matches;n(P,"Didn't find any matches after fetcher action");let x=++te;se.set(t,x);let L=qe(d,g.data);O.fetchers.set(t,L);let[A,M]=oe(e.history,O,P,d,S,!1,R.v7_skipActionErrorRevalidation,$,q,K,ge,ce,le,E,w,[o.route.id,g]);M.filter((e=>e.key!==t)).forEach((e=>{let t=e.key,r=O.fetchers.get(t),a=qe(void 0,r?r.data:void 0);O.fetchers.set(t,a),et(t),e.controller&&ee.set(t,e.controller)})),Ce({fetchers:new Map(O.fetchers)});let j=()=>M.forEach((e=>et(e.key)));m.signal.addEventListener("abort",j);let{loaderResults:k,fetcherResults:C}=await Je(O,P,A,M,D);if(m.signal.aborted)return;m.signal.removeEventListener("abort",j),se.delete(t),ee.delete(t),M.forEach((e=>ee.delete(e.key)));let _=xe(k);if(_)return Fe(D,_.result,!1,{preventScrollReset:u});if(_=xe(C),_)return le.add(_.key),Fe(D,_.result,!1,{preventScrollReset:u});let{loaderData:U,errors:I}=we(O,P,k,void 0,M,C,be);if(O.fetchers.has(t)){let e=Ke(g.data);O.fetchers.set(t,e)}at(x),"loading"===O.navigation.state&&x>ne?(n(H,"Expected pending action"),T&&T.abort(),_e(O.navigation.location,{matches:P,loaderData:U,errors:I,fetchers:new Map(O.fetchers)})):(Ce({errors:I,loaderData:Se(O.loaderData,U,P,I),fetchers:new Map(O.fetchers)}),$=!1)}(t,r,f,v,d,h.active,i,g,m):(ce.set(t,{routeId:r,path:f}),async function(t,r,a,o,i,s,l,u,c){let d=O.fetchers.get(t);Xe(t,qe(c,d?d.data:void 0),{flushSync:l});let h=new AbortController,f=ye(e.history,a,h.signal);if(s){let e=await ht(i,new URL(f.url).pathname,f.signal,t);if("aborted"===e.type)return;if("error"===e.type)return void Ge(t,r,e.error,{flushSync:l});if(!e.matches)return void Ge(t,r,Pe(404,{pathname:a}),{flushSync:l});o=Ne(i=e.matches,a)}ee.set(t,h);let p=te,m=(await Ye("loader",O,f,[o],i,t))[o.route.id];Me(m)&&(m=await ze(m,f.signal,!0)||m);ee.get(t)===h&&ee.delete(t);if(f.signal.aborted)return;if(ge.has(t))return void Xe(t,Ke(void 0));if(ke(m))return ne>p?void Xe(t,Ke(void 0)):(le.add(t),void await Fe(f,m,!1,{preventScrollReset:u}));if(je(m))return void Ge(t,r,m.error);n(!Me(m),"Unhandled fetcher deferred data"),Xe(t,Ke(m.data))}(t,r,f,v,d,h.active,i,g,m))},revalidate:function(){Ve(),Ce({revalidation:"loading"}),"submitting"!==O.navigation.state&&("idle"!==O.navigation.state?Te(H||O.historyAction,O.navigation.location,{overrideNavigation:O.navigation,enableViewTransition:!0===z}):Te(O.historyAction,O.location,{startUninterruptedRevalidation:!0}))},createHref:t=>e.history.createHref(t),encodeLocation:t=>e.history.encodeLocation(t),getFetcher:Qe,deleteFetcher:function(e){let t=(ve.get(e)||0)-1;t<=0?(ve.delete(e),ge.add(e),R.v7_fetcherPersist||Ze(e)):ve.set(e,t),Ce({fetchers:new Map(O.fetchers)})},dispose:function(){E&&E(),B&&B(),x.clear(),T&&T.abort(),O.fetchers.forEach(((e,t)=>Ze(t))),O.blockers.forEach(((e,t)=>nt(t)))},getBlocker:function(e,t){let r=O.blockers.get(e)||X;return Le.get(e)!==t&&Le.set(e,t),r},deleteBlocker:nt,patchRoutes:function(e,t){let r=null==c;ue(e,t,c||b,g,u),r&&(b=[...b],Ce({}))},_internalFetchControllers:ee,_internalActiveDeferreds:be,_internalSetRoutes:function(e){g={},c=f(e,u,void 0,g)}},v},e.createStaticHandler=function(e,r){n(e.length>0,"You must provide a non-empty routes array to createStaticHandler");let a,o={},i=(r?r.basename:null)||"/";if(null!=r&&r.mapRouteProperties)a=r.mapRouteProperties;else if(null!=r&&r.detectErrorBoundary){let e=r.detectErrorBoundary;a=t=>({hasErrorBoundary:e(t)})}else a=Q;let u=t({v7_relativeSplatPath:!1,v7_throwAbortReason:!1},r?r.future:null),c=f(e,a,void 0,o);async function h(e,r,a,o,i,s,l){n(e.signal,"query()/queryRoute() requests must contain an AbortController signal");try{if(Oe(e.method.toLowerCase())){let n=await async function(e,r,a,n,o,i,s){let l;if(a.route.action||a.route.lazy){l=(await y("action",e,[a],r,s,n,o))[a.route.id],e.signal.aborted&&te(e,s,u)}else{let t=Pe(405,{method:e.method,pathname:new URL(e.url).pathname,routeId:a.route.id});if(s)throw t;l={type:d.error,error:t}}if(ke(l))throw new Response(null,{status:l.response.status,headers:{Location:l.response.headers.get("Location")}});if(Me(l)){let e=Pe(400,{type:"defer-action"});if(s)throw e;l={type:d.error,error:e}}if(s){if(je(l))throw l.error;return{matches:[a],loaderData:{},actionData:{[a.route.id]:l.data},errors:null,statusCode:200,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}let c=new Request(e.url,{headers:e.headers,redirect:e.redirect,signal:e.signal});if(je(l)){let e=i?a:Re(r,a.route.id);return t({},await m(c,r,n,o,i,null,[e.route.id,l]),{statusCode:N(l.error)?l.error.status:null!=l.statusCode?l.statusCode:500,actionData:null,actionHeaders:t({},l.headers?{[a.route.id]:l.headers}:{})})}return t({},await m(c,r,n,o,i,null),{actionData:{[a.route.id]:l.data}},l.statusCode?{statusCode:l.statusCode}:{},{actionHeaders:l.headers?{[a.route.id]:l.headers}:{}})}(e,a,l||Ne(a,r),o,i,s,null!=l);return n}let n=await m(e,a,o,i,s,l);return Te(n)?n:t({},n,{actionData:null,actionHeaders:{}})}catch(e){if(function(e){return null!=e&&"object"==typeof e&&"type"in e&&"result"in e&&(e.type===d.data||e.type===d.error)}(e)&&Te(e.result)){if(e.type===d.error)throw e.result;return e.result}if(function(e){if(!Te(e))return!1;let t=e.status,r=e.headers.get("Location");return t>=300&&t<=399&&null!=r}(e))return e;throw e}}async function m(e,r,a,n,o,i,s){let l=null!=i;if(l&&(null==i||!i.route.loader)&&(null==i||!i.route.lazy))throw Pe(400,{method:e.method,pathname:new URL(e.url).pathname,routeId:null==i?void 0:i.route.id});let c=(i?[i]:s&&je(s[1])?ne(r,s[0]):r).filter((e=>e.route.loader||e.route.lazy));if(0===c.length)return{matches:r,loaderData:r.reduce(((e,t)=>Object.assign(e,{[t.route.id]:null})),{}),errors:s&&je(s[1])?{[s[0]]:s[1].error}:null,statusCode:200,loaderHeaders:{},activeDeferreds:null};let d=await y("loader",e,c,r,l,a,n);e.signal.aborted&&te(e,l,u);let h=new Map,f=be(r,d,s,h,o),p=new Set(c.map((e=>e.route.id)));return r.forEach((e=>{p.has(e.route.id)||(f.loaderData[e.route.id]=null)})),t({},f,{matches:r,activeDeferreds:h.size>0?Object.fromEntries(h.entries()):null})}async function y(e,t,r,n,s,l,c){let d=await he(c||de,e,null,t,r,n,null,o,a,l),h={};return await Promise.all(n.map((async e=>{if(!(e.route.id in d))return;let r=d[e.route.id];if(Ae(r)){throw pe(r.result,t,e.route.id,n,i,u.v7_relativeSplatPath)}if(Te(r.result)&&s)throw r;h[e.route.id]=await fe(r)}))),h}return{dataRoutes:c,query:async function(e,r){let{requestContext:a,skipLoaderErrorBubbling:n,dataStrategy:o}=void 0===r?{}:r,u=new URL(e.url),d=e.method,f=s("",l(u),null,"default"),m=p(c,f,i);if(!Ue(d)&&"HEAD"!==d){let e=Pe(405,{method:d}),{matches:t,route:r}=Ee(c);return{basename:i,location:f,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}if(!m){let e=Pe(404,{pathname:f.pathname}),{matches:t,route:r}=Ee(c);return{basename:i,location:f,matches:t,loaderData:{},actionData:null,errors:{[r.id]:e},statusCode:e.status,loaderHeaders:{},actionHeaders:{},activeDeferreds:null}}let y=await h(e,f,m,a,o||null,!0===n,null);return Te(y)?y:t({location:f,basename:i},y)},queryRoute:async function(e,t){let{routeId:r,requestContext:a,dataStrategy:n}=void 0===t?{}:t,o=new URL(e.url),u=e.method,d=s("",l(o),null,"default"),f=p(c,d,i);if(!Ue(u)&&"HEAD"!==u&&"OPTIONS"!==u)throw Pe(405,{method:u});if(!f)throw Pe(404,{pathname:d.pathname});let m=r?f.find((e=>e.route.id===r)):Ne(f,d);if(r&&!m)throw Pe(403,{pathname:d.pathname,routeId:r});if(!m)throw Pe(404,{pathname:d.pathname});let y=await h(e,d,f,a,n||null,!1,m);if(Te(y))return y;let v=y.errors?Object.values(y.errors)[0]:void 0;if(void 0!==v)throw v;if(y.actionData)return Object.values(y.actionData)[0];if(y.loaderData){var g;let e=Object.values(y.loaderData)[0];return null!=(g=y.activeDeferreds)&&g[m.route.id]&&(e[ee]=y.activeDeferreds[m.route.id]),e}}}},e.data=function(e,t){return new U(e,"number"==typeof t?{status:t}:t)},e.defer=function(e,t){return void 0===t&&(t={}),new H(e,"number"==typeof t?{status:t}:t)},e.generatePath=function(e,t){void 0===t&&(t={});let r=e;r.endsWith("*")&&"*"!==r&&!r.endsWith("/*")&&(o(!1,'Route path "'+r+'" will be treated as if it were "'+r.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+r.replace(/\*$/,"/*")+'".'),r=r.replace(/\*$/,"/*"));const a=r.startsWith("/")?"/":"",i=e=>null==e?"":"string"==typeof e?e:String(e);return a+r.split(/\/+/).map(((e,r,a)=>{if(r===a.length-1&&"*"===e){return i(t["*"])}const o=e.match(/^:([\w-]+)(\??)$/);if(o){const[,e,r]=o;let a=t[e];return n("?"===r||null!=a,'Missing ":'+e+'" param'),i(a)}return e.replace(/\?$/g,"")})).filter((e=>!!e)).join("/")},e.getStaticContextFromError=function(e,r,a){return t({},r,{statusCode:N(a)?a.status:500,errors:{[r._deepestRenderedBoundaryId||e[0].id]:a}})},e.getToPathname=function(e){return""===e||""===e.pathname?"/":"string"==typeof e?u(e).pathname:e.pathname},e.isDataWithResponseInit=Ce,e.isDeferredData=_e,e.isRouteErrorResponse=N,e.joinPaths=k,e.json=function(e,r){void 0===r&&(r={});let a="number"==typeof r?{status:r}:r,n=new Headers(a.headers);return n.has("Content-Type")||n.set("Content-Type","application/json; charset=utf-8"),new Response(JSON.stringify(e),t({},a,{headers:n}))},e.matchPath=R,e.matchRoutes=p,e.normalizePathname=C,e.parsePath=u,e.redirect=z,e.redirectDocument=(e,t)=>{let r=z(e,t);return r.headers.set("X-Remix-Reload-Document","true"),r},e.replace=(e,t)=>{let r=z(e,t);return r.headers.set("X-Remix-Replace","true"),r},e.resolvePath=x,e.resolveTo=j,e.stripBasename=P,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=router.umd.min.js.map
