{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BellDot = createLucideIcon(\"BellDot\", [[\"path\", {\n  d: \"M19.4 14.9C20.2 16.4 21 17 21 17H3s3-2 3-9c0-3.3 2.7-6 6-6 .7 0 1.3.1 1.9.3\",\n  key: \"xcehk\"\n}], [\"path\", {\n  d: \"M10.3 21a1.94 1.94 0 0 0 3.4 0\",\n  key: \"qgo35s\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"8\",\n  r: \"3\",\n  key: \"1g0gzu\"\n}]]);\nexport { BellDot as default };", "map": {"version": 3, "names": ["BellDot", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\bell-dot.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BellDot\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTkuNCAxNC45QzIwLjIgMTYuNCAyMSAxNyAyMSAxN0gzczMtMiAzLTljMC0zLjMgMi43LTYgNi02IC43IDAgMS4zLjEgMS45LjMiIC8+CiAgPHBhdGggZD0iTTEwLjMgMjFhMS45NCAxLjk0IDAgMCAwIDMuNCAwIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iOCIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/bell-dot\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BellDot = createLucideIcon('BellDot', [\n  [\n    'path',\n    {\n      d: 'M19.4 14.9C20.2 16.4 21 17 21 17H3s3-2 3-9c0-3.3 2.7-6 6-6 .7 0 1.3.1 1.9.3',\n      key: 'xcehk',\n    },\n  ],\n  ['path', { d: 'M10.3 21a1.94 1.94 0 0 0 3.4 0', key: 'qgo35s' }],\n  ['circle', { cx: '18', cy: '8', r: '3', key: '1g0gzu' }],\n]);\n\nexport default BellDot;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gCAAkC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,EACxD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}