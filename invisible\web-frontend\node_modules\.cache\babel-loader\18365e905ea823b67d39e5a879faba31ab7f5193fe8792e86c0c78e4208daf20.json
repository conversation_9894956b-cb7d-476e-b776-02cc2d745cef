{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowDownToLine = createLucideIcon(\"ArrowDownToLine\", [[\"path\", {\n  d: \"M12 17V3\",\n  key: \"1cwfxf\"\n}], [\"path\", {\n  d: \"m6 11 6 6 6-6\",\n  key: \"12ii2o\"\n}], [\"path\", {\n  d: \"M19 21H5\",\n  key: \"150jfl\"\n}]]);\nexport { ArrowDownToLine as default };", "map": {"version": 3, "names": ["ArrowDownToLine", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\arrow-down-to-line.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowDownToLine\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTdWMyIgLz4KICA8cGF0aCBkPSJtNiAxMSA2IDYgNi02IiAvPgogIDxwYXRoIGQ9Ik0xOSAyMUg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-down-to-line\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowDownToLine = createLucideIcon('ArrowDownToLine', [\n  ['path', { d: 'M12 17V3', key: '1cwfxf' }],\n  ['path', { d: 'm6 11 6 6 6-6', key: '12ii2o' }],\n  ['path', { d: 'M19 21H5', key: '150jfl' }],\n]);\n\nexport default ArrowDownToLine;\n"], "mappings": ";;;;;AAaM,MAAAA,eAAA,GAAkBC,gBAAA,CAAiB,iBAAmB,GAC1D,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}