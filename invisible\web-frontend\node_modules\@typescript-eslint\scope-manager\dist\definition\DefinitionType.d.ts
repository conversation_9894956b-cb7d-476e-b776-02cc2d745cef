declare enum DefinitionType {
    CatchClause = "CatchClause",
    ClassName = "ClassName",
    FunctionName = "FunctionName",
    ImplicitGlobalVariable = "ImplicitGlobalVariable",
    ImportBinding = "ImportBinding",
    Parameter = "Parameter",
    TSEnumName = "TSEnumName",
    TSEnumMember = "TSEnumMemberName",
    TSModuleName = "TSModuleName",
    Type = "Type",
    Variable = "Variable"
}
export { DefinitionType };
//# sourceMappingURL=DefinitionType.d.ts.map