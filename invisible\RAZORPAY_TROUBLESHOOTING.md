# 🔧 RAZORPAY & DOWNLOAD TROUBLESHOOTING GUIDE

## ✅ FIXES IMPLEMENTED

### 1. Environment Variable Issues FIXED ✅
- **Problem**: Backend was looking for `RAZORPAY_KEY_ID` but `.env` had `RAZORPAY_API_KEYID`
- **Solution**: Updated backend to check both variable names
- **Files Modified**: `license-server/routes/payments.js`

### 2. MongoDB Connection FIXED ✅
- **Problem**: Back<PERSON> was looking for `MONGODB_URI` but `.env` had `MONGO_URI`
- **Solution**: Updated backend to check both variable names
- **Files Modified**: `license-server/server.js`

### 3. Download Files VERIFIED ✅
- **Status**: `InvisibleAssessmentTool.exe` exists in `license-server/downloads/`
- **No action needed**: Download functionality should work

### 4. Frontend Configuration VERIFIED ✅
- **API Base URL**: Correctly set to `http://localhost:5002/api`
- **Ra<PERSON>pay Script**: Loaded in `public/index.html`
- **No action needed**: Frontend should connect properly

## 🚀 QUICK START GUIDE

### Option 1: Automated Startup
```bash
# Run the automated startup script
start-full-system.bat
```

### Option 2: Manual Startup
```bash
# Terminal 1: Start Backend
cd invisible/license-server
npm install
npm start

# Terminal 2: Start Frontend
cd invisible/web-frontend
npm install
npm start

# Terminal 3: Test Backend
cd invisible
node test-backend.js
```

## 🧪 TESTING THE SYSTEM

### 1. Backend Test
```bash
cd invisible
node test-backend.js
```

### 2. Frontend Test
1. Open http://localhost:3000
2. Register a new account
3. Go to Purchase page
4. Click "Create Test License (Skip Payment)" button
5. Verify license creation and download link

### 3. Real Razorpay Test
1. Use the "Pay with Razorpay" button
2. In test mode, it will simulate payment
3. In production, it will open real Razorpay checkout

## 🔍 COMMON ISSUES & SOLUTIONS

### Issue 1: "Payment creation failed"
**Symptoms**: Error when clicking payment buttons
**Solutions**:
1. Check if backend is running on port 5002
2. Verify MongoDB connection
3. Check browser console for errors
4. Verify user is logged in

### Issue 2: "Download failed"
**Symptoms**: Download button doesn't work
**Solutions**:
1. Verify `InvisibleAssessmentTool.exe` exists in `license-server/downloads/`
2. Check if user has valid license
3. Verify backend download route is working

### Issue 3: "Razorpay is not defined"
**Symptoms**: Frontend error when opening payment
**Solutions**:
1. Check if Razorpay script is loaded in `public/index.html`
2. Verify internet connection (Razorpay CDN)
3. Check browser console for script loading errors

### Issue 4: MongoDB Connection Error
**Symptoms**: Backend fails to start with DB error
**Solutions**:
1. Start MongoDB service
2. Check MongoDB URI in `invisible.env`
3. Verify MongoDB is running on correct port

## 📋 ENVIRONMENT VARIABLES CHECKLIST

Verify these variables in `invisible.env`:
```
✅ MONGO_URI=mongodb+srv://...
✅ RAZORPAY_API_KEYID=rzp_test_...
✅ RAZORPAY_API_KEYSECRET=...
✅ OPENAI_API_KEY=sk-proj-...
✅ GEMINI_API_KEY=...
✅ QWEN_API_KEY=...
✅ ANTHROPIC_API_KEY=...
```

## 🎯 PRODUCTION DEPLOYMENT

### For Real Razorpay (Production):
1. Get real Razorpay credentials from https://dashboard.razorpay.com/
2. Replace test keys in `invisible.env`:
   ```
   RAZORPAY_API_KEYID=rzp_live_...
   RAZORPAY_API_KEYSECRET=your_live_secret
   ```
3. Update pricing in `license-server/routes/payments.js` (currently ₹10/₹5 for testing)

### For Production MongoDB:
1. Use MongoDB Atlas or dedicated server
2. Update `MONGO_URI` in `invisible.env`

## 🔧 DEBUGGING COMMANDS

### Check Backend Status:
```bash
curl http://localhost:5002/health
curl http://localhost:5002/api/payments/test
```

### Check Frontend Build:
```bash
cd invisible/web-frontend
npm run build
```

### View Backend Logs:
- Check terminal where `npm start` is running
- Look for Razorpay initialization messages
- Check for MongoDB connection status

## 📞 SUPPORT

If issues persist:
1. Check all files are saved correctly
2. Restart both backend and frontend
3. Clear browser cache and cookies
4. Check firewall/antivirus blocking ports 3000/5002

## 🎉 SUCCESS INDICATORS

You'll know everything is working when:
- ✅ Backend starts without errors
- ✅ Frontend loads at http://localhost:3000
- ✅ User registration works
- ✅ Payment buttons appear and work
- ✅ Test license creation succeeds
- ✅ Download link generates successfully
- ✅ File download starts when clicked
