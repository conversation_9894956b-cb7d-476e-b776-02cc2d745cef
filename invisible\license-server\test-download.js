const fs = require('fs');
const path = require('path');

console.log('🔍 TESTING DOWNLOAD SYSTEM');
console.log('==========================');

// Test 1: Check if downloads directory exists
const downloadsDir = path.join(__dirname, 'downloads');
console.log('\n📁 Downloads directory:', downloadsDir);
console.log('Exists:', fs.existsSync(downloadsDir));

if (fs.existsSync(downloadsDir)) {
    const files = fs.readdirSync(downloadsDir);
    console.log('Files in downloads:', files);
    
    // Test 2: Check specific file
    const exeFile = path.join(downloadsDir, 'InvisibleAssessmentTool.exe');
    console.log('\n📄 Executable file:', exeFile);
    console.log('Exists:', fs.existsSync(exeFile));
    
    if (fs.existsSync(exeFile)) {
        const stats = fs.statSync(exeFile);
        console.log('Size:', (stats.size / (1024 * 1024)).toFixed(1), 'MB');
        console.log('Modified:', stats.mtime.toLocaleString());
    }
}

// Test 3: Check route file
const routeFile = path.join(__dirname, 'routes', 'download.js');
console.log('\n🛣️ Route file:', routeFile);
console.log('Exists:', fs.existsSync(routeFile));

console.log('\n✅ Download system test complete!');
console.log('\nIf all files exist, the issue might be:');
console.log('1. License validation failing');
console.log('2. API endpoint not being called correctly');
console.log('3. Authentication issues');
console.log('\nCheck the browser network tab for the actual error!');
