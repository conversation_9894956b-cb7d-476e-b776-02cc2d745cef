@echo off
echo 🚀 BUILDING PROFESSIONAL INSTALLER
echo ================================

REM Check if INNO Setup is installed
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    echo ✅ INNO Setup found!
    echo 🔨 Compiling installer...
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" installer-script.iss
    
    if exist "installer-output\InvisibleAssessmentTool-Setup-v1.0.0.exe" (
        echo.
        echo 🎉 SUCCESS! Installer created:
        echo 📁 installer-output\InvisibleAssessmentTool-Setup-v1.0.0.exe
        echo.
        echo 💡 This is your professional installer!
        echo 📤 Upload this to your download server.
        echo.
        pause
    ) else (
        echo ❌ Build failed! Check the script for errors.
        pause
    )
) else (
    echo ❌ INNO Setup not found!
    echo.
    echo 📥 Please download and install INNO Setup first:
    echo 🌐 https://jrsoftware.org/isinfo.php
    echo.
    echo After installation, run this script again.
    pause
)
