import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { Download as DownloadIcon, Shield, Key, Monitor, CheckCircle, AlertCircle } from 'lucide-react';

const DownloadContainer = styled.div`
  min-height: 100vh;
  padding: 120px 20px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
`;

const DownloadContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const SuccessHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;
  padding: 40px;
  background: rgba(76, 175, 80, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(76, 175, 80, 0.3);
`;

const SuccessIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 20px;
  color: #4CAF50;
`;

const SuccessTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #4CAF50;
`;

const SuccessMessage = styled.p`
  font-size: 1.2rem;
  opacity: 0.9;
`;

const LicenseInfo = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const LicenseTitle = styled.h2`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #4CAF50;
  
  svg {
    margin-right: 10px;
  }
`;

const LicenseDetails = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
`;

const LicenseDetail = styled.div`
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  
  .label {
    font-size: 0.9rem;
    opacity: 0.7;
    margin-bottom: 5px;
  }
  
  .value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #4CAF50;
  }
`;

const LicenseKey = styled.div`
  background: rgba(0, 0, 0, 0.3);
  padding: 15px;
  border-radius: 10px;
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  text-align: center;
  margin-top: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  
  .copy-btn {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: #4CAF50;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.8rem;
  }
`;

const DownloadSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const DownloadButton = styled.button`
  width: 100%;
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 20px;
  border-radius: 15px;
  font-size: 1.2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  svg {
    margin-right: 10px;
  }
`;

const InstructionsSection = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
`;

const InstructionsList = styled.ol`
  margin-left: 20px;
  
  li {
    margin-bottom: 15px;
    line-height: 1.6;
    
    strong {
      color: #4CAF50;
    }
  }
`;

const Download = () => {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const [downloading, setDownloading] = useState(false);
  const [licenseData, setLicenseData] = useState(null);
  const [downloadStatus, setDownloadStatus] = useState(null);

  useEffect(() => {
    // Get license data from navigation state or fetch from API
    if (location.state?.licenseKey) {
      setLicenseData({
        licenseKey: location.state.licenseKey,
        plan: location.state.plan
      });
    } else {
      fetchLatestLicense();
    }

    // Fetch download status
    fetchDownloadStatus();
  }, [location.state]);

  const fetchDownloadStatus = async () => {
    try {
      const response = await axios.get('/download/status');
      if (response.data.success) {
        setDownloadStatus(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching download status:', error);
    }
  };

  const fetchLatestLicense = async () => {
    try {
      const response = await axios.get('/licenses/my-licenses');
      if (response.data.success && response.data.data.licenses.length > 0) {
        const latestLicense = response.data.data.licenses[0];
        setLicenseData({
          licenseKey: latestLicense.licenseKey,
          plan: {
            title: latestLicense.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',
            screenshots: latestLicense.screenshotsLimit
          }
        });
      } else {
        toast.error('No valid license found. Please purchase a license first.');
        navigate('/purchase');
      }
    } catch (error) {
      toast.error('Failed to fetch license information');
      navigate('/purchase');
    }
  };

  const copyLicenseKey = () => {
    if (licenseData?.licenseKey) {
      navigator.clipboard.writeText(licenseData.licenseKey);
      toast.success('License key copied to clipboard!');
    }
  };

  const downloadSoftware = async () => {
    try {
      setDownloading(true);

      // Create secure download link
      const response = await axios.post('/download/create-link', {
        licenseKey: licenseData.licenseKey
      });

      if (response.data.success) {
        const { downloadUrl, fileName } = response.data.data;

        // Create download link
        const link = document.createElement('a');
        link.href = `http://localhost:5002${downloadUrl}`;
        link.setAttribute('download', fileName);
        link.style.display = 'none';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        toast.success('Download started! Check your downloads folder.');
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Download error:', error);
      const errorMessage = error.response?.data?.message || 'Download failed. Please try again or contact support.';

      // Check if it's a download limit error
      if (errorMessage.includes('Download limit reached')) {
        toast.error('Download limit reached! You can only download once per license.');
        // Refresh download status
        fetchDownloadStatus();
      } else {
        toast.error(errorMessage);
      }
    } finally {
      setDownloading(false);
    }
  };

  if (!licenseData) {
    return (
      <DownloadContainer>
        <DownloadContent>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <div className="loading-spinner" style={{ margin: '0 auto 20px' }}></div>
            <p>Loading license information...</p>
          </div>
        </DownloadContent>
      </DownloadContainer>
    );
  }

  return (
    <DownloadContainer>
      <DownloadContent>
        <SuccessHeader>
          <SuccessIcon>
            <CheckCircle size={64} />
          </SuccessIcon>
          <SuccessTitle>Payment Successful!</SuccessTitle>
          <SuccessMessage>
            Your license has been activated and you can now download the software.
          </SuccessMessage>
        </SuccessHeader>

        <LicenseInfo>
          <LicenseTitle>
            <Key size={24} />
            License Information
          </LicenseTitle>

          <LicenseDetails>
            <LicenseDetail>
              <div className="label">Plan</div>
              <div className="value">{licenseData.plan?.title}</div>
            </LicenseDetail>
            <LicenseDetail>
              <div className="label">Screenshots</div>
              <div className="value">{licenseData.plan?.screenshots}/month</div>
            </LicenseDetail>
            <LicenseDetail>
              <div className="label">Status</div>
              <div className="value">Active</div>
            </LicenseDetail>
            <LicenseDetail>
              <div className="label">User</div>
              <div className="value">{user?.name}</div>
            </LicenseDetail>
          </LicenseDetails>

          <div>
            <strong>Your License Key:</strong>
            <LicenseKey>
              {licenseData.licenseKey}
              <button className="copy-btn" onClick={copyLicenseKey}>
                Copy
              </button>
            </LicenseKey>
          </div>
        </LicenseInfo>

        <DownloadSection>
          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>
            <DownloadIcon size={24} style={{ marginRight: '10px' }} />
            Download Software
          </h2>

          {/* 📊 DOWNLOAD STATUS DISPLAY */}
          {downloadStatus && downloadStatus.licenses && downloadStatus.licenses.length > 0 && (
            <div style={{
              background: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads
                ? 'rgba(220, 53, 69, 0.1)'
                : 'rgba(40, 167, 69, 0.1)',
              border: `1px solid ${downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads
                ? 'rgba(220, 53, 69, 0.3)'
                : 'rgba(40, 167, 69, 0.3)'}`,
              borderRadius: '10px',
              padding: '15px',
              marginBottom: '20px',
              textAlign: 'center'
            }}>
              <div style={{
                color: downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads
                  ? '#dc3545'
                  : '#28a745',
                fontWeight: '600',
                marginBottom: '5px'
              }}>
                Downloads: {downloadStatus.licenses[0].downloadCount || 0} / {downloadStatus.licenses[0].maxDownloads || 1}
              </div>
              <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>
                {downloadStatus.licenses[0].downloadCount >= downloadStatus.licenses[0].maxDownloads
                  ? '❌ Download limit reached for this license'
                  : '✅ Download available'
                }
              </div>
            </div>
          )}

          <DownloadButton
            onClick={downloadSoftware}
            disabled={downloading || (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads)}
            style={{
              opacity: (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads) ? 0.5 : 1,
              cursor: (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads) ? 'not-allowed' : 'pointer'
            }}
          >
            {downloading ? (
              <>
                <div className="loading-spinner" style={{ width: '20px', height: '20px', marginRight: '10px' }}></div>
                Preparing Download...
              </>
            ) : (downloadStatus?.licenses?.[0]?.downloadCount >= downloadStatus?.licenses?.[0]?.maxDownloads) ? (
              <>
                <Shield size={24} />
                Download Limit Reached
              </>
            ) : (
              <>
                <DownloadIcon size={24} />
                Download Invisible Assessment Tool
              </>
            )}
          </DownloadButton>

          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', opacity: 0.8 }}>
            <Shield size={16} style={{ marginRight: '5px' }} />
            <small>Secure download • Hardware-bound installation</small>
          </div>
        </DownloadSection>

        <InstructionsSection>
          <h2 style={{ marginBottom: '20px', display: 'flex', alignItems: 'center' }}>
            <Monitor size={24} style={{ marginRight: '10px' }} />
            Installation Instructions
          </h2>

          <InstructionsList>
            <li><strong>Download</strong> the software using the button above</li>
            <li><strong>Run</strong> the installer as administrator on your target computer</li>
            <li><strong>Enter</strong> your license key when prompted during installation</li>
            <li><strong>Complete</strong> the hardware binding process (one-time setup)</li>
            <li><strong>Launch</strong> the tool - it will start completely invisible</li>
            <li><strong>Use Ctrl+B</strong> to show/hide the interface</li>
            <li><strong>Use Ctrl+H</strong> to take screenshots</li>
            <li><strong>Use Ctrl+Enter</strong> to analyze screenshots with AI</li>
          </InstructionsList>

          <div style={{
            marginTop: '20px',
            padding: '15px',
            background: 'rgba(255, 193, 7, 0.1)',
            borderRadius: '10px',
            border: '1px solid rgba(255, 193, 7, 0.3)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
              <AlertCircle size={20} style={{ marginRight: '10px', color: '#FFC107' }} />
              <strong style={{ color: '#FFC107' }}>Important Notes:</strong>
            </div>
            <ul style={{ marginLeft: '30px', lineHeight: '1.6' }}>
              <li>The software is bound to your hardware and cannot be transferred</li>
              <li>Keep your license key safe - you'll need it for support</li>
              <li>The tool works completely in stealth mode during assessments</li>
              <li>Contact support if you encounter any installation issues</li>
            </ul>
          </div>
        </InstructionsSection>
      </DownloadContent>
    </DownloadContainer>
  );
};

export default Download;
