{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst DatabaseBackup = createLucideIcon(\"DatabaseBackup\", [[\"ellipse\", {\n  cx: \"12\",\n  cy: \"5\",\n  rx: \"9\",\n  ry: \"3\",\n  key: \"msslwz\"\n}], [\"path\", {\n  d: \"M3 5v14c0 1.4 3 2.7 7 3\",\n  key: \"jgylly\"\n}], [\"path\", {\n  d: \"M3 12c0 1.2 2 2.5 5 3\",\n  key: \"vxrdms\"\n}], [\"path\", {\n  d: \"M21 5v4\",\n  key: \"1vq2e7\"\n}], [\"path\", {\n  d: \"M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16\",\n  key: \"1f4ei9\"\n}], [\"path\", {\n  d: \"M12 12v4h4\",\n  key: \"1bxaet\"\n}]]);\nexport { DatabaseBackup as default };", "map": {"version": 3, "names": ["DatabaseBackup", "createLucideIcon", "cx", "cy", "rx", "ry", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\database-backup.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name DatabaseBackup\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8ZWxsaXBzZSBjeD0iMTIiIGN5PSI1IiByeD0iOSIgcnk9IjMiIC8+CiAgPHBhdGggZD0iTTMgNXYxNGMwIDEuNCAzIDIuNyA3IDMiIC8+CiAgPHBhdGggZD0iTTMgMTJjMCAxLjIgMiAyLjUgNSAzIiAvPgogIDxwYXRoIGQ9Ik0yMSA1djQiIC8+CiAgPHBhdGggZD0iTTEzIDIwYTUgNSAwIDAgMCA5LTMgNC41IDQuNSAwIDAgMC00LjUtNC41Yy0xLjMzIDAtMi41NC41NC0zLjQxIDEuNDFMMTIgMTYiIC8+CiAgPHBhdGggZD0iTTEyIDEydjRoNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/database-backup\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DatabaseBackup = createLucideIcon('DatabaseBackup', [\n  ['ellipse', { cx: '12', cy: '5', rx: '9', ry: '3', key: 'msslwz' }],\n  ['path', { d: 'M3 5v14c0 1.4 3 2.7 7 3', key: 'jgylly' }],\n  ['path', { d: 'M3 12c0 1.2 2 2.5 5 3', key: 'vxrdms' }],\n  ['path', { d: 'M21 5v4', key: '1vq2e7' }],\n  [\n    'path',\n    {\n      d: 'M13 20a5 5 0 0 0 9-3 4.5 4.5 0 0 0-4.5-4.5c-1.33 0-2.54.54-3.41 1.41L12 16',\n      key: '1f4ei9',\n    },\n  ],\n  ['path', { d: 'M12 12v4h4', key: '1bxaet' }],\n]);\n\nexport default DatabaseBackup;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CAAC,WAAW;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,MAAQ;EAAEC,CAAA,EAAG,yBAA2B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,uBAAyB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxC,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}