{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Anchor = createLucideIcon(\"Anchor\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"rqqgnr\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"22\",\n  y2: \"8\",\n  key: \"abakz7\"\n}], [\"path\", {\n  d: \"M5 12H2a10 10 0 0 0 20 0h-3\",\n  key: \"1hv3nh\"\n}]]);\nexport { Anchor as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\anchor.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Anchor\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjUiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjEyIiB4Mj0iMTIiIHkxPSIyMiIgeTI9IjgiIC8+CiAgPHBhdGggZD0iTTUgMTJIMmExMCAxMCAwIDAgMCAyMCAwaC0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/anchor\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Anchor = createLucideIcon('Anchor', [\n  ['circle', { cx: '12', cy: '5', r: '3', key: 'rqqgnr' }],\n  ['line', { x1: '12', x2: '12', y1: '22', y2: '8', key: 'abakz7' }],\n  ['path', { d: 'M5 12H2a10 10 0 0 0 20 0h-3', key: '1hv3nh' }],\n]);\n\nexport default Anchor;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAEK,CAAA,EAAG,6BAA+B;EAAAL,GAAA,EAAK;AAAA,CAAU,EAC7D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}