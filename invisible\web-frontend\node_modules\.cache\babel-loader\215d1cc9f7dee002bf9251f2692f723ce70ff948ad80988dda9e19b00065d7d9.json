{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Divide = createLucideIcon(\"Divide\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"6\",\n  r: \"1\",\n  key: \"1bh7o1\"\n}], [\"line\", {\n  x1: \"5\",\n  x2: \"19\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"13b5wn\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"18\",\n  r: \"1\",\n  key: \"lqb9t5\"\n}]]);\nexport { Divide as default };", "map": {"version": 3, "names": ["Divide", "createLucideIcon", "cx", "cy", "r", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\divide.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Divide\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjYiIHI9IjEiIC8+CiAgPGxpbmUgeDE9IjUiIHgyPSIxOSIgeTE9IjEyIiB5Mj0iMTIiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxOCIgcj0iMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/divide\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Divide = createLucideIcon('Divide', [\n  ['circle', { cx: '12', cy: '6', r: '1', key: '1bh7o1' }],\n  ['line', { x1: '5', x2: '19', y1: '12', y2: '12', key: '13b5wn' }],\n  ['circle', { cx: '12', cy: '18', r: '1', key: 'lqb9t5' }],\n]);\n\nexport default Divide;\n"], "mappings": ";;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}