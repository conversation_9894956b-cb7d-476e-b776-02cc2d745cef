{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Rat = createLucideIcon(\"Rat\", [[\"path\", {\n  d: \"M17 5c0-1.7-1.3-3-3-3s-3 1.3-3 3c0 .8.3 1.5.8 2H11c-3.9 0-7 3.1-7 7v0c0 2.2 1.8 4 4 4\",\n  key: \"16aj0u\"\n}], [\"path\", {\n  d: \"M16.8 3.9c.3-.3.6-.5 1-.7 1.5-.6 3.3.1 3.9 1.6.6 1.5-.1 3.3-1.6 3.9l1.6 2.8c.2.3.2.7.2 1-.2.8-.9 1.2-1.7 1.1 0 0-1.6-.3-2.7-.6H17c-1.7 0-3 1.3-3 3\",\n  key: \"1crdmb\"\n}], [\"path\", {\n  d: \"M13.2 18a3 3 0 0 0-2.2-5\",\n  key: \"1ol3lk\"\n}], [\"path\", {\n  d: \"M13 22H4a2 2 0 0 1 0-4h12\",\n  key: \"bt3f23\"\n}], [\"path\", {\n  d: \"M16 9h.01\",\n  key: \"1bdo4e\"\n}]]);\nexport { Rat as default };", "map": {"version": 3, "names": ["Rat", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\rat.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Rat\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) - https://lucide.dev/icons/rat\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Rat = createLucideIcon('Rat', [\n  [\n    'path',\n    {\n      d: 'M17 5c0-1.7-1.3-3-3-3s-3 1.3-3 3c0 .8.3 1.5.8 2H11c-3.9 0-7 3.1-7 7v0c0 2.2 1.8 4 4 4',\n      key: '16aj0u',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M16.8 3.9c.3-.3.6-.5 1-.7 1.5-.6 3.3.1 3.9 1.6.6 1.5-.1 3.3-1.6 3.9l1.6 2.8c.2.3.2.7.2 1-.2.8-.9 1.2-1.7 1.1 0 0-1.6-.3-2.7-.6H17c-1.7 0-3 1.3-3 3',\n      key: '1crdmb',\n    },\n  ],\n  ['path', { d: 'M13.2 18a3 3 0 0 0-2.2-5', key: '1ol3lk' }],\n  ['path', { d: 'M13 22H4a2 2 0 0 1 0-4h12', key: 'bt3f23' }],\n  ['path', { d: 'M16 9h.01', key: '1bdo4e' }],\n]);\n\nexport default Rat;\n"], "mappings": ";;;;;AAaM,MAAAA,GAAA,GAAMC,gBAAA,CAAiB,KAAO,GAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1D,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}