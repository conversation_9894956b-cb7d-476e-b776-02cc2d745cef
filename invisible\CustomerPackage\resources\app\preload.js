const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
    toggleWindow: () => ipcRenderer.invoke('toggle-window'),
    moveWindowLeft: () => ipcRenderer.invoke('move-window-left'),
    moveWindowRight: () => ipcRenderer.invoke('move-window-right'),
    moveWindowUp: () => ipcRenderer.invoke('move-window-up'),
    moveWindowDown: () => ipcRenderer.invoke('move-window-down'),

    // Chat functionality
    sendMessage: (message) => ipcRenderer.invoke('send-message', message),
    onReceiveMessage: (callback) => ipcRenderer.on('receive-message', (_, message) => callback(message)),

    // API key management
    saveApiKey: (key, type) => ipcRenderer.invoke('save-api-key', key, type),
    getApiKey: (type) => ipc<PERSON><PERSON>er.invoke('get-api-key', type),

    // Screenshot functionality
    takeScreenshot: () => ipcRenderer.invoke('take-screenshot'),
    analyzeScreenshot: () => ipcRenderer.invoke('analyze-screenshot'),
    onScreenshotTaken: (callback) => ipcRenderer.on('screenshot-taken', (_, data) => callback(data)),
    onAnalyzeScreenshot: (callback) => ipcRenderer.on('analyze-screenshot', (_, data) => callback(data)),
    readFileAsBase64: (filePath) => ipcRenderer.invoke('read-file-as-base64', filePath),

    // Mode switching
    onSwitchMode: (callback) => ipcRenderer.on('switch-mode', (_, mode) => callback(mode)),

    // 🚀 Global shortcuts for seamless workflow
    onGlobalAnalyze: (callback) => ipcRenderer.on('global-analyze-screenshots', () => callback()),
    onGlobalMCQFast: (callback) => ipcRenderer.on('global-mcq-fast', () => callback()),

    // Window management functions
    moveWindow: (direction) => {
        switch (direction) {
            case 'left': return ipcRenderer.invoke('move-window-left');
            case 'right': return ipcRenderer.invoke('move-window-right');
            case 'up': return ipcRenderer.invoke('move-window-up');
            case 'down': return ipcRenderer.invoke('move-window-down');
            default: return Promise.resolve({ success: false });
        }
    },
    decreaseOpacity: () => ipcRenderer.invoke('decrease-opacity'),
    increaseOpacity: () => ipcRenderer.invoke('increase-opacity'),
    resetView: () => ipcRenderer.invoke('reset-view'),
    quitApp: () => ipcRenderer.invoke('quit-app'),

    // Window resizing functions
    shrinkWindow: () => ipcRenderer.invoke('shrink-window'),
    resetWindowSize: () => ipcRenderer.invoke('reset-window-size')
});
