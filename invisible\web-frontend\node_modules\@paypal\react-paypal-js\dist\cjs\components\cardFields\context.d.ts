/// <reference types="react" />
import { RegistryHookReturnType } from "./hooks";
import type { PayPalCardFieldsComponent } from "@paypal/paypal-js/types/components/card-fields";
export type PayPalCardFieldsContextType = {
    cardFieldsForm: PayPalCardFieldsComponent | null;
} & RegistryHookReturnType;
export declare const PayPalCardFieldsContext: import("react").Context<PayPalCardFieldsContextType>;
