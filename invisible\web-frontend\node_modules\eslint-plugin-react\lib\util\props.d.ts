/**
 * Checks if the Identifier node passed in looks like a propTypes declaration.
 * @param {ASTNode} node The node to check. Must be an Identifier node.
 * @returns {boolean} `true` if the node is a propTypes declaration, `false` if not
 */
export function isPropTypesDeclaration(node: ASTNode): boolean;
/**
 * Checks if the node passed in looks like a contextTypes declaration.
 * @param {ASTNode} node The node to check.
 * @returns {boolean} `true` if the node is a contextTypes declaration, `false` if not
 */
export function isContextTypesDeclaration(node: ASTNode): boolean;
/**
 * Checks if the node passed in looks like a contextType declaration.
 * @param {ASTNode} node The node to check.
 * @returns {boolean} `true` if the node is a contextType declaration, `false` if not
 */
export function isContextTypeDeclaration(node: ASTNode): boolean;
/**
 * Checks if the node passed in looks like a childContextTypes declaration.
 * @param {ASTNode} node The node to check.
 * @returns {boolean} `true` if the node is a childContextTypes declaration, `false` if not
 */
export function isChildContextTypesDeclaration(node: ASTNode): boolean;
/**
 * Checks if the Identifier node passed in looks like a defaultProps declaration.
 * @param {ASTNode} node The node to check. Must be an Identifier node.
 * @returns {boolean} `true` if the node is a defaultProps declaration, `false` if not
 */
export function isDefaultPropsDeclaration(node: ASTNode): boolean;
/**
 * Checks if we are declaring a display name
 * @param {ASTNode} node The AST node being checked.
 * @returns {boolean} True if we are declaring a display name, false if not.
 */
export function isDisplayNameDeclaration(node: ASTNode): boolean;
/**
 * Checks if the PropTypes MemberExpression node passed in declares a required propType.
 * @param {ASTNode} propTypeExpression node to check. Must be a `PropTypes` MemberExpression.
 * @returns {boolean} `true` if this PropType is required, `false` if not.
 */
export function isRequiredPropType(propTypeExpression: ASTNode): boolean;
/**
 * Returns the type arguments of a node or type parameters if type arguments are not available.
 * @param {ASTNode} node The node to get the type arguments from.
 * @returns {ASTNode} The type arguments or type parameters of the node.
 */
export function getTypeArguments(node: ASTNode): ASTNode;
/**
 * Returns the super type arguments of a node or super type parameters if type arguments are not available.
 * @param {ASTNode} node The node to get the super type arguments from.
 * @returns {ASTNode} The super type arguments or parameters of the node.
 */
export function getSuperTypeArguments(node: ASTNode): ASTNode;
//# sourceMappingURL=props.d.ts.map