{"name": "eslint-plugin-jsx-a11y", "version": "6.10.2", "description": "Static AST checker for accessibility rules on JSX elements.", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "a11y", "accessibility", "jsx"], "author": "<PERSON>", "repository": {"type": "git", "url": "https://github.com/jsx-eslint/eslint-plugin-jsx-a11y"}, "main": "lib/index.js", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated && npm run build", "build": "rimraf lib && babel src --out-dir lib --copy-files", "create": "node ./scripts/create-rule", "flow": "flow", "lint:fix": "npm run lint -- --fix", "lint": "npx eslint@8 --ext=js,mjs,cjs,ts,tsx .", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest && npm run lint && npm run flow && npm run tests-only", "pretest": "npm run lint:fix && npm run flow", "test": "npm run tests-only", "tests-only": "tape --require=@babel/register '__tests__/**/*.js'", "posttest": "npx npm@'>=10.2' audit --production", "pretest:examples": "npm run build", "test:examples": "npm run test-example:legacy && npm run test-example:flat-esm && npm run test-example:flat-cjs", "test-example:legacy": "cd examples/legacy && npm install && npm run lint", "test-example:flat-esm": "cd examples/flat-esm && npm install && npm run lint", "test-example:flat-cjs": "cd examples/flat-cjs && npm install && npm run lint", "pregenerate-list-of-rules": "npm run build", "generate-list-of-rules": "eslint-doc-generator --rule-doc-title-format prefix-name --rule-doc-section-options false --config-emoji recommended,☑️ --ignore-config flat/recommended --ignore-config flat/strict", "generate-list-of-rules:check": "npm run generate-list-of-rules -- --check", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.26.0", "@babel/eslint-parser": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.9", "@babel/register": "^7.25.9", "auto-changelog": "^2.5.0", "babel-plugin-add-module-exports": "^1.0.4", "babel-preset-airbnb": "^5.0.0", "eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9", "eslint-config-airbnb-base": "^15.0.0", "eslint-doc-generator": "^1.7.1", "eslint-plugin-eslint-plugin": "^4.3.0", "eslint-plugin-flowtype": "^5.8.0 || ^8.0.3", "eslint-plugin-import": "^2.31.0", "estraverse": "^5.3.0", "flow-bin": "^0.147.0", "in-publish": "^2.0.1", "jackspeak": "=2.1.1", "jscodeshift": "^17.0.0", "minimist": "^1.2.8", "npmignore": "^0.3.1", "object.assign": "^4.1.5", "object.entries": "^1.1.8", "rimraf": "^3.0.2", "safe-publish-latest": "^2.0.0", "semver": "^6.3.1", "tape": "^5.9.0", "to-ast": "^1.0.0"}, "engines": {"node": ">=4.0"}, "license": "MIT", "dependencies": {"aria-query": "^5.3.2", "array-includes": "^3.1.8", "array.prototype.flatmap": "^1.3.2", "ast-types-flow": "^0.0.8", "axe-core": "^4.10.0", "axobject-query": "^4.1.0", "damerau-levenshtein": "^1.0.8", "emoji-regex": "^9.2.2", "hasown": "^2.0.2", "jsx-ast-utils": "^3.3.5", "language-tags": "^1.0.9", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "safe-regex-test": "^1.0.3", "string.prototype.includes": "^2.0.1"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true, "startingVersion": "6.6.2"}, "publishConfig": {"ignore": ["!lib", ".github/workflows", ".flowconfig", "/src", "/reports", "/flow", "scripts/", "CONTRIBUTING.md", "/examples"]}}