; 🚀 INVISIBLE ASSESSMENT TOOL - BASIC INSTALLER
; Minimal version that will definitely work

[Setup]
AppName=Invisible Assessment Tool
AppVersion=1.0.0
AppPublisher=Your Company Name
AppId={{B8E6D1A2-F4C3-4D5E-8F9A-1B2C3D4E5F6G}}
DefaultDirName={autopf}\InvisibleAssessmentTool
DefaultGroupName=Invisible Assessment Tool
OutputDir=installer-output
OutputBaseFilename=InvisibleAssessmentTool-Setup-v1.0.0
Compression=lzma2
SolidCompression=yes
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop shortcut"; GroupDescription: "Additional shortcuts:"; Flags: unchecked

[Files]
Source: "portable-app\InvisibleAssessmentTool.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "Debug_Launcher.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "CUSTOMER_INSTRUCTIONS.txt"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"
Name: "{group}\Debug Mode (Troubleshooting)"; Filename: "{app}\Debug_Launcher.bat"; IconFilename: "{app}\InvisibleAssessmentTool.exe"
Name: "{group}\User Instructions"; Filename: "{app}\CUSTOMER_INSTRUCTIONS.txt"
Name: "{group}\Uninstall Invisible Assessment Tool"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"; Tasks: desktopicon

[Run]
Filename: "{app}\InvisibleAssessmentTool.exe"; Description: "Launch Invisible Assessment Tool"; Flags: nowait postinstall skipifsilent

[Code]
var
  LicenseKeyPage: TInputQueryWizardPage;

procedure InitializeWizard;
begin
  LicenseKeyPage := CreateInputQueryPage(wpSelectTasks,
    'License Key', 'Enter your license key',
    'Please enter the license key you received after purchase:');
  LicenseKeyPage.Add('License Key:', False);
end;

function NextButtonClick(CurPageID: Integer): Boolean;
var
  LicenseKey: String;
  LicenseFile: String;
begin
  Result := True;
  
  if CurPageID = LicenseKeyPage.ID then
  begin
    LicenseKey := LicenseKeyPage.Values[0];
    
    if Length(LicenseKey) < 5 then
    begin
      MsgBox('Please enter a valid license key.', mbError, MB_OK);
      Result := False;
    end
    else
    begin
      LicenseFile := ExpandConstant('{app}\license.key');
      SaveStringToFile(LicenseFile, LicenseKey, False);
    end;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Installation completed successfully!' + #13#10 +
           'Your Invisible Assessment Tool is ready!' + #13#10 + #13#10 +
           'USAGE:' + #13#10 +
           '• Press Ctrl+B to show/hide interface' + #13#10 +
           '• Press Ctrl+H to take screenshots' + #13#10 +
           '• Press Ctrl+Enter to analyze problems' + #13#10 + #13#10 +
           'TROUBLESHOOTING:' + #13#10 +
           'If Ctrl+B doesn''t work, use "Debug Mode" from Start Menu',
           mbInformation, MB_OK);
  end;
end;
