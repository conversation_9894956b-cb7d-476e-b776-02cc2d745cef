; 🚀 INVISIBLE ASSESSMENT TOOL - BASIC INSTALLER
; Minimal version that will definitely work

[Setup]
AppName=Invisible Assessment Tool
AppVersion=1.0.2
AppPublisher=Your Company Name
AppId={{B8E6D1A2-F4C3-4D5E-8F9A-1B2C3D4E5F6G}}
DefaultDirName={autopf}\InvisibleAssessmentTool
DefaultGroupName=Invisible Assessment Tool
OutputDir=installer-output
OutputBaseFilename=InvisibleAssessmentTool-Setup-v1.0.2-COMPLETE
Compression=lzma2
SolidCompression=yes
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop shortcut"; GroupDescription: "Additional shortcuts:"; Flags: unchecked

[Files]
; Main executable
Source: "portable-app\InvisibleAssessmentTool.exe"; DestDir: "{app}"; Flags: ignoreversion

; ICU data file (CRITICAL - fixes the error you found!)
Source: "portable-app\icudtl.dat"; DestDir: "{app}"; Flags: ignoreversion

; Chrome/Electron resource files
Source: "portable-app\chrome_100_percent.pak"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\chrome_200_percent.pak"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\resources.pak"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\snapshot_blob.bin"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\v8_context_snapshot.bin"; DestDir: "{app}"; Flags: ignoreversion

; Required DLL files
Source: "portable-app\d3dcompiler_47.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\ffmpeg.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\libEGL.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\libGLESv2.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\vulkan-1.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\vk_swiftshader.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\vk_swiftshader_icd.json"; DestDir: "{app}"; Flags: ignoreversion

; Locales folder (all language files)
Source: "portable-app\locales\*"; DestDir: "{app}\locales"; Flags: ignoreversion recursesubdirs

; Resources folder (app code)
Source: "portable-app\resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs

; Version and license files
Source: "portable-app\version"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\LICENSE"; DestDir: "{app}"; Flags: ignoreversion
Source: "portable-app\LICENSES.chromium.html"; DestDir: "{app}"; Flags: ignoreversion

; Debug and instruction files
Source: "Debug_Launcher.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "CUSTOMER_INSTRUCTIONS.txt"; DestDir: "{app}"; Flags: ignoreversion

; Directory placeholder files (ensure directories exist)
Source: "screenshots\.gitkeep"; DestDir: "{app}\screenshots"; Flags: ignoreversion
Source: "temp\.gitkeep"; DestDir: "{app}\temp"; Flags: ignoreversion
Source: "cache\.gitkeep"; DestDir: "{app}\cache"; Flags: ignoreversion

[Icons]
Name: "{group}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"
Name: "{group}\Debug Mode (Troubleshooting)"; Filename: "{app}\Debug_Launcher.bat"; IconFilename: "{app}\InvisibleAssessmentTool.exe"
Name: "{group}\User Instructions"; Filename: "{app}\CUSTOMER_INSTRUCTIONS.txt"
Name: "{group}\Uninstall Invisible Assessment Tool"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"; Tasks: desktopicon

[Dirs]
; Create required directories for screenshots and cache
Name: "{app}\screenshots"
Name: "{app}\temp"
Name: "{app}\cache"

[Run]
Filename: "{app}\InvisibleAssessmentTool.exe"; Description: "Launch Invisible Assessment Tool"; Flags: nowait postinstall skipifsilent

[Code]
var
  LicenseKeyPage: TInputQueryWizardPage;

procedure InitializeWizard;
begin
  LicenseKeyPage := CreateInputQueryPage(wpSelectTasks,
    'License Key', 'Enter your license key',
    'Please enter the license key you received after purchase:');
  LicenseKeyPage.Add('License Key:', False);
end;

function NextButtonClick(CurPageID: Integer): Boolean;
var
  LicenseKey: String;
  LicenseFile: String;
begin
  Result := True;
  
  if CurPageID = LicenseKeyPage.ID then
  begin
    LicenseKey := LicenseKeyPage.Values[0];
    
    if Length(LicenseKey) < 5 then
    begin
      MsgBox('Please enter a valid license key.', mbError, MB_OK);
      Result := False;
    end
    else
    begin
      LicenseFile := ExpandConstant('{app}\license.key');
      SaveStringToFile(LicenseFile, LicenseKey, False);
    end;
  end;
end;

procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('🎉 INSTALLATION COMPLETED SUCCESSFULLY!' + #13#10 +
           'Your Invisible Assessment Tool is ready!' + #13#10 + #13#10 +
           '🚀 CORE SHORTCUTS:' + #13#10 +
           '• Ctrl+B = Show/Hide interface' + #13#10 +
           '• Ctrl+H = Take screenshots' + #13#10 +
           '• Ctrl+Enter = Analyze problems' + #13#10 +
           '• Ctrl+D = DSA mode' + #13#10 +
           '• Ctrl+C = Chatbot mode' + #13#10 +
           '• Ctrl+N = Interview mode' + #13#10 + #13#10 +
           '📋 FEATURES INCLUDED:' + #13#10 +
           '✅ AI-powered problem solving' + #13#10 +
           '✅ Screenshot analysis' + #13#10 +
           '✅ MCQ assistance' + #13#10 +
           '✅ Interview copilot' + #13#10 +
           '✅ Debug assistant' + #13#10 +
           '✅ Complete stealth mode' + #13#10 + #13#10 +
           '🔧 TROUBLESHOOTING:' + #13#10 +
           'Use "Debug Mode" from Start Menu if needed' + #13#10 +
           'Check "User Instructions" for complete shortcut list',
           mbInformation, MB_OK);
  end;
end;
