{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArrowBigRightDash = createLucideIcon(\"ArrowBigRightDash\", [[\"path\", {\n  d: \"M5 9v6\",\n  key: \"158jrl\"\n}], [\"path\", {\n  d: \"M9 9h3V5l7 7-7 7v-4H9V9z\",\n  key: \"1sg2xn\"\n}]]);\nexport { ArrowBigRightDash as default };", "map": {"version": 3, "names": ["ArrowBigRightDash", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\arrow-big-right-dash.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArrowBigRightDash\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSA5djYiIC8+CiAgPHBhdGggZD0iTTkgOWgzVjVsNyA3LTcgN3YtNEg5Vjl6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/arrow-big-right-dash\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArrowBigRightDash = createLucideIcon('ArrowBigRightDash', [\n  ['path', { d: 'M5 9v6', key: '158jrl' }],\n  ['path', { d: 'M9 9h3V5l7 7-7 7v-4H9V9z', key: '1sg2xn' }],\n]);\n\nexport default ArrowBigRightDash;\n"], "mappings": ";;;;;AAaM,MAAAA,iBAAA,GAAoBC,gBAAA,CAAiB,mBAAqB,GAC9D,CAAC,MAAQ;EAAEC,CAAA,EAAG,QAAU;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvC,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}