{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Profile.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, Mail, Calendar, CreditCard } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProfileContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = ProfileContainer;\nconst ProfileContent = styled.div`\n  max-width: 600px;\n  margin: 0 auto;\n`;\n_c2 = ProfileContent;\nconst ProfileCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n_c3 = ProfileCard;\nconst ProfileHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n`;\n_c4 = ProfileHeader;\nconst ProfileTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n`;\n_c5 = ProfileTitle;\nconst ProfileInfo = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n_c6 = ProfileInfo;\nconst InfoItem = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n`;\n_c7 = InfoItem;\nconst InfoLabel = styled.div`\n  font-weight: 600;\n  min-width: 120px;\n`;\n_c8 = InfoLabel;\nconst InfoValue = styled.div`\n  opacity: 0.9;\n`;\n_c9 = InfoValue;\nconst Profile = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  return /*#__PURE__*/_jsxDEV(ProfileContainer, {\n    children: /*#__PURE__*/_jsxDEV(ProfileContent, {\n      children: /*#__PURE__*/_jsxDEV(ProfileCard, {\n        children: [/*#__PURE__*/_jsxDEV(ProfileHeader, {\n          children: /*#__PURE__*/_jsxDEV(ProfileTitle, {\n            children: \"Profile\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ProfileInfo, {\n          children: [/*#__PURE__*/_jsxDEV(InfoItem, {\n            children: [/*#__PURE__*/_jsxDEV(User, {\n              size: 24,\n              style: {\n                color: '#4CAF50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoLabel, {\n              children: \"Name:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n              children: user === null || user === void 0 ? void 0 : user.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 74,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoItem, {\n            children: [/*#__PURE__*/_jsxDEV(Mail, {\n              size: 24,\n              style: {\n                color: '#4CAF50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoLabel, {\n              children: \"Email:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n              children: user === null || user === void 0 ? void 0 : user.email\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoItem, {\n            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n              size: 24,\n              style: {\n                color: '#4CAF50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoLabel, {\n              children: \"Member Since:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n              children: new Date(user === null || user === void 0 ? void 0 : user.registrationDate).toLocaleDateString()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(InfoItem, {\n            children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n              size: 24,\n              style: {\n                color: '#4CAF50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoLabel, {\n              children: \"Total Purchases:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(InfoValue, {\n              children: (user === null || user === void 0 ? void 0 : user.totalPurchases) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(Profile, \"9ep4vdl3mBfipxjmc+tQCDhw6Ik=\", false, function () {\n  return [useAuth];\n});\n_c0 = Profile;\nexport default Profile;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0;\n$RefreshReg$(_c, \"ProfileContainer\");\n$RefreshReg$(_c2, \"ProfileContent\");\n$RefreshReg$(_c3, \"ProfileCard\");\n$RefreshReg$(_c4, \"ProfileHeader\");\n$RefreshReg$(_c5, \"ProfileTitle\");\n$RefreshReg$(_c6, \"ProfileInfo\");\n$RefreshReg$(_c7, \"InfoItem\");\n$RefreshReg$(_c8, \"InfoLabel\");\n$RefreshReg$(_c9, \"InfoValue\");\n$RefreshReg$(_c0, \"Profile\");", "map": {"version": 3, "names": ["React", "styled", "useAuth", "User", "Mail", "Calendar", "CreditCard", "jsxDEV", "_jsxDEV", "ProfileContainer", "div", "_c", "ProfileContent", "_c2", "ProfileCard", "_c3", "ProfileHeader", "_c4", "ProfileTitle", "h1", "_c5", "ProfileInfo", "_c6", "InfoItem", "_c7", "InfoLabel", "_c8", "InfoValue", "_c9", "Profile", "_s", "user", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "style", "color", "name", "email", "Date", "registrationDate", "toLocaleDateString", "totalPurchases", "_c0", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Profile.js"], "sourcesContent": ["import React from 'react';\nimport styled from 'styled-components';\nimport { useAuth } from '../contexts/AuthContext';\nimport { User, Mail, Calendar, CreditCard } from 'lucide-react';\n\nconst ProfileContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst ProfileContent = styled.div`\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\nconst ProfileCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n`;\n\nconst ProfileHeader = styled.div`\n  text-align: center;\n  margin-bottom: 40px;\n`;\n\nconst ProfileTitle = styled.h1`\n  font-size: 2.5rem;\n  margin-bottom: 10px;\n`;\n\nconst ProfileInfo = styled.div`\n  display: grid;\n  gap: 20px;\n`;\n\nconst InfoItem = styled.div`\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 10px;\n`;\n\nconst InfoLabel = styled.div`\n  font-weight: 600;\n  min-width: 120px;\n`;\n\nconst InfoValue = styled.div`\n  opacity: 0.9;\n`;\n\nconst Profile = () => {\n  const { user } = useAuth();\n\n  return (\n    <ProfileContainer>\n      <ProfileContent>\n        <ProfileCard>\n          <ProfileHeader>\n            <ProfileTitle>Profile</ProfileTitle>\n          </ProfileHeader>\n\n          <ProfileInfo>\n            <InfoItem>\n              <User size={24} style={{ color: '#4CAF50' }} />\n              <InfoLabel>Name:</InfoLabel>\n              <InfoValue>{user?.name}</InfoValue>\n            </InfoItem>\n\n            <InfoItem>\n              <Mail size={24} style={{ color: '#4CAF50' }} />\n              <InfoLabel>Email:</InfoLabel>\n              <InfoValue>{user?.email}</InfoValue>\n            </InfoItem>\n\n            <InfoItem>\n              <Calendar size={24} style={{ color: '#4CAF50' }} />\n              <InfoLabel>Member Since:</InfoLabel>\n              <InfoValue>{new Date(user?.registrationDate).toLocaleDateString()}</InfoValue>\n            </InfoItem>\n\n            <InfoItem>\n              <CreditCard size={24} style={{ color: '#4CAF50' }} />\n              <InfoLabel>Total Purchases:</InfoLabel>\n              <InfoValue>{user?.totalPurchases || 0}</InfoValue>\n            </InfoItem>\n          </ProfileInfo>\n        </ProfileCard>\n      </ProfileContent>\n    </ProfileContainer>\n  );\n};\n\nexport default Profile;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,mBAAmB;AACtC,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,gBAAgB,GAAGR,MAAM,CAACS,GAAG;AACnC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,gBAAgB;AAOtB,MAAMG,cAAc,GAAGX,MAAM,CAACS,GAAG;AACjC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,cAAc;AAKpB,MAAME,WAAW,GAAGb,MAAM,CAACS,GAAG;AAC9B;AACA;AACA;AACA;AACA;AACA,CAAC;AAACK,GAAA,GANID,WAAW;AAQjB,MAAME,aAAa,GAAGf,MAAM,CAACS,GAAG;AAChC;AACA;AACA,CAAC;AAACO,GAAA,GAHID,aAAa;AAKnB,MAAME,YAAY,GAAGjB,MAAM,CAACkB,EAAE;AAC9B;AACA;AACA,CAAC;AAACC,GAAA,GAHIF,YAAY;AAKlB,MAAMG,WAAW,GAAGpB,MAAM,CAACS,GAAG;AAC9B;AACA;AACA,CAAC;AAACY,GAAA,GAHID,WAAW;AAKjB,MAAME,QAAQ,GAAGtB,MAAM,CAACS,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACc,GAAA,GAPID,QAAQ;AASd,MAAME,SAAS,GAAGxB,MAAM,CAACS,GAAG;AAC5B;AACA;AACA,CAAC;AAACgB,GAAA,GAHID,SAAS;AAKf,MAAME,SAAS,GAAG1B,MAAM,CAACS,GAAG;AAC5B;AACA,CAAC;AAACkB,GAAA,GAFID,SAAS;AAIf,MAAME,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM;IAAEC;EAAK,CAAC,GAAG7B,OAAO,CAAC,CAAC;EAE1B,oBACEM,OAAA,CAACC,gBAAgB;IAAAuB,QAAA,eACfxB,OAAA,CAACI,cAAc;MAAAoB,QAAA,eACbxB,OAAA,CAACM,WAAW;QAAAkB,QAAA,gBACVxB,OAAA,CAACQ,aAAa;UAAAgB,QAAA,eACZxB,OAAA,CAACU,YAAY;YAAAc,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAc;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEhB5B,OAAA,CAACa,WAAW;UAAAW,QAAA,gBACVxB,OAAA,CAACe,QAAQ;YAAAS,QAAA,gBACPxB,OAAA,CAACL,IAAI;cAACkC,IAAI,EAAE,EAAG;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/C5B,OAAA,CAACiB,SAAS;cAAAO,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5B5B,OAAA,CAACmB,SAAS;cAAAK,QAAA,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAES;YAAI;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B,CAAC,eAEX5B,OAAA,CAACe,QAAQ;YAAAS,QAAA,gBACPxB,OAAA,CAACJ,IAAI;cAACiC,IAAI,EAAE,EAAG;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC/C5B,OAAA,CAACiB,SAAS;cAAAO,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC7B5B,OAAA,CAACmB,SAAS;cAAAK,QAAA,EAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU;YAAK;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B,CAAC,eAEX5B,OAAA,CAACe,QAAQ;YAAAS,QAAA,gBACPxB,OAAA,CAACH,QAAQ;cAACgC,IAAI,EAAE,EAAG;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnD5B,OAAA,CAACiB,SAAS;cAAAO,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpC5B,OAAA,CAACmB,SAAS;cAAAK,QAAA,EAAE,IAAIU,IAAI,CAACX,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,gBAAgB,CAAC,CAACC,kBAAkB,CAAC;YAAC;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC,eAEX5B,OAAA,CAACe,QAAQ;YAAAS,QAAA,gBACPxB,OAAA,CAACF,UAAU;cAAC+B,IAAI,EAAE,EAAG;cAACC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAU;YAAE;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrD5B,OAAA,CAACiB,SAAS;cAAAO,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACvC5B,OAAA,CAACmB,SAAS;cAAAK,QAAA,EAAE,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEc,cAAc,KAAI;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEvB,CAAC;AAACN,EAAA,CAxCID,OAAO;EAAA,QACM3B,OAAO;AAAA;AAAA4C,GAAA,GADpBjB,OAAO;AA0Cb,eAAeA,OAAO;AAAC,IAAAlB,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAkB,GAAA;AAAAC,YAAA,CAAApC,EAAA;AAAAoC,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAA9B,GAAA;AAAA8B,YAAA,CAAA3B,GAAA;AAAA2B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAvB,GAAA;AAAAuB,YAAA,CAAArB,GAAA;AAAAqB,YAAA,CAAAnB,GAAA;AAAAmB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}