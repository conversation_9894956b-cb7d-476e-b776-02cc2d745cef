{"ast": null, "code": "'use strict';\n\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n  min = min !== undefined ? min : 1000;\n  return function push(chunkLength) {\n    const now = Date.now();\n    const startedAt = timestamps[tail];\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n    let i = tail;\n    let bytesCount = 0;\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n    head = (head + 1) % samplesCount;\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n    if (now - firstSampleTS < min) {\n      return;\n    }\n    const passed = startedAt && now - startedAt;\n    return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\nexport default speedometer;", "map": {"version": 3, "names": ["speedometer", "samplesCount", "min", "bytes", "Array", "timestamps", "head", "tail", "firstSampleTS", "undefined", "push", "chunkLength", "now", "Date", "startedAt", "i", "bytesCount", "passed", "Math", "round"], "sources": ["D:/electron/invisible/web-frontend/node_modules/axios/lib/helpers/speedometer.js"], "sourcesContent": ["'use strict';\n\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */\nfunction speedometer(samplesCount, min) {\n  samplesCount = samplesCount || 10;\n  const bytes = new Array(samplesCount);\n  const timestamps = new Array(samplesCount);\n  let head = 0;\n  let tail = 0;\n  let firstSampleTS;\n\n  min = min !== undefined ? min : 1000;\n\n  return function push(chunkLength) {\n    const now = Date.now();\n\n    const startedAt = timestamps[tail];\n\n    if (!firstSampleTS) {\n      firstSampleTS = now;\n    }\n\n    bytes[head] = chunkLength;\n    timestamps[head] = now;\n\n    let i = tail;\n    let bytesCount = 0;\n\n    while (i !== head) {\n      bytesCount += bytes[i++];\n      i = i % samplesCount;\n    }\n\n    head = (head + 1) % samplesCount;\n\n    if (head === tail) {\n      tail = (tail + 1) % samplesCount;\n    }\n\n    if (now - firstSampleTS < min) {\n      return;\n    }\n\n    const passed = startedAt && now - startedAt;\n\n    return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n  };\n}\n\nexport default speedometer;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,WAAWA,CAACC,YAAY,EAAEC,GAAG,EAAE;EACtCD,YAAY,GAAGA,YAAY,IAAI,EAAE;EACjC,MAAME,KAAK,GAAG,IAAIC,KAAK,CAACH,YAAY,CAAC;EACrC,MAAMI,UAAU,GAAG,IAAID,KAAK,CAACH,YAAY,CAAC;EAC1C,IAAIK,IAAI,GAAG,CAAC;EACZ,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIC,aAAa;EAEjBN,GAAG,GAAGA,GAAG,KAAKO,SAAS,GAAGP,GAAG,GAAG,IAAI;EAEpC,OAAO,SAASQ,IAAIA,CAACC,WAAW,EAAE;IAChC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IAEtB,MAAME,SAAS,GAAGT,UAAU,CAACE,IAAI,CAAC;IAElC,IAAI,CAACC,aAAa,EAAE;MAClBA,aAAa,GAAGI,GAAG;IACrB;IAEAT,KAAK,CAACG,IAAI,CAAC,GAAGK,WAAW;IACzBN,UAAU,CAACC,IAAI,CAAC,GAAGM,GAAG;IAEtB,IAAIG,CAAC,GAAGR,IAAI;IACZ,IAAIS,UAAU,GAAG,CAAC;IAElB,OAAOD,CAAC,KAAKT,IAAI,EAAE;MACjBU,UAAU,IAAIb,KAAK,CAACY,CAAC,EAAE,CAAC;MACxBA,CAAC,GAAGA,CAAC,GAAGd,YAAY;IACtB;IAEAK,IAAI,GAAG,CAACA,IAAI,GAAG,CAAC,IAAIL,YAAY;IAEhC,IAAIK,IAAI,KAAKC,IAAI,EAAE;MACjBA,IAAI,GAAG,CAACA,IAAI,GAAG,CAAC,IAAIN,YAAY;IAClC;IAEA,IAAIW,GAAG,GAAGJ,aAAa,GAAGN,GAAG,EAAE;MAC7B;IACF;IAEA,MAAMe,MAAM,GAAGH,SAAS,IAAIF,GAAG,GAAGE,SAAS;IAE3C,OAAOG,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACH,UAAU,GAAG,IAAI,GAAGC,MAAM,CAAC,GAAGR,SAAS;EACpE,CAAC;AACH;AAEA,eAAeT,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}