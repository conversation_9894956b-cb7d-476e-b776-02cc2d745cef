{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Keyboard = createLucideIcon(\"Keyboard\", [[\"rect\", {\n  width: \"20\",\n  height: \"16\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"15u882\"\n}], [\"path\", {\n  d: \"M6 8h.001\",\n  key: \"1ej0i3\"\n}], [\"path\", {\n  d: \"M10 8h.001\",\n  key: \"1x2st2\"\n}], [\"path\", {\n  d: \"M14 8h.001\",\n  key: \"1vkmyp\"\n}], [\"path\", {\n  d: \"M18 8h.001\",\n  key: \"kfsenl\"\n}], [\"path\", {\n  d: \"M8 12h.001\",\n  key: \"1sjpby\"\n}], [\"path\", {\n  d: \"M12 12h.001\",\n  key: \"al75ts\"\n}], [\"path\", {\n  d: \"M16 12h.001\",\n  key: \"931bgk\"\n}], [\"path\", {\n  d: \"M7 16h10\",\n  key: \"wp8him\"\n}]]);\nexport { Keyboard as default };", "map": {"version": 3, "names": ["Keyboard", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\keyboard.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Keyboard\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHg9IjIiIHk9IjQiIHJ4PSIyIiByeT0iMiIgLz4KICA8cGF0aCBkPSJNNiA4aC4wMDEiIC8+CiAgPHBhdGggZD0iTTEwIDhoLjAwMSIgLz4KICA8cGF0aCBkPSJNMTQgOGguMDAxIiAvPgogIDxwYXRoIGQ9Ik0xOCA4aC4wMDEiIC8+CiAgPHBhdGggZD0iTTggMTJoLjAwMSIgLz4KICA8cGF0aCBkPSJNMTIgMTJoLjAwMSIgLz4KICA8cGF0aCBkPSJNMTYgMTJoLjAwMSIgLz4KICA8cGF0aCBkPSJNNyAxNmgxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/keyboard\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Keyboard = createLucideIcon('Keyboard', [\n  [\n    'rect',\n    {\n      width: '20',\n      height: '16',\n      x: '2',\n      y: '4',\n      rx: '2',\n      ry: '2',\n      key: '15u882',\n    },\n  ],\n  ['path', { d: 'M6 8h.001', key: '1ej0i3' }],\n  ['path', { d: 'M10 8h.001', key: '1x2st2' }],\n  ['path', { d: 'M14 8h.001', key: '1vkmyp' }],\n  ['path', { d: 'M18 8h.001', key: 'kfsenl' }],\n  ['path', { d: 'M8 12h.001', key: '1sjpby' }],\n  ['path', { d: 'M12 12h.001', key: 'al75ts' }],\n  ['path', { d: 'M16 12h.001', key: '931bgk' }],\n  ['path', { d: 'M7 16h10', key: 'wp8him' }],\n]);\n\nexport default Keyboard;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,aAAe;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}