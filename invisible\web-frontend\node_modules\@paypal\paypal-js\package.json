{"name": "@paypal/paypal-js", "version": "8.2.0", "description": "Loading wrapper and TypeScript types for the PayPal JS SDK", "main": "index.js", "module": "dist/esm/paypal-js.js", "types": "types/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "rm -rf dist && rollup --config --bundleConfigAsCjs", "lint": "eslint .", "prerelease": "npm run validate", "release": "standard-version --commit-all", "postrelease": "git push && git push --follow-tags && npm run build && npm publish", "test": "vitest src", "test:bundle": "vitest bundle-tests/**", "test:e2e": "playwright test", "test:e2e:debug": "PWDEBUG=1 playwright test --project=\"chromium\"", "test:e2e:start": "node e2e-tests/http-server.js", "typecheck": "tsc --noEmit --allowSyntheticDefaultImports", "validate": "npm run check-node-version && npm run typecheck && npm run build && npm run lint && npm test -- --watch=false --coverage.enabled --coverage.include=src && npm run test:bundle -- --watch=false", "openapi": "npm run openapi-orders && npm run openapi-subscriptions", "openapi-orders": "openapi-typescript node_modules/@paypal/paypal-rest-api-specifications/openapi/checkout_orders_v2.json -o types/apis/openapi/checkout_orders_v2.d.ts", "openapi-subscriptions": "openapi-typescript node_modules/@paypal/paypal-rest-api-specifications/openapi/billing_subscriptions_v1.json -o types/apis/openapi/billing_subscriptions_v1.d.ts"}, "files": ["dist", "types", "!types/**/*.test.ts"], "keywords": ["paypal", "sdk", "async", "script", "loader", "types", "typescript", "typescript-definitions"], "license": "Apache-2.0", "repository": {"type": "git", "url": "git://github.com/paypal/paypal-js.git"}, "devDependencies": {"@commitlint/cli": "18.4.3", "@commitlint/config-conventional": "18.4.3", "@paypal/paypal-rest-api-specifications": "^1.0.0", "@playwright/test": "^1.40.1", "@rollup/plugin-commonjs": "25.0.7", "@rollup/plugin-node-resolve": "15.2.3", "@rollup/plugin-replace": "5.0.5", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "11.1.5", "@types/node": "^20.10.5", "@types/promise-polyfill": "6.0.6", "@typescript-eslint/eslint-plugin": "6.15.0", "@typescript-eslint/parser": "6.15.0", "@vitest/coverage-v8": "^1.1.0", "eslint": "8.56.0", "filesize": "10.1.0", "husky": "8.0.3", "jsdom": "^23.0.1", "lint-staged": "15.2.0", "openapi-typescript": "^6.7.3", "rollup": "4.9.1", "semver": "7.5.4", "standard-version": "9.5.0", "tslib": "2.6.2", "typescript": "5.3.3", "vitest": "1.1.0"}, "dependencies": {"promise-polyfill": "^8.3.0"}}