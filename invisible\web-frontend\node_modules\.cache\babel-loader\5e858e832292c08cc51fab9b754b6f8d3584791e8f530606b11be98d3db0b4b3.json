{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst BatteryMedium = createLucideIcon(\"BatteryMedium\", [[\"rect\", {\n  width: \"16\",\n  height: \"10\",\n  x: \"2\",\n  y: \"7\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1w10f2\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"22\",\n  y1: \"11\",\n  y2: \"13\",\n  key: \"4dh1rd\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"11\",\n  y2: \"13\",\n  key: \"1wd6dw\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"11\",\n  y2: \"13\",\n  key: \"haxvl5\"\n}]]);\nexport { BatteryMedium as default };", "map": {"version": 3, "names": ["BatteryMedium", "createLucideIcon", "width", "height", "x", "y", "rx", "ry", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\battery-medium.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name BatteryMedium\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTAiIHg9IjIiIHk9IjciIHJ4PSIyIiByeT0iMiIgLz4KICA8bGluZSB4MT0iMjIiIHgyPSIyMiIgeTE9IjExIiB5Mj0iMTMiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSI2IiB5MT0iMTEiIHkyPSIxMyIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/battery-medium\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst BatteryMedium = createLucideIcon('BatteryMedium', [\n  [\n    'rect',\n    {\n      width: '16',\n      height: '10',\n      x: '2',\n      y: '7',\n      rx: '2',\n      ry: '2',\n      key: '1w10f2',\n    },\n  ],\n  ['line', { x1: '22', x2: '22', y1: '11', y2: '13', key: '4dh1rd' }],\n  ['line', { x1: '6', x2: '6', y1: '11', y2: '13', key: '1wd6dw' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '13', key: 'haxvl5' }],\n]);\n\nexport default BatteryMedium;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,KAAO;EACPC,MAAQ;EACRC,CAAG;EACHC,CAAG;EACHC,EAAI;EACJC,EAAI;EACJC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAClE,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}