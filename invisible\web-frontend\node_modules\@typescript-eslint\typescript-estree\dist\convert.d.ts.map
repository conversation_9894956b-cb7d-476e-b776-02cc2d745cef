{"version": 3, "file": "convert.d.ts", "sourceRoot": "", "sources": ["../src/convert.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,MAAM,YAAY,CAAC;AAGjC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AAuB5C,OAAO,KAAK,EACV,aAAa,EACb,2BAA2B,EAC5B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,gCAAgC,CAAC;AAC/E,OAAO,KAAK,EAAE,QAAQ,EAAoB,MAAM,EAAE,MAAM,aAAa,CAAC;AAMtE,UAAU,gBAAgB;IACxB,qBAAqB,EAAE,OAAO,CAAC;IAC/B,sBAAsB,EAAE,OAAO,CAAC;CACjC;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAC1B,KAAK,EAAE,EAAE,CAAC,sBAAsB,GAAG,wBAAwB,GAC1D,OAAO,CAMT;AAED,MAAM,WAAW,OAAO;IACtB,qBAAqB,EAAE,2BAA2B,CAAC;IACnD,qBAAqB,EAAE,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC7D;AAED,qBAAa,SAAS;IACpB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAgB;IACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAmB;IAC3C,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAiB;IACvD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAiB;IAEvD,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,UAAU,CAAS;IAE3B;;;;;OAKG;gBACS,GAAG,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,EAAE,gBAAgB;IAKzD,UAAU,IAAI,OAAO;IAOrB,cAAc,IAAI,QAAQ,CAAC,OAAO;IAIlC;;;;;;;OAOG;IACH,OAAO,CAAC,SAAS;IAkCjB;;;;;OAKG;IACH,OAAO,CAAC,UAAU;IAgElB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAW/B;;;;;OAKG;IACH,OAAO,CAAC,cAAc;IAItB;;;;;OAKG;IACH,OAAO,CAAC,YAAY;IAIpB;;;;;OAKG;IACH,OAAO,CAAC,WAAW;IAInB,OAAO,CAAC,UAAU;IAsBlB,OAAO,CAAC,oCAAoC;IAe5C;;;;;;OAMG;IACH,OAAO,CAAC,qBAAqB;IAqB7B;;;;;OAKG;IACH,OAAO,CAAC,sBAAsB;IAkC9B;;;;;OAKG;IACH,OAAO,CAAC,oCAAoC;IAa5C;;;;OAIG;IACH,OAAO,CAAC,kDAAkD;IAe1D;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IAiBzB,OAAO,CAAC,sBAAsB;IA4C9B;;;;OAIG;IACH,OAAO,CAAC,UAAU;IAwFlB,OAAO,CAAC,oBAAoB;IAW5B,OAAO,CAAC,+BAA+B;IAiDvC;;;;;OAKG;IACH,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,sBAAsB;IA4D9B,OAAO,CAAC,mBAAmB;IAQ3B;;;;;;;;OAQG;IACH,OAAO,CAAC,sBAAsB;IA+C9B;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IAczB,OAAO,CAAC,qBAAqB;IAwB7B;;;;;;;OAOG;IACH,OAAO,CAAC,WAAW;CAurEpB"}