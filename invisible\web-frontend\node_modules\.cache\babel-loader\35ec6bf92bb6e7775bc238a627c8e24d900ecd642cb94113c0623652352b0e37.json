{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ClipboardPaste = createLucideIcon(\"ClipboardPaste\", [[\"path\", {\n  d: \"M15 2H9a1 1 0 0 0-1 1v2c0 .6.4 1 1 1h6c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1Z\",\n  key: \"1pp7kr\"\n}], [\"path\", {\n  d: \"M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2M16 4h2a2 2 0 0 1 2 2v2M11 14h10\",\n  key: \"2ik1ml\"\n}], [\"path\", {\n  d: \"m17 10 4 4-4 4\",\n  key: \"vp2hj1\"\n}]]);\nexport { ClipboardPaste as default };", "map": {"version": 3, "names": ["ClipboardPaste", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\clipboard-paste.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ClipboardPaste\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg5YTEgMSAwIDAgMC0xIDF2MmMwIC42LjQgMSAxIDFoNmMuNiAwIDEtLjQgMS0xVjNjMC0uNi0uNC0xLTEtMVoiIC8+CiAgPHBhdGggZD0iTTggNEg2YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yTTE2IDRoMmEyIDIgMCAwIDEgMiAydjJNMTEgMTRoMTAiIC8+CiAgPHBhdGggZD0ibTE3IDEwIDQgNC00IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/clipboard-paste\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ClipboardPaste = createLucideIcon('ClipboardPaste', [\n  [\n    'path',\n    {\n      d: 'M15 2H9a1 1 0 0 0-1 1v2c0 .6.4 1 1 1h6c.6 0 1-.4 1-1V3c0-.6-.4-1-1-1Z',\n      key: '1pp7kr',\n    },\n  ],\n  [\n    'path',\n    {\n      d: 'M8 4H6a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2M16 4h2a2 2 0 0 1 2 2v2M11 14h10',\n      key: '2ik1ml',\n    },\n  ],\n  ['path', { d: 'm17 10 4 4-4 4', key: 'vp2hj1' }],\n]);\n\nexport default ClipboardPaste;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}