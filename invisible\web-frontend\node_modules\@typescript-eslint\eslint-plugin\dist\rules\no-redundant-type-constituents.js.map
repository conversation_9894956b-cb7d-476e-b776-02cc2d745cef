{"version": 3, "file": "no-redundant-type-constituents.js", "sourceRoot": "", "sources": ["../../src/rules/no-redundant-type-constituents.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,oDAAoE;AACpE,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,MAAM,2BAA2B,GAAG;IAClC,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM;IACjD,CAAC,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO;IACnD,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM;IACjD,CAAC,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM;IACjD,CAAC,EAAE,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM;CAC3C,CAAC;AAEX,MAAM,gBAAgB,GAAG;IACvB,EAAE,CAAC,SAAS,CAAC,aAAa;IAC1B,EAAE,CAAC,SAAS,CAAC,cAAc;IAC3B,EAAE,CAAC,SAAS,CAAC,aAAa;IAC1B,EAAE,CAAC,SAAS,CAAC,aAAa;IAC1B,EAAE,CAAC,SAAS,CAAC,eAAe;CACpB,CAAC;AAEX,MAAM,kBAAkB,GAAG;IACzB,EAAE,CAAC,SAAS,CAAC,MAAM;IACnB,EAAE,CAAC,SAAS,CAAC,OAAO;IACpB,EAAE,CAAC,SAAS,CAAC,MAAM;IACnB,EAAE,CAAC,SAAS,CAAC,MAAM;CACX,CAAC;AAEX,MAAM,sBAAsB,GAAG;IAC7B,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ;IAC/B,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,SAAS;IACjC,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ;IAC/B,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ;CACvB,CAAC;AAEX,MAAM,sBAAsB,GAAG;IAC7B,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa;IAClC,OAAO,EAAE,EAAE,CAAC,SAAS,CAAC,cAAc;IACpC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa;IAClC,MAAM,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa;CAC1B,CAAC;AAEX,MAAM,yBAAyB,GAAG,IAAI,GAAG,CAAC;IACxC,CAAC,gBAAQ,CAAC,cAAc,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;IACxD,CAAC,gBAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;IAC9D,CAAC,gBAAQ,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;IAChE,CAAC,gBAAQ,CAAC,cAAc,CAAC,cAAc,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;IAC5D,CAAC,gBAAQ,CAAC,cAAc,CAAC,gBAAgB,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;IAChE,CAAC,gBAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;IAC9D,CAAC,gBAAQ,CAAC,cAAc,CAAC,eAAe,EAAE,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;CAC/D,CAAC,CAAC;AAcH,SAAS,aAAa,CACpB,GAAsB,EACtB,GAAQ,EACR,KAAY;IAEZ,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAE9B,IAAI,QAAQ,EAAE;QACZ,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACtB;SAAM;QACL,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;KACvB;AACH,CAAC;AAED,SAAS,mBAAmB,CAAC,IAAa;IACxC,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KACnC;IAED,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;QACtC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,CAAC;KACtE;IAED,IAAI,IAAI,CAAC,SAAS,EAAE,EAAE;QACpB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;KAC9B;IAED,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE;QAC5B,OAAO,KAAK,CAAC;KACd;IAED,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;QAC9B,OAAO,OAAO,CAAC;KAChB;IAED,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAChC,OAAO,SAAS,CAAC;KAClB;IAED,IAAI,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,EAAE;QACxC,OAAO,uBAAuB,CAAC;KAChC;IAED,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;QAC5C,OAAO,MAAM,CAAC;KACf;IAED,IAAI,OAAO,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;QAC7C,OAAO,OAAO,CAAC;KAChB;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,uBAAuB,CAAC,QAA2B;IAC1D,QAAQ,QAAQ,CAAC,IAAI,EAAE;QACrB,KAAK,sBAAc,CAAC,YAAY;YAC9B,OAAO,KAAK,CAAC;QACf,KAAK,sBAAc,CAAC,gBAAgB;YAClC,OAAO,SAAS,CAAC;QACnB,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,OAAO,CAAC;QACjB,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,QAAQ,CAAC;QAClB,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,QAAQ,CAAC;QAClB,KAAK,sBAAc,CAAC,gBAAgB;YAClC,OAAO,SAAS,CAAC;QACnB,KAAK,sBAAc,CAAC,aAAa;YAC/B,QAAQ,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE;gBAC7B,KAAK,gBAAQ,CAAC,cAAc,CAAC,OAAO;oBAClC,QAAQ,OAAO,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE;wBACrC,KAAK,QAAQ;4BACX,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAC7C,QAAQ,CAAC,OAAO,CAAC,KACnB,GAAG,CAAC;wBACN,KAAK,QAAQ;4BACX,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;wBAChD;4BACE,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;qBACtC;gBACH,KAAK,gBAAQ,CAAC,cAAc,CAAC,eAAe;oBAC1C,OAAO,uBAAuB,CAAC;aAClC;KACJ;IAED,OAAO,cAAc,CAAC;AACxB,CAAC;AAED,SAAS,sBAAsB,CAAC,IAA0B;;IACxD,OAAO,CAAC,CAAC,CACP,CAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,MAAK,sBAAc,CAAC,gBAAgB;QACrD,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YACtC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CACvC,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,2BAA2B,CAAC,IAAa;IAChD,OAAO,IAAI,CAAC,OAAO,EAAE;QACnB,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC;QACvB,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC;QAClD,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;QACjD,CAAC,CAAC,CAAC,IAAI,CAAC;QACR,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACnC,CAAC;AAED,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,gCAAgC;IACtC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,2FAA2F;YAC7F,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,gEAAgE;YACnF,mBAAmB,EAAE,2EAA2E;YAChG,UAAU,EAAE,yEAAyE;YACrF,SAAS,EAAE,sEAAsE;SAClF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,UAAU,GAAG,IAAI,GAAG,EAA0C,CAAC;QAErE,SAAS,wBAAwB,CAC/B,QAA2B;YAE3B,MAAM,gBAAgB,GAAG,yBAAyB,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACtE,IAAI,gBAAgB,EAAE;gBACpB,OAAO;oBACL;wBACE,SAAS,EAAE,gBAAgB;wBAC3B,QAAQ,EAAE,uBAAuB,CAAC,QAAQ,CAAC;qBAC5C;iBACF,CAAC;aACH;YAED,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa;gBAC9C,QAAQ,CAAC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAChD;gBACA,OAAO;oBACL;wBACE,SAAS,EACP,sBAAsB,CACpB,OAAO,QAAQ,CAAC,OAAO;6BACpB,KAA4C,CAChD;wBACH,QAAQ,EAAE,uBAAuB,CAAC,QAAQ,CAAC;qBAC5C;iBACF,CAAC;aACH;YAED,IAAI,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC;aACzD;YAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClE,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YACxD,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG,2BAA2B,CAAC,QAAQ,CAAC,CAAC;YAExD,OAAO,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;gBAChC,SAAS,EAAE,QAAQ,CAAC,KAAK;gBACzB,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,CAAC;aACxC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,SAAS,8BAA8B,CACrC,QAA2B;YAE3B,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC1C,IAAI,QAAQ,EAAE;gBACZ,OAAO,QAAQ,CAAC;aACjB;YAED,MAAM,OAAO,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YACnD,UAAU,CAAC,GAAG,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAClC,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAO;YACL,yBAAyB,CAAC,IAAiC;gBACzD,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAA+B,CAAC;gBAChE,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAG/B,CAAC;gBAEJ,SAAS,kCAAkC,CACzC,EAAE,SAAS,EAAE,QAAQ,EAAqB,EAC1C,QAA2B;oBAE3B,KAAK,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC,IAAI;wBACnC,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;wBAC/B,CAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,CAAC;wBACjC,CAAC,YAAY,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;qBAC5B,EAAE;wBACV,IAAI,SAAS,KAAK,SAAS,EAAE;4BAC3B,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE;oCACJ,SAAS,EAAE,cAAc;oCACzB,QAAQ;iCACT;gCACD,SAAS;gCACT,IAAI,EAAE,QAAQ;6BACf,CAAC,CAAC;4BACH,OAAO,IAAI,CAAC;yBACb;qBACF;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;oBACjC,MAAM,aAAa,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC;oBAE/D,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE;wBACpC,IAAI,kCAAkC,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;4BAC1D,SAAS;yBACV;wBAED,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;4BAC9C,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,EAAE;gCAC1C,aAAa,CACX,gBAAgB,EAChB,2BAA2B,CAAC,eAAe,CAAC,EAC5C,QAAQ,CAAC,QAAQ,CAClB,CAAC;gCACF,MAAM;6BACP;yBACF;wBAED,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE;4BAClD,IAAI,QAAQ,CAAC,SAAS,KAAK,iBAAiB,EAAE;gCAC5C,aAAa,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,QAAQ,CAAC,CAAC;6BAChE;yBACF;qBACF;iBACF;gBAED,2DAA2D;gBAC3D,sDAAsD;gBACtD,iDAAiD;gBACjD,KAAK,MAAM,CAAC,iBAAiB,EAAE,SAAS,CAAC,IAAI,kBAAkB,EAAE;oBAC/D,MAAM,mBAAmB,GAAG,gBAAgB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;oBACpE,IAAI,mBAAmB,EAAE;wBACvB,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;4BAChC,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE;oCACJ,OAAO,EAAE,mBAAmB,CAAC,IAAI,CAAC,KAAK,CAAC;oCACxC,SAAS,EAAE,sBAAsB,CAAC,iBAAiB,CAAC;iCACrD;gCACD,SAAS,EAAE,qBAAqB;gCAChC,IAAI,EAAE,QAAQ;6BACf,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;YACD,kBAAkB,CAAC,IAA0B;gBAC3C,MAAM,gBAAgB,GAAG,IAAI,GAAG,EAG7B,CAAC;gBACJ,MAAM,kBAAkB,GAAG,IAAI,GAAG,EAAqB,CAAC;gBAExD,SAAS,2BAA2B,CAClC,EAAE,SAAS,EAAE,QAAQ,EAAqB,EAC1C,QAA2B;oBAE3B,KAAK,MAAM,SAAS,IAAI;wBACtB,EAAE,CAAC,SAAS,CAAC,GAAG;wBAChB,EAAE,CAAC,SAAS,CAAC,OAAO;qBACZ,EAAE;wBACV,IAAI,SAAS,KAAK,SAAS,EAAE;4BAC3B,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE;oCACJ,SAAS,EAAE,OAAO;oCAClB,QAAQ;iCACT;gCACD,SAAS,EAAE,WAAW;gCACtB,IAAI,EAAE,QAAQ;6BACf,CAAC,CAAC;4BACH,OAAO,IAAI,CAAC;yBACb;qBACF;oBAED,IACE,SAAS,KAAK,EAAE,CAAC,SAAS,CAAC,KAAK;wBAChC,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAC7B;wBACA,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE;gCACJ,SAAS,EAAE,OAAO;gCAClB,QAAQ,EAAE,OAAO;6BAClB;4BACD,SAAS,EAAE,YAAY;4BACvB,IAAI,EAAE,QAAQ;yBACf,CAAC,CAAC;wBACH,OAAO,IAAI,CAAC;qBACb;oBAED,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;oBACjC,MAAM,aAAa,GAAG,8BAA8B,CAAC,QAAQ,CAAC,CAAC;oBAE/D,KAAK,MAAM,QAAQ,IAAI,aAAa,EAAE;wBACpC,IAAI,2BAA2B,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;4BACnD,SAAS;yBACV;wBAED,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;4BAC9C,IAAI,QAAQ,CAAC,SAAS,KAAK,eAAe,EAAE;gCAC1C,aAAa,CACX,gBAAgB,EAChB,2BAA2B,CAAC,eAAe,CAAC,EAC5C;oCACE,YAAY,EAAE,QAAQ,CAAC,QAAQ;oCAC/B,QAAQ;iCACT,CACF,CAAC;gCACF,MAAM;6BACP;yBACF;wBAED,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE;4BAClD,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,iBAAiB,CAAC,KAAK,CAAC,EAAE;gCAClD,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;6BAC3C;yBACF;qBACF;iBACF;gBAOD,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAGhC,CAAC;gBAEJ,yDAAyD;gBACzD,wDAAwD;gBACxD,yEAAyE;gBACzE,KAAK,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,CAAC,IAAI,gBAAgB,EAAE;oBACrE,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;wBAC7C,KAAK,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,IAAI,iBAAiB,EAAE;4BAC1D,aAAa,CAAC,mBAAmB,EAAE,QAAQ,EAAE;gCAC3C,YAAY;gCACZ,iBAAiB;6BAClB,CAAC,CAAC;yBACJ;qBACF;iBACF;gBAED,+DAA+D;gBAC/D,gDAAgD;gBAChD,wDAAwD;gBACxD,KAAK,MAAM,CAAC,QAAQ,EAAE,iBAAiB,CAAC,IAAI,mBAAmB,EAAE;oBAC/D,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CACpC,iBAAiB,EACjB,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,iBAAiB,CAC/B,CAAC;oBAEF,KAAK,MAAM,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,OAAO,EAAE;wBAChD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE;gCACJ,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;gCACzD,SAAS,EAAE,sBAAsB,CAAC,iBAAiB,CAAC;6BACrD;4BACD,SAAS,EAAE,mBAAmB;4BAC9B,IAAI,EAAE,QAAQ;yBACf,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}