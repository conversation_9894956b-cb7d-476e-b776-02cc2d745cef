const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 CREATING SECURE BUILD FOR PAYING CUSTOMERS');
console.log('=============================================');

// Step 1: Create license validation in main.js
console.log('\n🔑 STEP 1: Adding license validation...');

const licenseValidationCode = `
// 🔒 LICENSE VALIDATION FOR PAYING CUSTOMERS
const crypto = require('crypto');
const os = require('os');

// Generate hardware fingerprint
function getHardwareId() {
    const cpus = os.cpus();
    const networkInterfaces = os.networkInterfaces();
    const platform = os.platform();
    const arch = os.arch();
    
    const hwString = JSON.stringify({
        platform,
        arch,
        cpuModel: cpus[0]?.model || 'unknown',
        totalMemory: os.totalmem(),
        hostname: os.hostname()
    });
    
    return crypto.createHash('sha256').update(hwString).digest('hex').substring(0, 16);
}

// Validate license on startup
async function validateLicense() {
    try {
        // Check if license file exists
        const licenseFile = path.join(__dirname, 'license.key');
        if (!fs.existsSync(licenseFile)) {
            console.log('❌ No license found. This software requires a valid license.');
            return false;
        }

        const licenseData = fs.readFileSync(licenseFile, 'utf8');
        const [licenseKey, hardwareId] = licenseData.split('|');
        
        // Verify hardware binding
        const currentHardwareId = getHardwareId();
        if (hardwareId && hardwareId !== currentHardwareId) {
            console.log('❌ License is bound to different hardware.');
            return false;
        }

        // If no hardware binding, bind to current hardware
        if (!hardwareId) {
            fs.writeFileSync(licenseFile, \`\${licenseKey}|\${currentHardwareId}\`);
            console.log('✅ License bound to current hardware.');
        }

        console.log('✅ License validated successfully.');
        return true;
    } catch (error) {
        console.error('❌ License validation failed:', error.message);
        return false;
    }
}
`;

// Step 2: Create secure download system
console.log('\n📥 STEP 2: Creating secure download system...');

const secureDownloadCode = `
// 🔒 SECURE DOWNLOAD WITH LICENSE INJECTION
const createSecureDownload = async (licenseKey, userId) => {
    try {
        // Read the base executable
        const baseExePath = path.join(__dirname, 'downloads', 'InvisibleAssessmentTool-Base.exe');
        const baseExe = fs.readFileSync(baseExePath);
        
        // Create license file content
        const licenseContent = \`\${licenseKey}|\`; // Hardware ID will be added on first run
        
        // Create temporary directory for this download
        const tempDir = path.join(__dirname, 'temp', \`download_\${userId}_\${Date.now()}\`);
        fs.mkdirSync(tempDir, { recursive: true });
        
        // Copy executable to temp directory
        const tempExePath = path.join(tempDir, 'InvisibleAssessmentTool.exe');
        fs.writeFileSync(tempExePath, baseExe);
        
        // Create license file
        const licenseFilePath = path.join(tempDir, 'license.key');
        fs.writeFileSync(licenseFilePath, licenseContent);
        
        // Create installer script
        const installerScript = \`
@echo off
echo 🚀 Installing Invisible Assessment Tool...
echo.

REM Create application directory
set "INSTALL_DIR=%LOCALAPPDATA%\\\\InvisibleAssessmentTool"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
copy "InvisibleAssessmentTool.exe" "%INSTALL_DIR%\\\\" >nul
copy "license.key" "%INSTALL_DIR%\\\\" >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "\\$WshShell = New-Object -comObject WScript.Shell; \\$Shortcut = \\$WshShell.CreateShortcut('\\$env:USERPROFILE\\\\Desktop\\\\Invisible Assessment Tool.lnk'); \\$Shortcut.TargetPath = '%INSTALL_DIR%\\\\InvisibleAssessmentTool.exe'; \\$Shortcut.Save()"

echo.
echo ✅ Installation complete!
echo 📍 Installed to: %INSTALL_DIR%
echo 🖥️ Desktop shortcut created
echo.
echo 🚀 You can now run the Invisible Assessment Tool from your desktop!
pause
        \`;
        
        const installerPath = path.join(tempDir, 'install.bat');
        fs.writeFileSync(installerPath, installerScript);
        
        return tempDir;
    } catch (error) {
        console.error('❌ Secure download creation failed:', error);
        throw error;
    }
};
`;

console.log('\n✅ Secure build system created!');
console.log('\n📋 NEXT STEPS:');
console.log('1. Run: node build-secure-executable.js');
console.log('2. Test the secure download system');
console.log('3. Deploy to production');

console.log('\n🔒 SECURITY FEATURES:');
console.log('✅ Hardware-bound licenses');
console.log('✅ Secure download tokens');
console.log('✅ License validation on startup');
console.log('✅ Automatic installation script');
console.log('✅ Only paying customers can download');
