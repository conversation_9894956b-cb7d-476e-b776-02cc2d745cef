{"ast": null, "code": "var _jsxFileName = \"D:\\\\electron\\\\invisible\\\\web-frontend\\\\src\\\\pages\\\\Purchase.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CreditCard, Shield, Download, CheckCircle, Smartphone, CreditCard as CardIcon } from 'lucide-react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PurchaseContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n_c = PurchaseContainer;\nconst PurchaseContent = styled.div`\n  max-width: 1000px;\n  margin: 0 auto;\n`;\n_c2 = PurchaseContent;\nconst PurchaseHeader = styled.div`\n  text-align: center;\n  margin-bottom: 60px;\n`;\n_c3 = PurchaseHeader;\nconst PurchaseTitle = styled.h1`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n_c4 = PurchaseTitle;\nconst PurchaseSubtitle = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n_c5 = PurchaseSubtitle;\nconst PurchaseGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 40px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n_c6 = PurchaseGrid;\nconst PlanCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  &.recommended {\n    border: 2px solid #4CAF50;\n  }\n`;\n_c7 = PlanCard;\nconst RecommendedBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n_c8 = RecommendedBadge;\nconst PlanTitle = styled.h2`\n  font-size: 1.8rem;\n  margin-bottom: 10px;\n  color: #4CAF50;\n`;\n_c9 = PlanTitle;\nconst PlanPrice = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n_c0 = PlanPrice;\nconst PlanFeatures = styled.ul`\n  list-style: none;\n  margin-bottom: 30px;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    display: flex;\n    align-items: center;\n    \n    svg {\n      margin-right: 10px;\n      color: #4CAF50;\n    }\n  }\n`;\n_c1 = PlanFeatures;\nconst PaymentSection = styled.div`\n  margin-top: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n`;\n_c10 = PaymentSection;\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40px;\n  \n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n    border-top: 4px solid #4CAF50;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n_c11 = LoadingSpinner;\nconst Purchase = () => {\n  _s();\n  const {\n    user\n  } = useAuth();\n  const navigate = useNavigate();\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [userLicenses, setUserLicenses] = useState([]);\n  useEffect(() => {\n    fetchUserLicenses();\n  }, []);\n  const fetchUserLicenses = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success) {\n        setUserLicenses(response.data.data.licenses);\n      }\n    } catch (error) {\n      console.error('Error fetching licenses:', error);\n    }\n  };\n  const hasActiveLicense = userLicenses.some(license => license.isValid);\n  const isFirstPurchase = (user === null || user === void 0 ? void 0 : user.totalPurchases) === 0;\n  const plans = [{\n    id: 'initial',\n    title: 'Initial Purchase',\n    price: '₹10',\n    amount: 10,\n    screenshots: 50,\n    description: 'Perfect for getting started (TESTING PRICE)',\n    features: ['50 Screenshots per month', 'AI-powered problem analysis', 'Complete stealth integration', 'Hardware binding security', 'Global keyboard shortcuts', 'All programming languages', 'Lifetime software access', 'Priority support'],\n    recommended: isFirstPurchase,\n    available: isFirstPurchase\n  }, {\n    id: 'renewal',\n    title: 'Monthly Renewal',\n    price: '₹5',\n    amount: 5,\n    screenshots: 45,\n    description: 'Continue your subscription (TESTING PRICE)',\n    features: ['45 Screenshots per month', 'AI-powered problem analysis', 'Complete stealth integration', 'Hardware binding security', 'Global keyboard shortcuts', 'All programming languages', 'Continued access', 'Regular updates'],\n    recommended: !isFirstPurchase,\n    available: !isFirstPurchase || hasActiveLicense\n  }];\n  const createRazorpayOrder = async planId => {\n    try {\n      setLoading(true);\n\n      // 🔒 CHECK IF USER ALREADY HAS VALID LICENSE\n      if (hasActiveLicense) {\n        toast.error('You already have an active license! Please use your existing license or wait for it to expire.');\n        setLoading(false);\n        return;\n      }\n      const plan = plans.find(p => p.id === planId);\n      console.log('🚀 Creating Razorpay order for plan:', planId);\n      console.log('📊 Axios base URL:', axios.defaults.baseURL);\n      console.log('🔑 Auth header:', axios.defaults.headers.common['Authorization']);\n\n      // Create Razorpay order\n      const response = await axios.post('/payments/create', {\n        tier: planId === 'initial' ? 1 : 2\n      });\n      console.log('✅ Response received:', response.data);\n      if (response.data.success) {\n        const {\n          orderId,\n          amount,\n          currency,\n          razorpayKeyId,\n          userDetails,\n          testMode\n        } = response.data.data;\n\n        // Check if this is test mode\n        if (testMode) {\n          // Test mode - simulate payment success\n          toast.info('Test mode: Simulating payment...');\n          setTimeout(async () => {\n            await verifyRazorpayPayment({\n              razorpay_order_id: orderId,\n              razorpay_payment_id: `pay_test_${Date.now()}`,\n              razorpay_signature: 'test_signature'\n            }, plan);\n          }, 2000);\n          return;\n        }\n\n        // Real Razorpay integration\n        const options = {\n          key: razorpayKeyId,\n          amount: amount * 100,\n          // Amount in paise\n          currency: currency,\n          name: 'Invisible Assessment Tool',\n          description: plan.description,\n          order_id: orderId,\n          prefill: {\n            name: userDetails.name,\n            email: userDetails.email\n          },\n          theme: {\n            color: '#4CAF50'\n          },\n          handler: async response => {\n            await verifyRazorpayPayment(response, plan);\n          },\n          modal: {\n            ondismiss: () => {\n              setLoading(false);\n              toast.info('Payment cancelled');\n            }\n          }\n        };\n\n        // Open Razorpay checkout\n        const razorpay = new window.Razorpay(options);\n        razorpay.open();\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Frontend payment creation error:', error);\n      console.error('❌ Error response:', error.response);\n      console.error('❌ Error message:', error.message);\n      toast.error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Failed to create payment');\n      setLoading(false);\n    }\n  };\n  const verifyRazorpayPayment = async (paymentResponse, plan) => {\n    try {\n      // For test mode, create license directly\n      if (paymentResponse.razorpay_order_id.startsWith('order_test_')) {\n        console.log('🧪 Test mode - creating license directly');\n        const response = await axios.get(`/payments/create-test-license/${user.id}`);\n        if (response.data.success) {\n          toast.success('Test payment successful! License created.');\n          navigate('/download', {\n            state: {\n              licenseKey: response.data.data.licenseKey,\n              plan: plan\n            }\n          });\n          return;\n        }\n      }\n\n      // Real payment verification\n      const response = await axios.post('/payments/verify', {\n        razorpay_order_id: paymentResponse.razorpay_order_id,\n        razorpay_payment_id: paymentResponse.razorpay_payment_id,\n        razorpay_signature: paymentResponse.razorpay_signature\n      });\n      if (response.data.success) {\n        toast.success('Payment successful! License activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('❌ Payment verification error:', error);\n      toast.error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || 'Payment verification failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 🧪 DIRECT TEST LICENSE CREATION\n  const createDirectTestLicense = async planId => {\n    try {\n      setLoading(true);\n\n      // 🔒 CHECK IF USER ALREADY HAS VALID LICENSE\n      if (hasActiveLicense) {\n        toast.error('You already have an active license! Please use your existing license or wait for it to expire.');\n        setLoading(false);\n        return;\n      }\n      const plan = plans.find(p => p.id === planId);\n      console.log('🧪 Creating direct test license...');\n      const response = await axios.get(`/payments/create-test-license/${user.id}`);\n      if (response.data.success) {\n        toast.success('Test license created successfully!');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      console.error('❌ Direct test license error:', error);\n      toast.error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || 'Failed to create test license');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const onPayPalApprove = async (data, actions, planId) => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/payments/execute', {\n        paymentId: data.orderID,\n        payerId: data.payerID\n      });\n      if (response.data.success) {\n        toast.success('Payment successful! Your license has been activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plans.find(p => p.id === planId)\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      toast.error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || 'Payment processing failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const onPayPalError = error => {\n    console.error('PayPal error:', error);\n    toast.error('Payment failed. Please try again.');\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(PurchaseContainer, {\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"spinner\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            marginLeft: '20px'\n          },\n          children: \"Processing payment...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 400,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 398,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(PurchaseContainer, {\n    children: /*#__PURE__*/_jsxDEV(PurchaseContent, {\n      children: [/*#__PURE__*/_jsxDEV(PurchaseHeader, {\n        children: [/*#__PURE__*/_jsxDEV(PurchaseTitle, {\n          children: \"\\uD83D\\uDCB3 Choose Your Plan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 410,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(PurchaseSubtitle, {\n          children: \"Secure your access to the Invisible Assessment Tool\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 409,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(PurchaseGrid, {\n        children: plans.map(plan => /*#__PURE__*/_jsxDEV(PlanCard, {\n          className: plan.recommended ? 'recommended' : '',\n          children: [plan.recommended && /*#__PURE__*/_jsxDEV(RecommendedBadge, {\n            children: \"RECOMMENDED\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 36\n          }, this), /*#__PURE__*/_jsxDEV(PlanTitle, {\n            children: plan.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PlanPrice, {\n            children: plan.price\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            style: {\n              opacity: 0.8,\n              marginBottom: '20px'\n            },\n            children: plan.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(PlanFeatures, {\n            children: plan.features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this), feature]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 15\n          }, this), plan.available ? /*#__PURE__*/_jsxDEV(PaymentSection, {\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              style: {\n                marginBottom: '15px',\n                display: 'flex',\n                alignItems: 'center'\n              },\n              children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                size: 20,\n                style: {\n                  marginRight: '10px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 21\n              }, this), \"Secure Payment with Razorpay\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                fontSize: '0.9rem',\n                opacity: '0.8',\n                marginBottom: '15px'\n              },\n              children: \"\\uD83D\\uDCB3 Cards \\u2022 \\uD83D\\uDCF1 UPI \\u2022 \\uD83C\\uDFE6 Net Banking \\u2022 \\uD83D\\uDCB0 Wallets\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 443,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                width: '100%',\n                background: 'linear-gradient(45deg, #3395ff, #1976d2)',\n                color: 'white',\n                border: 'none',\n                padding: '15px',\n                borderRadius: '10px',\n                fontSize: '1.1rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                gap: '10px',\n                marginBottom: '10px'\n              },\n              onClick: () => createRazorpayOrder(plan.id),\n              disabled: loading,\n              children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"loading-spinner\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 470,\n                  columnNumber: 25\n                }, this), \"Processing...\"]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(CreditCard, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 25\n                }, this), \"Pay \", plan.price, \" with Razorpay\"]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              style: {\n                width: '100%',\n                background: 'linear-gradient(45deg, #4CAF50, #45a049)',\n                color: 'white',\n                border: 'none',\n                padding: '10px',\n                borderRadius: '5px',\n                fontSize: '0.9rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.3s ease'\n              },\n              onClick: () => createDirectTestLicense(plan.id),\n              disabled: loading,\n              children: \"\\uD83E\\uDDEA Create Test License (Skip Payment)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 482,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '20px',\n              background: 'rgba(255, 255, 255, 0.1)',\n              borderRadius: '10px',\n              textAlign: 'center',\n              opacity: 0.7\n            },\n            children: [/*#__PURE__*/_jsxDEV(Shield, {\n              size: 24,\n              style: {\n                marginBottom: '10px'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 509,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Not available for your account type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 17\n          }, this)]\n        }, plan.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 407,\n    columnNumber: 5\n  }, this);\n};\n_s(Purchase, \"d/KFbHmNnLvZRXuS1L4OKdgJn+I=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c12 = Purchase;\nexport default Purchase;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1, _c10, _c11, _c12;\n$RefreshReg$(_c, \"PurchaseContainer\");\n$RefreshReg$(_c2, \"PurchaseContent\");\n$RefreshReg$(_c3, \"PurchaseHeader\");\n$RefreshReg$(_c4, \"PurchaseTitle\");\n$RefreshReg$(_c5, \"PurchaseSubtitle\");\n$RefreshReg$(_c6, \"PurchaseGrid\");\n$RefreshReg$(_c7, \"PlanCard\");\n$RefreshReg$(_c8, \"RecommendedBadge\");\n$RefreshReg$(_c9, \"PlanTitle\");\n$RefreshReg$(_c0, \"PlanPrice\");\n$RefreshReg$(_c1, \"PlanFeatures\");\n$RefreshReg$(_c10, \"PaymentSection\");\n$RefreshReg$(_c11, \"LoadingSpinner\");\n$RefreshReg$(_c12, \"Purchase\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "styled", "toast", "axios", "useAuth", "CreditCard", "Shield", "Download", "CheckCircle", "Smartphone", "CardIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Pur<PERSON><PERSON><PERSON><PERSON>", "div", "_c", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_c3", "PurchaseTitle", "h1", "_c4", "PurchaseSubtitle", "p", "_c5", "PurchaseGrid", "_c6", "PlanCard", "_c7", "RecommendedBadge", "_c8", "PlanTitle", "h2", "_c9", "PlanPrice", "_c0", "PlanFeatures", "ul", "_c1", "PaymentSection", "_c10", "LoadingSpinner", "_c11", "Purchase", "_s", "user", "navigate", "<PERSON><PERSON><PERSON>", "setSelectedPlan", "loading", "setLoading", "userLicenses", "setUserLicenses", "fetchUserLicenses", "response", "get", "data", "success", "licenses", "error", "console", "hasActiveLicense", "some", "license", "<PERSON><PERSON><PERSON><PERSON>", "isFirstPurchase", "totalPurchases", "plans", "id", "title", "price", "amount", "screenshots", "description", "features", "recommended", "available", "createRazorpayOrder", "planId", "plan", "find", "log", "defaults", "baseURL", "headers", "common", "post", "tier", "orderId", "currency", "razorpayKeyId", "userDetails", "testMode", "info", "setTimeout", "verifyRazorpayPayment", "razorpay_order_id", "razorpay_payment_id", "Date", "now", "razorpay_signature", "options", "key", "name", "order_id", "prefill", "email", "theme", "color", "handler", "modal", "ondismiss", "razorpay", "window", "Razorpay", "open", "Error", "message", "_error$response", "_error$response$data", "paymentResponse", "startsWith", "state", "licenseKey", "_error$response2", "_error$response2$data", "createDirectTestLicense", "_error$response3", "_error$response3$data", "onPayPalApprove", "actions", "paymentId", "orderID", "payerId", "payerID", "_error$response4", "_error$response4$data", "onPayPalError", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "map", "opacity", "marginBottom", "feature", "index", "size", "display", "alignItems", "marginRight", "fontSize", "width", "background", "border", "padding", "borderRadius", "fontWeight", "cursor", "transition", "justifyContent", "gap", "onClick", "disabled", "textAlign", "_c12", "$RefreshReg$"], "sources": ["D:/electron/invisible/web-frontend/src/pages/Purchase.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport styled from 'styled-components';\nimport toast from 'react-hot-toast';\nimport axios from 'axios';\nimport { useAuth } from '../contexts/AuthContext';\nimport { CreditCard, Shield, Download, CheckCircle, Smartphone, CreditCard as CardIcon } from 'lucide-react';\n\nconst PurchaseContainer = styled.div`\n  min-height: 100vh;\n  padding: 120px 20px 40px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n`;\n\nconst PurchaseContent = styled.div`\n  max-width: 1000px;\n  margin: 0 auto;\n`;\n\nconst PurchaseHeader = styled.div`\n  text-align: center;\n  margin-bottom: 60px;\n`;\n\nconst PurchaseTitle = styled.h1`\n  font-size: 3rem;\n  margin-bottom: 20px;\n  font-weight: 700;\n`;\n\nconst PurchaseSubtitle = styled.p`\n  font-size: 1.2rem;\n  opacity: 0.9;\n  max-width: 600px;\n  margin: 0 auto;\n`;\n\nconst PurchaseGrid = styled.div`\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 40px;\n  \n  @media (max-width: 768px) {\n    grid-template-columns: 1fr;\n  }\n`;\n\nconst PlanCard = styled.div`\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(10px);\n  border-radius: 20px;\n  padding: 40px;\n  border: 1px solid rgba(255, 255, 255, 0.2);\n  position: relative;\n  \n  &.recommended {\n    border: 2px solid #4CAF50;\n  }\n`;\n\nconst RecommendedBadge = styled.div`\n  position: absolute;\n  top: -15px;\n  left: 50%;\n  transform: translateX(-50%);\n  background: #4CAF50;\n  color: white;\n  padding: 8px 20px;\n  border-radius: 20px;\n  font-size: 0.9rem;\n  font-weight: bold;\n`;\n\nconst PlanTitle = styled.h2`\n  font-size: 1.8rem;\n  margin-bottom: 10px;\n  color: #4CAF50;\n`;\n\nconst PlanPrice = styled.div`\n  font-size: 3rem;\n  font-weight: bold;\n  margin-bottom: 20px;\n`;\n\nconst PlanFeatures = styled.ul`\n  list-style: none;\n  margin-bottom: 30px;\n  \n  li {\n    padding: 10px 0;\n    border-bottom: 1px solid rgba(255,255,255,0.1);\n    display: flex;\n    align-items: center;\n    \n    svg {\n      margin-right: 10px;\n      color: #4CAF50;\n    }\n  }\n`;\n\nconst PaymentSection = styled.div`\n  margin-top: 30px;\n  padding: 20px;\n  background: rgba(255, 255, 255, 0.05);\n  border-radius: 15px;\n`;\n\nconst LoadingSpinner = styled.div`\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 40px;\n  \n  .spinner {\n    width: 40px;\n    height: 40px;\n    border: 4px solid rgba(255, 255, 255, 0.3);\n    border-top: 4px solid #4CAF50;\n    border-radius: 50%;\n    animation: spin 1s linear infinite;\n  }\n  \n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\nconst Purchase = () => {\n  const { user } = useAuth();\n  const navigate = useNavigate();\n  const [selectedPlan, setSelectedPlan] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [userLicenses, setUserLicenses] = useState([]);\n\n  useEffect(() => {\n    fetchUserLicenses();\n  }, []);\n\n  const fetchUserLicenses = async () => {\n    try {\n      const response = await axios.get('/licenses/my-licenses');\n      if (response.data.success) {\n        setUserLicenses(response.data.data.licenses);\n      }\n    } catch (error) {\n      console.error('Error fetching licenses:', error);\n    }\n  };\n\n  const hasActiveLicense = userLicenses.some(license => license.isValid);\n  const isFirstPurchase = user?.totalPurchases === 0;\n\n  const plans = [\n    {\n      id: 'initial',\n      title: 'Initial Purchase',\n      price: '₹10',\n      amount: 10,\n      screenshots: 50,\n      description: 'Perfect for getting started (TESTING PRICE)',\n      features: [\n        '50 Screenshots per month',\n        'AI-powered problem analysis',\n        'Complete stealth integration',\n        'Hardware binding security',\n        'Global keyboard shortcuts',\n        'All programming languages',\n        'Lifetime software access',\n        'Priority support'\n      ],\n      recommended: isFirstPurchase,\n      available: isFirstPurchase\n    },\n    {\n      id: 'renewal',\n      title: 'Monthly Renewal',\n      price: '₹5',\n      amount: 5,\n      screenshots: 45,\n      description: 'Continue your subscription (TESTING PRICE)',\n      features: [\n        '45 Screenshots per month',\n        'AI-powered problem analysis',\n        'Complete stealth integration',\n        'Hardware binding security',\n        'Global keyboard shortcuts',\n        'All programming languages',\n        'Continued access',\n        'Regular updates'\n      ],\n      recommended: !isFirstPurchase,\n      available: !isFirstPurchase || hasActiveLicense\n    }\n  ];\n\n  const createRazorpayOrder = async (planId) => {\n    try {\n      setLoading(true);\n\n      // 🔒 CHECK IF USER ALREADY HAS VALID LICENSE\n      if (hasActiveLicense) {\n        toast.error('You already have an active license! Please use your existing license or wait for it to expire.');\n        setLoading(false);\n        return;\n      }\n\n      const plan = plans.find(p => p.id === planId);\n\n      console.log('🚀 Creating Razorpay order for plan:', planId);\n      console.log('📊 Axios base URL:', axios.defaults.baseURL);\n      console.log('🔑 Auth header:', axios.defaults.headers.common['Authorization']);\n\n      // Create Razorpay order\n      const response = await axios.post('/payments/create', {\n        tier: planId === 'initial' ? 1 : 2\n      });\n\n      console.log('✅ Response received:', response.data);\n\n      if (response.data.success) {\n        const { orderId, amount, currency, razorpayKeyId, userDetails, testMode } = response.data.data;\n\n        // Check if this is test mode\n        if (testMode) {\n          // Test mode - simulate payment success\n          toast.info('Test mode: Simulating payment...');\n          setTimeout(async () => {\n            await verifyRazorpayPayment({\n              razorpay_order_id: orderId,\n              razorpay_payment_id: `pay_test_${Date.now()}`,\n              razorpay_signature: 'test_signature'\n            }, plan);\n          }, 2000);\n          return;\n        }\n\n        // Real Razorpay integration\n        const options = {\n          key: razorpayKeyId,\n          amount: amount * 100, // Amount in paise\n          currency: currency,\n          name: 'Invisible Assessment Tool',\n          description: plan.description,\n          order_id: orderId,\n          prefill: {\n            name: userDetails.name,\n            email: userDetails.email\n          },\n          theme: {\n            color: '#4CAF50'\n          },\n          handler: async (response) => {\n            await verifyRazorpayPayment(response, plan);\n          },\n          modal: {\n            ondismiss: () => {\n              setLoading(false);\n              toast.info('Payment cancelled');\n            }\n          }\n        };\n\n        // Open Razorpay checkout\n        const razorpay = new window.Razorpay(options);\n        razorpay.open();\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      console.error('❌ Frontend payment creation error:', error);\n      console.error('❌ Error response:', error.response);\n      console.error('❌ Error message:', error.message);\n      toast.error(error.response?.data?.message || 'Failed to create payment');\n      setLoading(false);\n    }\n  };\n\n  const verifyRazorpayPayment = async (paymentResponse, plan) => {\n    try {\n      // For test mode, create license directly\n      if (paymentResponse.razorpay_order_id.startsWith('order_test_')) {\n        console.log('🧪 Test mode - creating license directly');\n        const response = await axios.get(`/payments/create-test-license/${user.id}`);\n\n        if (response.data.success) {\n          toast.success('Test payment successful! License created.');\n          navigate('/download', {\n            state: {\n              licenseKey: response.data.data.licenseKey,\n              plan: plan\n            }\n          });\n          return;\n        }\n      }\n\n      // Real payment verification\n      const response = await axios.post('/payments/verify', {\n        razorpay_order_id: paymentResponse.razorpay_order_id,\n        razorpay_payment_id: paymentResponse.razorpay_payment_id,\n        razorpay_signature: paymentResponse.razorpay_signature\n      });\n\n      if (response.data.success) {\n        toast.success('Payment successful! License activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      console.error('❌ Payment verification error:', error);\n      toast.error(error.response?.data?.message || 'Payment verification failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 🧪 DIRECT TEST LICENSE CREATION\n  const createDirectTestLicense = async (planId) => {\n    try {\n      setLoading(true);\n\n      // 🔒 CHECK IF USER ALREADY HAS VALID LICENSE\n      if (hasActiveLicense) {\n        toast.error('You already have an active license! Please use your existing license or wait for it to expire.');\n        setLoading(false);\n        return;\n      }\n\n      const plan = plans.find(p => p.id === planId);\n\n      console.log('🧪 Creating direct test license...');\n      const response = await axios.get(`/payments/create-test-license/${user.id}`);\n\n      if (response.data.success) {\n        toast.success('Test license created successfully!');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plan\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      console.error('❌ Direct test license error:', error);\n      toast.error(error.response?.data?.message || 'Failed to create test license');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const onPayPalApprove = async (data, actions, planId) => {\n    try {\n      setLoading(true);\n\n      const response = await axios.post('/payments/execute', {\n        paymentId: data.orderID,\n        payerId: data.payerID\n      });\n\n      if (response.data.success) {\n        toast.success('Payment successful! Your license has been activated.');\n        navigate('/download', {\n          state: {\n            licenseKey: response.data.data.licenseKey,\n            plan: plans.find(p => p.id === planId)\n          }\n        });\n      } else {\n        throw new Error(response.data.message);\n      }\n    } catch (error) {\n      toast.error(error.response?.data?.message || 'Payment processing failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const onPayPalError = (error) => {\n    console.error('PayPal error:', error);\n    toast.error('Payment failed. Please try again.');\n  };\n\n  if (loading) {\n    return (\n      <PurchaseContainer>\n        <LoadingSpinner>\n          <div className=\"spinner\"></div>\n          <span style={{ marginLeft: '20px' }}>Processing payment...</span>\n        </LoadingSpinner>\n      </PurchaseContainer>\n    );\n  }\n\n  return (\n    <PurchaseContainer>\n      <PurchaseContent>\n        <PurchaseHeader>\n          <PurchaseTitle>💳 Choose Your Plan</PurchaseTitle>\n          <PurchaseSubtitle>\n            Secure your access to the Invisible Assessment Tool\n          </PurchaseSubtitle>\n        </PurchaseHeader>\n\n        <PurchaseGrid>\n          {plans.map((plan) => (\n            <PlanCard\n              key={plan.id}\n              className={plan.recommended ? 'recommended' : ''}\n            >\n              {plan.recommended && <RecommendedBadge>RECOMMENDED</RecommendedBadge>}\n\n              <PlanTitle>{plan.title}</PlanTitle>\n              <PlanPrice>{plan.price}</PlanPrice>\n              <p style={{ opacity: 0.8, marginBottom: '20px' }}>{plan.description}</p>\n\n              <PlanFeatures>\n                {plan.features.map((feature, index) => (\n                  <li key={index}>\n                    <CheckCircle size={16} />\n                    {feature}\n                  </li>\n                ))}\n              </PlanFeatures>\n\n              {plan.available ? (\n                <PaymentSection>\n                  <h4 style={{ marginBottom: '15px', display: 'flex', alignItems: 'center' }}>\n                    <CreditCard size={20} style={{ marginRight: '10px' }} />\n                    Secure Payment with Razorpay\n                  </h4>\n                  <p style={{ fontSize: '0.9rem', opacity: '0.8', marginBottom: '15px' }}>\n                    💳 Cards • 📱 UPI • 🏦 Net Banking • 💰 Wallets\n                  </p>\n\n                  <button\n                    style={{\n                      width: '100%',\n                      background: 'linear-gradient(45deg, #3395ff, #1976d2)',\n                      color: 'white',\n                      border: 'none',\n                      padding: '15px',\n                      borderRadius: '10px',\n                      fontSize: '1.1rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      gap: '10px',\n                      marginBottom: '10px'\n                    }}\n                    onClick={() => createRazorpayOrder(plan.id)}\n                    disabled={loading}\n                  >\n                    {loading ? (\n                      <>\n                        <div className=\"loading-spinner\"></div>\n                        Processing...\n                      </>\n                    ) : (\n                      <>\n                        <CreditCard size={20} />\n                        Pay {plan.price} with Razorpay\n                      </>\n                    )}\n                  </button>\n\n                  {/* 🧪 TEST BUTTON */}\n                  <button\n                    style={{\n                      width: '100%',\n                      background: 'linear-gradient(45deg, #4CAF50, #45a049)',\n                      color: 'white',\n                      border: 'none',\n                      padding: '10px',\n                      borderRadius: '5px',\n                      fontSize: '0.9rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.3s ease'\n                    }}\n                    onClick={() => createDirectTestLicense(plan.id)}\n                    disabled={loading}\n                  >\n                    🧪 Create Test License (Skip Payment)\n                  </button>\n                </PaymentSection>\n              ) : (\n                <div style={{\n                  padding: '20px',\n                  background: 'rgba(255, 255, 255, 0.1)',\n                  borderRadius: '10px',\n                  textAlign: 'center',\n                  opacity: 0.7\n                }}>\n                  <Shield size={24} style={{ marginBottom: '10px' }} />\n                  <p>Not available for your account type</p>\n                </div>\n              )}\n            </PlanCard>\n          ))}\n        </PurchaseGrid>\n      </PurchaseContent>\n    </PurchaseContainer>\n  );\n};\n\nexport default Purchase;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,mBAAmB;AACtC,OAAOC,KAAK,MAAM,iBAAiB;AACnC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SAASC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEJ,UAAU,IAAIK,QAAQ,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE7G,MAAMC,iBAAiB,GAAGd,MAAM,CAACe,GAAG;AACpC;AACA;AACA;AACA;AACA,CAAC;AAACC,EAAA,GALIF,iBAAiB;AAOvB,MAAMG,eAAe,GAAGjB,MAAM,CAACe,GAAG;AAClC;AACA;AACA,CAAC;AAACG,GAAA,GAHID,eAAe;AAKrB,MAAME,cAAc,GAAGnB,MAAM,CAACe,GAAG;AACjC;AACA;AACA,CAAC;AAACK,GAAA,GAHID,cAAc;AAKpB,MAAME,aAAa,GAAGrB,MAAM,CAACsB,EAAE;AAC/B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,aAAa;AAMnB,MAAMG,gBAAgB,GAAGxB,MAAM,CAACyB,CAAC;AACjC;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GALIF,gBAAgB;AAOtB,MAAMG,YAAY,GAAG3B,MAAM,CAACe,GAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACa,GAAA,GARID,YAAY;AAUlB,MAAME,QAAQ,GAAG7B,MAAM,CAACe,GAAG;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACe,GAAA,GAXID,QAAQ;AAad,MAAME,gBAAgB,GAAG/B,MAAM,CAACe,GAAG;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACiB,GAAA,GAXID,gBAAgB;AAatB,MAAME,SAAS,GAAGjC,MAAM,CAACkC,EAAE;AAC3B;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAJIF,SAAS;AAMf,MAAMG,SAAS,GAAGpC,MAAM,CAACe,GAAG;AAC5B;AACA;AACA;AACA,CAAC;AAACsB,GAAA,GAJID,SAAS;AAMf,MAAME,YAAY,GAAGtC,MAAM,CAACuC,EAAE;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAACC,GAAA,GAfIF,YAAY;AAiBlB,MAAMG,cAAc,GAAGzC,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA,CAAC;AAAC2B,IAAA,GALID,cAAc;AAOpB,MAAME,cAAc,GAAG3C,MAAM,CAACe,GAAG;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAAC6B,IAAA,GAnBID,cAAc;AAqBpB,MAAME,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM;IAAEC;EAAK,CAAC,GAAG5C,OAAO,CAAC,CAAC;EAC1B,MAAM6C,QAAQ,GAAGjD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACkD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACdyD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,uBAAuB,CAAC;MACzD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBL,eAAe,CAACE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACE,QAAQ,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAClD;EACF,CAAC;EAED,MAAME,gBAAgB,GAAGV,YAAY,CAACW,IAAI,CAACC,OAAO,IAAIA,OAAO,CAACC,OAAO,CAAC;EACtE,MAAMC,eAAe,GAAG,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,cAAc,MAAK,CAAC;EAElD,MAAMC,KAAK,GAAG,CACZ;IACEC,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,KAAK;IACZC,MAAM,EAAE,EAAE;IACVC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,6CAA6C;IAC1DC,QAAQ,EAAE,CACR,0BAA0B,EAC1B,6BAA6B,EAC7B,8BAA8B,EAC9B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,0BAA0B,EAC1B,kBAAkB,CACnB;IACDC,WAAW,EAAEV,eAAe;IAC5BW,SAAS,EAAEX;EACb,CAAC,EACD;IACEG,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,CAAC;IACTC,WAAW,EAAE,EAAE;IACfC,WAAW,EAAE,4CAA4C;IACzDC,QAAQ,EAAE,CACR,0BAA0B,EAC1B,6BAA6B,EAC7B,8BAA8B,EAC9B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,kBAAkB,EAClB,iBAAiB,CAClB;IACDC,WAAW,EAAE,CAACV,eAAe;IAC7BW,SAAS,EAAE,CAACX,eAAe,IAAIJ;EACjC,CAAC,CACF;EAED,MAAMgB,mBAAmB,GAAG,MAAOC,MAAM,IAAK;IAC5C,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAIW,gBAAgB,EAAE;QACpB9D,KAAK,CAAC4D,KAAK,CAAC,gGAAgG,CAAC;QAC7GT,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAM6B,IAAI,GAAGZ,KAAK,CAACa,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC6C,EAAE,KAAKU,MAAM,CAAC;MAE7ClB,OAAO,CAACqB,GAAG,CAAC,sCAAsC,EAAEH,MAAM,CAAC;MAC3DlB,OAAO,CAACqB,GAAG,CAAC,oBAAoB,EAAEjF,KAAK,CAACkF,QAAQ,CAACC,OAAO,CAAC;MACzDvB,OAAO,CAACqB,GAAG,CAAC,iBAAiB,EAAEjF,KAAK,CAACkF,QAAQ,CAACE,OAAO,CAACC,MAAM,CAAC,eAAe,CAAC,CAAC;;MAE9E;MACA,MAAM/B,QAAQ,GAAG,MAAMtD,KAAK,CAACsF,IAAI,CAAC,kBAAkB,EAAE;QACpDC,IAAI,EAAET,MAAM,KAAK,SAAS,GAAG,CAAC,GAAG;MACnC,CAAC,CAAC;MAEFlB,OAAO,CAACqB,GAAG,CAAC,sBAAsB,EAAE3B,QAAQ,CAACE,IAAI,CAAC;MAElD,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB,MAAM;UAAE+B,OAAO;UAAEjB,MAAM;UAAEkB,QAAQ;UAAEC,aAAa;UAAEC,WAAW;UAAEC;QAAS,CAAC,GAAGtC,QAAQ,CAACE,IAAI,CAACA,IAAI;;QAE9F;QACA,IAAIoC,QAAQ,EAAE;UACZ;UACA7F,KAAK,CAAC8F,IAAI,CAAC,kCAAkC,CAAC;UAC9CC,UAAU,CAAC,YAAY;YACrB,MAAMC,qBAAqB,CAAC;cAC1BC,iBAAiB,EAAER,OAAO;cAC1BS,mBAAmB,EAAE,YAAYC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;cAC7CC,kBAAkB,EAAE;YACtB,CAAC,EAAErB,IAAI,CAAC;UACV,CAAC,EAAE,IAAI,CAAC;UACR;QACF;;QAEA;QACA,MAAMsB,OAAO,GAAG;UACdC,GAAG,EAAEZ,aAAa;UAClBnB,MAAM,EAAEA,MAAM,GAAG,GAAG;UAAE;UACtBkB,QAAQ,EAAEA,QAAQ;UAClBc,IAAI,EAAE,2BAA2B;UACjC9B,WAAW,EAAEM,IAAI,CAACN,WAAW;UAC7B+B,QAAQ,EAAEhB,OAAO;UACjBiB,OAAO,EAAE;YACPF,IAAI,EAAEZ,WAAW,CAACY,IAAI;YACtBG,KAAK,EAAEf,WAAW,CAACe;UACrB,CAAC;UACDC,KAAK,EAAE;YACLC,KAAK,EAAE;UACT,CAAC;UACDC,OAAO,EAAE,MAAOvD,QAAQ,IAAK;YAC3B,MAAMyC,qBAAqB,CAACzC,QAAQ,EAAEyB,IAAI,CAAC;UAC7C,CAAC;UACD+B,KAAK,EAAE;YACLC,SAAS,EAAEA,CAAA,KAAM;cACf7D,UAAU,CAAC,KAAK,CAAC;cACjBnD,KAAK,CAAC8F,IAAI,CAAC,mBAAmB,CAAC;YACjC;UACF;QACF,CAAC;;QAED;QACA,MAAMmB,QAAQ,GAAG,IAAIC,MAAM,CAACC,QAAQ,CAACb,OAAO,CAAC;QAC7CW,QAAQ,CAACG,IAAI,CAAC,CAAC;MACjB,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAAC9D,QAAQ,CAACE,IAAI,CAAC6D,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MAAA,IAAA2D,eAAA,EAAAC,oBAAA;MACd3D,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1DC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAACL,QAAQ,CAAC;MAClDM,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC0D,OAAO,CAAC;MAChDtH,KAAK,CAAC4D,KAAK,CAAC,EAAA2D,eAAA,GAAA3D,KAAK,CAACL,QAAQ,cAAAgE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB9D,IAAI,cAAA+D,oBAAA,uBAApBA,oBAAA,CAAsBF,OAAO,KAAI,0BAA0B,CAAC;MACxEnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6C,qBAAqB,GAAG,MAAAA,CAAOyB,eAAe,EAAEzC,IAAI,KAAK;IAC7D,IAAI;MACF;MACA,IAAIyC,eAAe,CAACxB,iBAAiB,CAACyB,UAAU,CAAC,aAAa,CAAC,EAAE;QAC/D7D,OAAO,CAACqB,GAAG,CAAC,0CAA0C,CAAC;QACvD,MAAM3B,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,iCAAiCV,IAAI,CAACuB,EAAE,EAAE,CAAC;QAE5E,IAAId,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzB1D,KAAK,CAAC0D,OAAO,CAAC,2CAA2C,CAAC;UAC1DX,QAAQ,CAAC,WAAW,EAAE;YACpB4E,KAAK,EAAE;cACLC,UAAU,EAAErE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmE,UAAU;cACzC5C,IAAI,EAAEA;YACR;UACF,CAAC,CAAC;UACF;QACF;MACF;;MAEA;MACA,MAAMzB,QAAQ,GAAG,MAAMtD,KAAK,CAACsF,IAAI,CAAC,kBAAkB,EAAE;QACpDU,iBAAiB,EAAEwB,eAAe,CAACxB,iBAAiB;QACpDC,mBAAmB,EAAEuB,eAAe,CAACvB,mBAAmB;QACxDG,kBAAkB,EAAEoB,eAAe,CAACpB;MACtC,CAAC,CAAC;MAEF,IAAI9C,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1D,KAAK,CAAC0D,OAAO,CAAC,wCAAwC,CAAC;QACvDX,QAAQ,CAAC,WAAW,EAAE;UACpB4E,KAAK,EAAE;YACLC,UAAU,EAAErE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmE,UAAU;YACzC5C,IAAI,EAAEA;UACR;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIqC,KAAK,CAAC9D,QAAQ,CAACE,IAAI,CAAC6D,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MAAA,IAAAiE,gBAAA,EAAAC,qBAAA;MACdjE,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD5D,KAAK,CAAC4D,KAAK,CAAC,EAAAiE,gBAAA,GAAAjE,KAAK,CAACL,QAAQ,cAAAsE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpE,IAAI,cAAAqE,qBAAA,uBAApBA,qBAAA,CAAsBR,OAAO,KAAI,6BAA6B,CAAC;IAC7E,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4E,uBAAuB,GAAG,MAAOhD,MAAM,IAAK;IAChD,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAIW,gBAAgB,EAAE;QACpB9D,KAAK,CAAC4D,KAAK,CAAC,gGAAgG,CAAC;QAC7GT,UAAU,CAAC,KAAK,CAAC;QACjB;MACF;MAEA,MAAM6B,IAAI,GAAGZ,KAAK,CAACa,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC6C,EAAE,KAAKU,MAAM,CAAC;MAE7ClB,OAAO,CAACqB,GAAG,CAAC,oCAAoC,CAAC;MACjD,MAAM3B,QAAQ,GAAG,MAAMtD,KAAK,CAACuD,GAAG,CAAC,iCAAiCV,IAAI,CAACuB,EAAE,EAAE,CAAC;MAE5E,IAAId,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1D,KAAK,CAAC0D,OAAO,CAAC,oCAAoC,CAAC;QACnDX,QAAQ,CAAC,WAAW,EAAE;UACpB4E,KAAK,EAAE;YACLC,UAAU,EAAErE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmE,UAAU;YACzC5C,IAAI,EAAEA;UACR;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIqC,KAAK,CAAC9D,QAAQ,CAACE,IAAI,CAAC6D,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MAAA,IAAAoE,gBAAA,EAAAC,qBAAA;MACdpE,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD5D,KAAK,CAAC4D,KAAK,CAAC,EAAAoE,gBAAA,GAAApE,KAAK,CAACL,QAAQ,cAAAyE,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvE,IAAI,cAAAwE,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAI,+BAA+B,CAAC;IAC/E,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+E,eAAe,GAAG,MAAAA,CAAOzE,IAAI,EAAE0E,OAAO,EAAEpD,MAAM,KAAK;IACvD,IAAI;MACF5B,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMI,QAAQ,GAAG,MAAMtD,KAAK,CAACsF,IAAI,CAAC,mBAAmB,EAAE;QACrD6C,SAAS,EAAE3E,IAAI,CAAC4E,OAAO;QACvBC,OAAO,EAAE7E,IAAI,CAAC8E;MAChB,CAAC,CAAC;MAEF,IAAIhF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1D,KAAK,CAAC0D,OAAO,CAAC,sDAAsD,CAAC;QACrEX,QAAQ,CAAC,WAAW,EAAE;UACpB4E,KAAK,EAAE;YACLC,UAAU,EAAErE,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACmE,UAAU;YACzC5C,IAAI,EAAEZ,KAAK,CAACa,IAAI,CAACzD,CAAC,IAAIA,CAAC,CAAC6C,EAAE,KAAKU,MAAM;UACvC;QACF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIsC,KAAK,CAAC9D,QAAQ,CAACE,IAAI,CAAC6D,OAAO,CAAC;MACxC;IACF,CAAC,CAAC,OAAO1D,KAAK,EAAE;MAAA,IAAA4E,gBAAA,EAAAC,qBAAA;MACdzI,KAAK,CAAC4D,KAAK,CAAC,EAAA4E,gBAAA,GAAA5E,KAAK,CAACL,QAAQ,cAAAiF,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/E,IAAI,cAAAgF,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,2BAA2B,CAAC;IAC3E,CAAC,SAAS;MACRnE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuF,aAAa,GAAI9E,KAAK,IAAK;IAC/BC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACrC5D,KAAK,CAAC4D,KAAK,CAAC,mCAAmC,CAAC;EAClD,CAAC;EAED,IAAIV,OAAO,EAAE;IACX,oBACExC,OAAA,CAACG,iBAAiB;MAAA8H,QAAA,eAChBjI,OAAA,CAACgC,cAAc;QAAAiG,QAAA,gBACbjI,OAAA;UAAKkI,SAAS,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC/BtI,OAAA;UAAMuI,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAExB;EAEA,oBACEtI,OAAA,CAACG,iBAAiB;IAAA8H,QAAA,eAChBjI,OAAA,CAACM,eAAe;MAAA2H,QAAA,gBACdjI,OAAA,CAACQ,cAAc;QAAAyH,QAAA,gBACbjI,OAAA,CAACU,aAAa;UAAAuH,QAAA,EAAC;QAAmB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAe,CAAC,eAClDtI,OAAA,CAACa,gBAAgB;UAAAoH,QAAA,EAAC;QAElB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAkB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEjBtI,OAAA,CAACgB,YAAY;QAAAiH,QAAA,EACVvE,KAAK,CAAC+E,GAAG,CAAEnE,IAAI,iBACdtE,OAAA,CAACkB,QAAQ;UAEPgH,SAAS,EAAE5D,IAAI,CAACJ,WAAW,GAAG,aAAa,GAAG,EAAG;UAAA+D,QAAA,GAEhD3D,IAAI,CAACJ,WAAW,iBAAIlE,OAAA,CAACoB,gBAAgB;YAAA6G,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAkB,CAAC,eAErEtI,OAAA,CAACsB,SAAS;YAAA2G,QAAA,EAAE3D,IAAI,CAACV;UAAK;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnCtI,OAAA,CAACyB,SAAS;YAAAwG,QAAA,EAAE3D,IAAI,CAACT;UAAK;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACnCtI,OAAA;YAAGuI,KAAK,EAAE;cAAEG,OAAO,EAAE,GAAG;cAAEC,YAAY,EAAE;YAAO,CAAE;YAAAV,QAAA,EAAE3D,IAAI,CAACN;UAAW;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAExEtI,OAAA,CAAC2B,YAAY;YAAAsG,QAAA,EACV3D,IAAI,CAACL,QAAQ,CAACwE,GAAG,CAAC,CAACG,OAAO,EAAEC,KAAK,kBAChC7I,OAAA;cAAAiI,QAAA,gBACEjI,OAAA,CAACJ,WAAW;gBAACkJ,IAAI,EAAE;cAAG;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACxBM,OAAO;YAAA,GAFDC,KAAK;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU,CAAC,EAEdhE,IAAI,CAACH,SAAS,gBACbnE,OAAA,CAAC8B,cAAc;YAAAmG,QAAA,gBACbjI,OAAA;cAAIuI,KAAK,EAAE;gBAAEI,YAAY,EAAE,MAAM;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE;cAAS,CAAE;cAAAf,QAAA,gBACzEjI,OAAA,CAACP,UAAU;gBAACqJ,IAAI,EAAE,EAAG;gBAACP,KAAK,EAAE;kBAAEU,WAAW,EAAE;gBAAO;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gCAE1D;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLtI,OAAA;cAAGuI,KAAK,EAAE;gBAAEW,QAAQ,EAAE,QAAQ;gBAAER,OAAO,EAAE,KAAK;gBAAEC,YAAY,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAExE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJtI,OAAA;cACEuI,KAAK,EAAE;gBACLY,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0CAA0C;gBACtDjD,KAAK,EAAE,OAAO;gBACdkD,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE,MAAM;gBACpBL,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE,KAAK;gBACjBC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE,eAAe;gBAC3BX,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBW,cAAc,EAAE,QAAQ;gBACxBC,GAAG,EAAE,MAAM;gBACXjB,YAAY,EAAE;cAChB,CAAE;cACFkB,OAAO,EAAEA,CAAA,KAAMzF,mBAAmB,CAACE,IAAI,CAACX,EAAE,CAAE;cAC5CmG,QAAQ,EAAEtH,OAAQ;cAAAyF,QAAA,EAEjBzF,OAAO,gBACNxC,OAAA,CAAAE,SAAA;gBAAA+H,QAAA,gBACEjI,OAAA;kBAAKkI,SAAS,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,iBAEzC;cAAA,eAAE,CAAC,gBAEHtI,OAAA,CAAAE,SAAA;gBAAA+H,QAAA,gBACEjI,OAAA,CAACP,UAAU;kBAACqJ,IAAI,EAAE;gBAAG;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,QACpB,EAAChE,IAAI,CAACT,KAAK,EAAC,gBAClB;cAAA,eAAE;YACH;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eAGTtI,OAAA;cACEuI,KAAK,EAAE;gBACLY,KAAK,EAAE,MAAM;gBACbC,UAAU,EAAE,0CAA0C;gBACtDjD,KAAK,EAAE,OAAO;gBACdkD,MAAM,EAAE,MAAM;gBACdC,OAAO,EAAE,MAAM;gBACfC,YAAY,EAAE,KAAK;gBACnBL,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE,KAAK;gBACjBC,MAAM,EAAE,SAAS;gBACjBC,UAAU,EAAE;cACd,CAAE;cACFG,OAAO,EAAEA,CAAA,KAAMxC,uBAAuB,CAAC/C,IAAI,CAACX,EAAE,CAAE;cAChDmG,QAAQ,EAAEtH,OAAQ;cAAAyF,QAAA,EACnB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC,gBAEjBtI,OAAA;YAAKuI,KAAK,EAAE;cACVe,OAAO,EAAE,MAAM;cACfF,UAAU,EAAE,0BAA0B;cACtCG,YAAY,EAAE,MAAM;cACpBQ,SAAS,EAAE,QAAQ;cACnBrB,OAAO,EAAE;YACX,CAAE;YAAAT,QAAA,gBACAjI,OAAA,CAACN,MAAM;cAACoJ,IAAI,EAAE,EAAG;cAACP,KAAK,EAAE;gBAAEI,YAAY,EAAE;cAAO;YAAE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDtI,OAAA;cAAAiI,QAAA,EAAG;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CACN;QAAA,GA7FIhE,IAAI,CAACX,EAAE;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8FJ,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAExB,CAAC;AAACnG,EAAA,CAnYID,QAAQ;EAAA,QACK1C,OAAO,EACPJ,WAAW;AAAA;AAAA4K,IAAA,GAFxB9H,QAAQ;AAqYd,eAAeA,QAAQ;AAAC,IAAA7B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAG,GAAA,EAAAE,IAAA,EAAAE,IAAA,EAAA+H,IAAA;AAAAC,YAAA,CAAA5J,EAAA;AAAA4J,YAAA,CAAA1J,GAAA;AAAA0J,YAAA,CAAAxJ,GAAA;AAAAwJ,YAAA,CAAArJ,GAAA;AAAAqJ,YAAA,CAAAlJ,GAAA;AAAAkJ,YAAA,CAAAhJ,GAAA;AAAAgJ,YAAA,CAAA9I,GAAA;AAAA8I,YAAA,CAAA5I,GAAA;AAAA4I,YAAA,CAAAzI,GAAA;AAAAyI,YAAA,CAAAvI,GAAA;AAAAuI,YAAA,CAAApI,GAAA;AAAAoI,YAAA,CAAAlI,IAAA;AAAAkI,YAAA,CAAAhI,IAAA;AAAAgI,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}