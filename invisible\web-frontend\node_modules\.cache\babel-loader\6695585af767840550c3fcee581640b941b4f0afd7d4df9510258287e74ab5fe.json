{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FolderArchive = createLucideIcon(\"FolderArchive\", [[\"path\", {\n  d: \"M22 20V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2h6\",\n  key: \"1l0vpk\"\n}], [\"circle\", {\n  cx: \"16\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"1uwppb\"\n}], [\"path\", {\n  d: \"M16 11v-1\",\n  key: \"eoyjtm\"\n}], [\"path\", {\n  d: \"M16 17v-2\",\n  key: \"1xp69b\"\n}]]);\nexport { FolderArchive as default };", "map": {"version": 3, "names": ["FolderArchive", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\folder-archive.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FolderArchive\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjIgMjBWOGEyIDIgMCAwIDAtMi0yaC03LjkzYTIgMiAwIDAgMS0xLjY2LS45bC0uODItMS4yQTIgMiAwIDAgMCA3LjkzIDNINGEyIDIgMCAwIDAtMiAydjEzYzAgMS4xLjkgMiAyIDJoNiIgLz4KICA8Y2lyY2xlIGN4PSIxNiIgY3k9IjE5IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik0xNiAxMXYtMSIgLz4KICA8cGF0aCBkPSJNMTYgMTd2LTIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/folder-archive\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FolderArchive = createLucideIcon('FolderArchive', [\n  [\n    'path',\n    {\n      d: 'M22 20V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2h6',\n      key: '1l0vpk',\n    },\n  ],\n  ['circle', { cx: '16', cy: '19', r: '2', key: '1uwppb' }],\n  ['path', { d: 'M16 11v-1', key: 'eoyjtm' }],\n  ['path', { d: 'M16 17v-2', key: '1xp69b' }],\n]);\n\nexport default FolderArchive;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}