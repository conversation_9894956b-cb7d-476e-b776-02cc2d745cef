{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst CloudCog = createLucideIcon(\"CloudCog\", [[\"path\", {\n  d: \"M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9\",\n  key: \"19hoja\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"1spfwm\"\n}], [\"path\", {\n  d: \"M12 13v1\",\n  key: \"176q98\"\n}], [\"path\", {\n  d: \"M12 20v1\",\n  key: \"1wcdkc\"\n}], [\"path\", {\n  d: \"M16 17h-1\",\n  key: \"y560le\"\n}], [\"path\", {\n  d: \"M9 17H8\",\n  key: \"1lfe9z\"\n}], [\"path\", {\n  d: \"m15 14-.88.88\",\n  key: \"12ytk1\"\n}], [\"path\", {\n  d: \"M9.88 19.12 9 20\",\n  key: \"1kmb4r\"\n}], [\"path\", {\n  d: \"m15 20-.88-.88\",\n  key: \"1ipjcf\"\n}], [\"path\", {\n  d: \"M9.88 14.88 9 14\",\n  key: \"c4uok7\"\n}]]);\nexport { CloudCog as default };", "map": {"version": 3, "names": ["CloudCog", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\cloud-cog.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name CloudCog\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTYuMkE0LjUgNC41IDAgMCAwIDE3LjUgOGgtMS44QTcgNyAwIDEgMCA0IDE0LjkiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxNyIgcj0iMyIgLz4KICA8cGF0aCBkPSJNMTIgMTN2MSIgLz4KICA8cGF0aCBkPSJNMTIgMjB2MSIgLz4KICA8cGF0aCBkPSJNMTYgMTdoLTEiIC8+CiAgPHBhdGggZD0iTTkgMTdIOCIgLz4KICA8cGF0aCBkPSJtMTUgMTQtLjg4Ljg4IiAvPgogIDxwYXRoIGQ9Ik05Ljg4IDE5LjEyIDkgMjAiIC8+CiAgPHBhdGggZD0ibTE1IDIwLS44OC0uODgiIC8+CiAgPHBhdGggZD0iTTkuODggMTQuODggOSAxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/cloud-cog\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst CloudCog = createLucideIcon('CloudCog', [\n  [\n    'path',\n    { d: 'M20 16.2A4.5 4.5 0 0 0 17.5 8h-1.8A7 7 0 1 0 4 14.9', key: '19hoja' },\n  ],\n  ['circle', { cx: '12', cy: '17', r: '3', key: '1spfwm' }],\n  ['path', { d: 'M12 13v1', key: '176q98' }],\n  ['path', { d: 'M12 20v1', key: '1wcdkc' }],\n  ['path', { d: 'M16 17h-1', key: 'y560le' }],\n  ['path', { d: 'M9 17H8', key: '1lfe9z' }],\n  ['path', { d: 'm15 14-.88.88', key: '12ytk1' }],\n  ['path', { d: 'M9.88 19.12 9 20', key: '1kmb4r' }],\n  ['path', { d: 'm15 20-.88-.88', key: '1ipjcf' }],\n  ['path', { d: 'M9.88 14.88 9 14', key: 'c4uok7' }],\n]);\n\nexport default CloudCog;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EAAEC,CAAA,EAAG,qDAAuD;EAAAC,GAAA,EAAK;AAAS,EAC5E,EACA,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,eAAiB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC/C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAClD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}