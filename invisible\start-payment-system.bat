@echo off
echo 🚀 STARTING COMPLETE PAYMENT SYSTEM
echo ===================================
echo.

echo 📋 SYSTEM STATUS:
echo ✅ Razorpay credentials configured
echo ✅ Image loading issue fixed
echo ✅ Download system ready
echo.

echo 🔧 STEP 1: Starting License Server (Backend)...
cd license-server
start "License Server" cmd /k "node server.js"
timeout /t 3 /nobreak >nul

echo 🌐 STEP 2: Starting Frontend...
cd ..\web-frontend
start "Frontend" cmd /k "npm start"
timeout /t 3 /nobreak >nul

echo.
echo 🎉 PAYMENT SYSTEM STARTED!
echo.
echo 📋 WHAT'S RUNNING:
echo   • License Server: http://localhost:5002
echo   • Frontend: http://localhost:3000
echo   • Razorpay: ✅ Working (Test Mode)
echo   • Download System: ✅ Ready
echo.
echo 💰 READY TO MAKE MONEY!
echo   1. Go to http://localhost:3000
echo   2. Register/Login
echo   3. Purchase a plan (₹10 or ₹5)
echo   4. Download the software
echo   5. Test - no more image errors!
echo.
pause
