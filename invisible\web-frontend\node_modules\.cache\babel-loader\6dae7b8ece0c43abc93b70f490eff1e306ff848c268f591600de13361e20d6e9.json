{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ChevronRightCircle = createLucideIcon(\"ChevronRightCircle\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"10\",\n  key: \"1mglay\"\n}], [\"path\", {\n  d: \"m10 8 4 4-4 4\",\n  key: \"1wy4r4\"\n}]]);\nexport { ChevronRightCircle as default };", "map": {"version": 3, "names": ["ChevronRightCircle", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\chevron-right-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ChevronRightCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJtMTAgOCA0IDQtNCA0IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRightCircle = createLucideIcon('ChevronRightCircle', [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'm10 8 4 4-4 4', key: '1wy4r4' }],\n]);\n\nexport default ChevronRightCircle;\n"], "mappings": ";;;;;AAaM,MAAAA,kBAAA,GAAqBC,gBAAA,CAAiB,oBAAsB,GAChE,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAMC,GAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}