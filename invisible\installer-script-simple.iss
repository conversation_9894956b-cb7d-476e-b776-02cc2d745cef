; 🚀 INVISIBLE ASSESSMENT TOOL - SIMPLE INSTALLER
; Minimal working version for INNO Setup

[Setup]
; 📋 APPLICATION INFO
AppName=Invisible Assessment Tool
AppVersion=1.0.0
AppPublisher=Your Company Name
AppId={{B8E6D1A2-F4C3-4D5E-8F9A-1B2C3D4E5F6G}}

; 📁 INSTALLATION DIRECTORIES
DefaultDirName={autopf}\InvisibleAssessmentTool
DefaultGroupName=Invisible Assessment Tool
AllowNoIcons=yes

; 🎨 INSTALLER APPEARANCE
OutputDir=installer-output
OutputBaseFilename=InvisibleAssessmentTool-Setup-v1.0.0

; 🔧 INSTALLER SETTINGS
Compression=lzma2/ultra64
SolidCompression=yes
PrivilegesRequired=admin
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
; 📦 MAIN APPLICATION FILES
Source: "portable-app\InvisibleAssessmentTool.exe"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
; 🖥️ DESKTOP AND MENU SHORTCUTS
Name: "{group}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"
Name: "{group}\Uninstall Invisible Assessment Tool"; Filename: "{uninstallexe}"
Name: "{autodesktop}\Invisible Assessment Tool"; Filename: "{app}\InvisibleAssessmentTool.exe"; Tasks: desktopicon

[Registry]
; 🔧 WINDOWS REGISTRY ENTRIES
Root: HKCU; Subkey: "Software\InvisibleAssessmentTool"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKCU; Subkey: "Software\InvisibleAssessmentTool"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"

[Run]
; 🎯 POST-INSTALLATION ACTIONS
Filename: "{app}\InvisibleAssessmentTool.exe"; Description: "{cm:LaunchProgram,Invisible Assessment Tool}"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; 🧹 CLEANUP ON UNINSTALL
Type: filesandordirs; Name: "{app}\screenshots"
Type: filesandordirs; Name: "{app}\temp"
Type: filesandordirs; Name: "{app}\cache"
Type: filesandordirs; Name: "{app}\logs"

[Code]
// 🔑 LICENSE KEY INPUT
var
  LicenseKeyPage: TInputQueryWizardPage;

procedure InitializeWizard;
begin
  // Create license key input page
  LicenseKeyPage := CreateInputQueryPage(wpSelectTasks,
    'License Activation', 'Enter your license key',
    'Please enter the license key you received after purchase:');
  LicenseKeyPage.Add('License Key:', False);
end;

function NextButtonClick(CurPageID: Integer): Boolean;
var
  LicenseKey: String;
  LicenseFile: String;
begin
  Result := True;
  
  if CurPageID = LicenseKeyPage.ID then
  begin
    LicenseKey := LicenseKeyPage.Values[0];
    
    // Validate license key format
    if Length(LicenseKey) < 10 then
    begin
      MsgBox('Please enter a valid license key.', mbError, MB_OK);
      Result := False;
    end
    else
    begin
      // Save license key to file
      LicenseFile := ExpandConstant('{app}\license.key');
      SaveStringToFile(LicenseFile, LicenseKey, False);
    end;
  end;
end;

// 🎨 CUSTOM INSTALLER MESSAGES
procedure CurStepChanged(CurStep: TSetupStep);
begin
  if CurStep = ssPostInstall then
  begin
    MsgBox('Installation completed successfully!' + #13#10 + 
           'Your Invisible Assessment Tool is ready to use.' + #13#10 + #13#10 +
           'Quick Start:' + #13#10 +
           '• Press Ctrl+B to show/hide interface' + #13#10 +
           '• Press Ctrl+H to take screenshots' + #13#10 +
           '• Press Ctrl+Enter to analyze with AI' + #13#10 + #13#10 +
           'The software runs in complete stealth mode!', 
           mbInformation, MB_OK);
  end;
end;
