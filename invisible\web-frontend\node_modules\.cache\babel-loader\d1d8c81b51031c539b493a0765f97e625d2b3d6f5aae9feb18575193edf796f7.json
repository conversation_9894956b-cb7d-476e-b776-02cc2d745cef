{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Mails = createLucideIcon(\"Mails\", [[\"rect\", {\n  width: \"16\",\n  height: \"13\",\n  x: \"6\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"1drq3f\"\n}], [\"path\", {\n  d: \"m22 7-7.1 3.78c-.57.3-1.23.3-1.8 0L6 7\",\n  key: \"xn252p\"\n}], [\"path\", {\n  d: \"M2 8v11c0 1.1.9 2 2 2h14\",\n  key: \"n13cji\"\n}]]);\nexport { Mails as default };", "map": {"version": 3, "names": ["Mails", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\mails.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Mails\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMTMiIHg9IjYiIHk9IjQiIHJ4PSIyIiAvPgogIDxwYXRoIGQ9Im0yMiA3LTcuMSAzLjc4Yy0uNTcuMy0xLjIzLjMtMS44IDBMNiA3IiAvPgogIDxwYXRoIGQ9Ik0yIDh2MTFjMCAxLjEuOSAyIDIgMmgxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/mails\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Mails = createLucideIcon('Mails', [\n  [\n    'rect',\n    { width: '16', height: '13', x: '6', y: '4', rx: '2', key: '1drq3f' },\n  ],\n  ['path', { d: 'm22 7-7.1 3.78c-.57.3-1.23.3-1.8 0L6 7', key: 'xn252p' }],\n  ['path', { d: 'M2 8v11c0 1.1.9 2 2 2h14', key: 'n13cji' }],\n]);\n\nexport default Mails;\n"], "mappings": ";;;;;AAaM,MAAAA,KAAA,GAAQC,gBAAA,CAAiB,OAAS,GACtC,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,IAAM;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACtE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,wCAA0C;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvE,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}