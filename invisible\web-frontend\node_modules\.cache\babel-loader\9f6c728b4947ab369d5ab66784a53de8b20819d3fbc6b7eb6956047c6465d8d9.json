{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ArchiveRestore = createLucideIcon(\"ArchiveRestore\", [[\"rect\", {\n  width: \"20\",\n  height: \"5\",\n  x: \"2\",\n  y: \"4\",\n  rx: \"2\",\n  key: \"uhwcea\"\n}], [\"path\", {\n  d: \"M12 13v7\",\n  key: \"1arz7h\"\n}], [\"path\", {\n  d: \"m9 16 3-3 3 3\",\n  key: \"1idcnm\"\n}], [\"path\", {\n  d: \"M4 9v9a2 2 0 0 0 2 2h2\",\n  key: \"qxnby6\"\n}], [\"path\", {\n  d: \"M20 9v9a2 2 0 0 1-2 2h-2\",\n  key: \"gz3jmx\"\n}]]);\nexport { ArchiveRestore as default };", "map": {"version": 3, "names": ["ArchiveRestore", "createLucideIcon", "width", "height", "x", "y", "rx", "key", "d"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\archive-restore.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ArchiveRestore\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMjAiIGhlaWdodD0iNSIgeD0iMiIgeT0iNCIgcng9IjIiIC8+CiAgPHBhdGggZD0iTTEyIDEzdjciIC8+CiAgPHBhdGggZD0ibTkgMTYgMy0zIDMgMyIgLz4KICA8cGF0aCBkPSJNNCA5djlhMiAyIDAgMCAwIDIgMmgyIiAvPgogIDxwYXRoIGQ9Ik0yMCA5djlhMiAyIDAgMCAxLTIgMmgtMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/archive-restore\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ArchiveRestore = createLucideIcon('ArchiveRestore', [\n  [\n    'rect',\n    { width: '20', height: '5', x: '2', y: '4', rx: '2', key: 'uhwcea' },\n  ],\n  ['path', { d: 'M12 13v7', key: '1arz7h' }],\n  ['path', { d: 'm9 16 3-3 3 3', key: '1idcnm' }],\n  ['path', { d: 'M4 9v9a2 2 0 0 0 2 2h2', key: 'qxnby6' }],\n  ['path', { d: 'M20 9v9a2 2 0 0 1-2 2h-2', key: 'gz3jmx' }],\n]);\n\nexport default ArchiveRestore;\n"], "mappings": ";;;;;AAaM,MAAAA,cAAA,GAAiBC,gBAAA,CAAiB,gBAAkB,GACxD,CACE,QACA;EAAEC,KAAA,EAAO,IAAM;EAAAC,MAAA,EAAQ,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,CAAA,EAAG,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAC,GAAA,EAAK;AAAS,EACrE,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,UAAY;EAAAD,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAEC,CAAA,EAAG,eAAiB;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,wBAA0B;EAAAD,GAAA,EAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,0BAA4B;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}