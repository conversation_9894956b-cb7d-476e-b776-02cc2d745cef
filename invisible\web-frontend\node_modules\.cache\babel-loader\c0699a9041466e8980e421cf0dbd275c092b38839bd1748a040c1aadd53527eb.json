{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst FileLineChart = createLucideIcon(\"FileLineChart\", [[\"path\", {\n  d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\",\n  key: \"1nnpy2\"\n}], [\"polyline\", {\n  points: \"14 2 14 8 20 8\",\n  key: \"1ew0cm\"\n}], [\"path\", {\n  d: \"m16 13-3.5 3.5-2-2L8 17\",\n  key: \"zz7yod\"\n}]]);\nexport { FileLineChart as default };", "map": {"version": 3, "names": ["FileLineChart", "createLucideIcon", "d", "key", "points"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\file-line-chart.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name FileLineChart\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSAySDZhMiAyIDAgMCAwLTIgMnYxNmEyIDIgMCAwIDAgMiAyaDEyYTIgMiAwIDAgMCAyLTJWNy41TDE0LjUgMnoiIC8+CiAgPHBvbHlsaW5lIHBvaW50cz0iMTQgMiAxNCA4IDIwIDgiIC8+CiAgPHBhdGggZD0ibTE2IDEzLTMuNSAzLjUtMi0yTDggMTciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-line-chart\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileLineChart = createLucideIcon('FileLineChart', [\n  [\n    'path',\n    {\n      d: 'M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z',\n      key: '1nnpy2',\n    },\n  ],\n  ['polyline', { points: '14 2 14 8 20 8', key: '1ew0cm' }],\n  ['path', { d: 'm16 13-3.5 3.5-2-2L8 17', key: 'zz7yod' }],\n]);\n\nexport default FileLineChart;\n"], "mappings": ";;;;;AAaM,MAAAA,aAAA,GAAgBC,gBAAA,CAAiB,eAAiB,GACtD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,UAAY;EAAEC,MAAA,EAAQ,gBAAkB;EAAAD,GAAA,EAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAED,CAAA,EAAG,yBAA2B;EAAAC,GAAA,EAAK;AAAA,CAAU,EACzD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}