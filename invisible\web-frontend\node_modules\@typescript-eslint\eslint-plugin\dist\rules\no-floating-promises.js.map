{"version": 3, "file": "no-floating-promises.js", "sourceRoot": "", "sources": ["../../src/rules/no-floating-promises.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAChC,kCAA6C;AAe7C,kBAAe,IAAI,CAAC,UAAU,CAAqB;IACjD,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,6DAA6D;YAC/D,WAAW,EAAE,OAAO;YACpB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,QAAQ,EACN,4GAA4G;YAC9G,gBAAgB,EAAE,qBAAqB;YACvC,YAAY,EACV,wGAAwG;gBACxG,+DAA+D;YACjE,eAAe,EAAE,8BAA8B;SAChD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,UAAU,EAAE;wBACV,WAAW,EAAE,uCAAuC;wBACpD,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EACT,6EAA6E;wBAC/E,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,UAAU,EAAE,IAAI;YAChB,UAAU,EAAE,KAAK;SAClB;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,IAAI,OAAO,CAAC,UAAU,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;oBAC3C,OAAO;iBACR;gBAED,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;gBAEjC,IAAI,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE;oBACtD,UAAU,GAAG,UAAU,CAAC,UAAU,CAAC;iBACpC;gBAED,IAAI,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;oBAC3C,IAAI,OAAO,CAAC,UAAU,EAAE;wBACtB,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,cAAc;4BACzB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,iBAAiB;oCAC5B,GAAG,CAAC,KAAK;wCACP,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,IAAI,CAAC,UAAU,CAChB,CAAC;wCACF,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;4CACvC,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;yCAC9C;6CAAM;4CACL,OAAO;gDACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC;gDACtC,KAAK,CAAC,oBAAoB,CACxB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC1C,GAAG,CACJ;6CACF,CAAC;yCACH;oCACH,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;qBACJ;yBAAM;wBACL,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,UAAU;4BACrB,OAAO,EAAE;gCACP;oCACE,SAAS,EAAE,kBAAkB;oCAC7B,GAAG,CAAC,KAAK;wCACP,IACE,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;4CAClD,UAAU,CAAC,QAAQ,KAAK,MAAM,EAC9B;4CACA,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAC9C,OAAO,CACR,CAAC;yCACH;wCACD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CACrD,IAAI,CAAC,UAAU,CAChB,CAAC;wCACF,IAAI,2BAA2B,CAAC,MAAM,CAAC,EAAE;4CACvC,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;yCAC/C;6CAAM;4CACL,OAAO;gDACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;gDACvC,KAAK,CAAC,oBAAoB,CACxB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC1C,GAAG,CACJ;6CACF,CAAC;yCACH;oCACH,CAAC;iCACF;6BACF;yBACF,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;QAEF,SAAS,2BAA2B,CAAC,IAAa;YAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC/C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;gBACzB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACvE,OAAO,cAAc,GAAG,yBAAkB,CAAC,KAAK,CAAC;QACnD,CAAC;QAED,SAAS,WAAW,CAAC,IAAkC;YACrD,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAC1D,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CACL,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBACtD,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI;oBAC1B,sBAAc,CAAC,uBAAuB;oBACtC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CACrE,CAAC;QACJ,CAAC;QAED,SAAS,kBAAkB,CACzB,OAAuB,EACvB,IAAmB;YAEnB,yEAAyE;YACzE,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAAE;gBACnD,uEAAuE;gBACvE,yEAAyE;gBACzE,yBAAyB;gBACzB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;aACzE;YAED,IACE,CAAC,OAAO,CAAC,UAAU;gBACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,QAAQ,KAAK,MAAM,EACxB;gBACA,yEAAyE;gBACzE,4EAA4E;gBAC5E,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACnD;YAED,4EAA4E;YAC5E,IACE,CAAC,aAAa,CAAC,OAAO,EAAE,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,EACvE;gBACA,OAAO,KAAK,CAAC;aACd;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE;gBAC/C,sEAAsE;gBACtE,uCAAuC;gBACvC,OAAO,CACL,CAAC,6BAA6B,CAAC,IAAI,CAAC;oBACpC,CAAC,qCAAqC,CAAC,IAAI,CAAC;oBAC5C,CAAC,+BAA+B,CAAC,IAAI,CAAC,CACvC,CAAC;aACH;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE;gBAC7D,4EAA4E;gBAC5E,gCAAgC;gBAChC,OAAO,CACL,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;oBAC3C,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAC7C,CAAC;aACH;iBAAM,IACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBACvC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAC1C;gBACA,2EAA2E;gBAC3E,2EAA2E;gBAC3E,qDAAqD;gBACrD,OAAO,IAAI,CAAC;aACb;iBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE;gBACzD,OAAO,CACL,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;oBACtC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CACxC,CAAC;aACH;YAED,4EAA4E;YAC5E,8EAA8E;YAC9E,sBAAsB;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,6EAA6E;AAC7E,0EAA0E;AAC1E,EAAE;AACF,0GAA0G;AAC1G,SAAS,aAAa,CAAC,OAAuB,EAAE,IAAa;IAC3D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,KAAK,MAAM,EAAE,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE;QACtE,MAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,SAAS;SACV;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/D,IACE,oBAAoB,CAClB,QAAQ,EACR,SAAS,CAAC,EAAE,CACV,SAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;YAChC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACvD,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAC1D,EACD;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAC3B,IAAa,EACb,OAA6C;IAE7C,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,OAAuB,EACvB,KAAgB,EAChB,IAAa;IAEb,MAAM,IAAI,GAAwB,OAAO,CAAC,eAAe,CACvD,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAC/C,CAAC;IACF,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,6BAA6B,CACpC,UAAmC;IAEnC,OAAO,CACL,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO;QAC3C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAS,qCAAqC,CAC5C,UAAmC;IAEnC,OAAO,CACL,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,MAAM;QAC1C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAS,+BAA+B,CACtC,UAAmC;IAEnC,OAAO,CACL,UAAU,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC1D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;QAC7D,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS;QAC7C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC"}