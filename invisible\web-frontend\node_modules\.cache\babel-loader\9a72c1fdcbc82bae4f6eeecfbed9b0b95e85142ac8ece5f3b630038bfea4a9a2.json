{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst MoonStar = createLucideIcon(\"MoonStar\", [[\"path\", {\n  d: \"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z\",\n  key: \"a7tn18\"\n}], [\"path\", {\n  d: \"M19 3v4\",\n  key: \"vgv24u\"\n}], [\"path\", {\n  d: \"M21 5h-4\",\n  key: \"1wcg1f\"\n}]]);\nexport { MoonStar as default };", "map": {"version": 3, "names": ["MoonStar", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\moon-star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name MoonStar\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM2E2IDYgMCAwIDAgOSA5IDkgOSAwIDEgMS05LTlaIiAvPgogIDxwYXRoIGQ9Ik0xOSAzdjQiIC8+CiAgPHBhdGggZD0iTTIxIDVoLTQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/moon-star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MoonStar = createLucideIcon('MoonStar', [\n  ['path', { d: 'M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z', key: 'a7tn18' }],\n  ['path', { d: 'M19 3v4', key: 'vgv24u' }],\n  ['path', { d: 'M21 5h-4', key: '1wcg1f' }],\n]);\n\nexport default MoonStar;\n"], "mappings": ";;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,oCAAsC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnE,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}