import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import styled from 'styled-components';
import toast from 'react-hot-toast';
import axios from 'axios';
import { useAuth } from '../contexts/AuthContext';
import { CheckCircle, Download } from 'lucide-react';

const SuccessContainer = styled.div`
  min-height: 100vh;
  padding: 120px 20px 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const SuccessCard = styled.div`
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 500px;
  width: 100%;
`;

const SuccessIcon = styled.div`
  font-size: 4rem;
  margin-bottom: 20px;
  color: #4CAF50;
`;

const SuccessTitle = styled.h1`
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: #4CAF50;
`;

const SuccessMessage = styled.p`
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
`;

const LoadingSpinner = styled.div`
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 20px auto;
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const PaymentSuccess = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [processing, setProcessing] = useState(true);
  const [licenseData, setLicenseData] = useState(null);

  useEffect(() => {
    executePayment();
  }, []);

  const executePayment = async () => {
    try {
      const paymentId = searchParams.get('paymentId');
      const payerId = searchParams.get('PayerID');

      if (!paymentId || !payerId) {
        toast.error('Invalid payment parameters');
        navigate('/purchase');
        return;
      }

      const response = await axios.post('/payments/execute', {
        paymentId,
        payerId
      });

      if (response.data.success) {
        setLicenseData(response.data.data);
        toast.success('Payment successful! License activated.');
        
        // Redirect to download page after 3 seconds
        setTimeout(() => {
          navigate('/download', {
            state: {
              licenseKey: response.data.data.licenseKey,
              plan: {
                title: response.data.data.tier === 1 ? 'Initial Purchase' : 'Monthly Renewal',
                screenshots: response.data.data.license.screenshotsLimit
              }
            }
          });
        }, 3000);
      } else {
        throw new Error(response.data.message);
      }
    } catch (error) {
      console.error('Payment execution error:', error);
      toast.error(error.response?.data?.message || 'Payment processing failed');
      navigate('/purchase');
    } finally {
      setProcessing(false);
    }
  };

  if (processing) {
    return (
      <SuccessContainer>
        <SuccessCard>
          <LoadingSpinner />
          <h2>Processing Payment...</h2>
          <p>Please wait while we confirm your payment with PayPal.</p>
        </SuccessCard>
      </SuccessContainer>
    );
  }

  return (
    <SuccessContainer>
      <SuccessCard>
        <SuccessIcon>
          <CheckCircle size={64} />
        </SuccessIcon>
        <SuccessTitle>Payment Successful!</SuccessTitle>
        <SuccessMessage>
          Your license has been activated successfully.
        </SuccessMessage>
        
        {licenseData && (
          <div style={{ marginBottom: '30px' }}>
            <p><strong>License Key:</strong> {licenseData.licenseKey}</p>
            <p><strong>Amount Paid:</strong> ₹{licenseData.amount}</p>
            <p><strong>Screenshots:</strong> {licenseData.license.screenshotsLimit}</p>
          </div>
        )}
        
        <button
          className="btn-primary"
          onClick={() => navigate('/download')}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '10px',
            margin: '0 auto'
          }}
        >
          <Download size={20} />
          Download Software
        </button>
        
        <p style={{ marginTop: '20px', fontSize: '0.9rem', opacity: '0.7' }}>
          Redirecting to download page in a few seconds...
        </p>
      </SuccessCard>
    </SuccessContainer>
  );
};

export default PaymentSuccess;
