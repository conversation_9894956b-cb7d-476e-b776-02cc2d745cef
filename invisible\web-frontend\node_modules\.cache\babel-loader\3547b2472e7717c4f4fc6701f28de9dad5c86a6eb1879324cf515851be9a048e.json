{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst Flashlight = createLucideIcon(\"Flashlight\", [[\"path\", {\n  d: \"M18 6c0 2-2 2-2 4v10a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4V2h12z\",\n  key: \"1orkel\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"18\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"1z11jq\"\n}], [\"line\", {\n  x1: \"12\",\n  x2: \"12\",\n  y1: \"12\",\n  y2: \"12\",\n  key: \"1f4yc1\"\n}]]);\nexport { Flashlight as default };", "map": {"version": 3, "names": ["Flashlight", "createLucideIcon", "d", "key", "x1", "x2", "y1", "y2"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\flashlight.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Flashlight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNmMwIDItMiAyLTIgNHYxMGEyIDIgMCAwIDEtMiAyaC00YTIgMiAwIDAgMS0yLTJWMTBjMC0yLTItMi0yLTRWMmgxMnoiIC8+CiAgPGxpbmUgeDE9IjYiIHgyPSIxOCIgeTE9IjYiIHkyPSI2IiAvPgogIDxsaW5lIHgxPSIxMiIgeDI9IjEyIiB5MT0iMTIiIHkyPSIxMiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/flashlight\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Flashlight = createLucideIcon('Flashlight', [\n  [\n    'path',\n    {\n      d: 'M18 6c0 2-2 2-2 4v10a2 2 0 0 1-2 2h-4a2 2 0 0 1-2-2V10c0-2-2-2-2-4V2h12z',\n      key: '1orkel',\n    },\n  ],\n  ['line', { x1: '6', x2: '18', y1: '6', y2: '6', key: '1z11jq' }],\n  ['line', { x1: '12', x2: '12', y1: '12', y2: '12', key: '1f4yc1' }],\n]);\n\nexport default Flashlight;\n"], "mappings": ";;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,QAAQ;EAAEC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAJ,GAAA,EAAK;AAAA,CAAU,GAC/D,CAAC,QAAQ;EAAEC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAC,EAAA,EAAI,IAAM;EAAAJ,GAAA,EAAK;AAAA,CAAU,EACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}