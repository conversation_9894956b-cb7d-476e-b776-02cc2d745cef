{"version": 3, "file": "prefer-readonly-parameter-types.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-readonly-parameter-types.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,8CAAgC;AAUhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,gGAAgG;YAClG,WAAW,EAAE,KAAK;YAClB,oBAAoB,EAAE,IAAI;SAC3B;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,kBACR,wBAAwB,EAAE;wBACxB,IAAI,EAAE,SAAS;qBAChB,EACD,mBAAmB,EAAE;wBACnB,IAAI,EAAE,SAAS;qBAChB,IACE,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAC7C;aACF;SACF;QACD,QAAQ,EAAE;YACR,gBAAgB,EAAE,uCAAuC;SAC1D;KACF;IACD,cAAc,EAAE;wBAEZ,wBAAwB,EAAE,IAAI,EAC9B,mBAAmB,EAAE,KAAK,IACvB,IAAI,CAAC,2BAA2B;KAEtC;IACD,MAAM,CACJ,OAAO,EACP,CAAC,EAAE,wBAAwB,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,CAAC;QAE3E,MAAM,EAAE,qBAAqB,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3E,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QAEzC,OAAO;YACL,CAAC;gBACC,sBAAc,CAAC,uBAAuB;gBACtC,sBAAc,CAAC,mBAAmB;gBAClC,sBAAc,CAAC,kBAAkB;gBACjC,sBAAc,CAAC,0BAA0B;gBACzC,sBAAc,CAAC,+BAA+B;gBAC9C,sBAAc,CAAC,iBAAiB;gBAChC,sBAAc,CAAC,6BAA6B;gBAC5C,sBAAc,CAAC,cAAc;gBAC7B,sBAAc,CAAC,iBAAiB;aACjC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CACX,IAS8B;gBAE9B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,EAAE;oBAC/B,IACE,CAAC,wBAAwB;wBACzB,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EACjD;wBACA,SAAS;qBACV;oBAED,MAAM,WAAW,GACf,KAAK,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;wBAC/C,CAAC,CAAC,KAAK,CAAC,SAAS;wBACjB,CAAC,CAAC,KAAK,CAAC;oBAEZ,IAAI,mBAAmB,IAAI,WAAW,CAAC,cAAc,IAAI,IAAI,EAAE;wBAC7D,SAAS;qBACV;oBAED,MAAM,MAAM,GAAG,qBAAqB,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;oBACtD,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE;wBACpD,sBAAsB,EAAE,sBAAuB;qBAChD,CAAC,CAAC;oBAEH,IAAI,CAAC,UAAU,EAAE;wBACf,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,WAAW;4BACjB,SAAS,EAAE,kBAAkB;yBAC9B,CAAC,CAAC;qBACJ;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}