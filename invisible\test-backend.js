const axios = require('axios');
require('dotenv').config({ path: './.env' });

// Test backend connectivity and Razorpay configuration
async function testBackend() {
    console.log('🧪 TESTING BACKEND CONNECTIVITY AND RAZORPAY INTEGRATION');
    console.log('='.repeat(60));

    const baseURL = 'http://localhost:5002/api';

    try {
        // Test 1: Health check
        console.log('1️⃣ Testing health endpoint...');
        const healthResponse = await axios.get('http://localhost:5002/health');
        console.log('✅ Health check:', healthResponse.data);

        // Test 2: Test payments route
        console.log('\n2️⃣ Testing payments route...');
        const paymentsTestResponse = await axios.get(`${baseURL}/payments/test`);
        console.log('✅ Payments route:', paymentsTestResponse.data);

        // Test 3: Test pricing endpoint
        console.log('\n3️⃣ Testing pricing endpoint...');
        const pricingResponse = await axios.get(`${baseURL}/payments/pricing`);
        console.log('✅ Pricing:', pricingResponse.data);

        // Test 4: Environment variables check
        console.log('\n4️⃣ Environment Variables Check:');
        console.log('MONGO_URI:', process.env.MONGO_URI ? '✅ SET' : '❌ NOT SET');
        console.log('RAZORPAY_API_KEYID:', process.env.RAZORPAY_API_KEYID ? '✅ SET' : '❌ NOT SET');
        console.log('RAZORPAY_API_KEYSECRET:', process.env.RAZORPAY_API_KEYSECRET ? '✅ SET' : '❌ NOT SET');

        console.log('\n🎉 ALL TESTS PASSED! Backend is working correctly.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
        console.log('\n🔧 TROUBLESHOOTING STEPS:');
        console.log('1. Make sure the backend server is running: cd invisible/license-server && npm start');
        console.log('2. Check if MongoDB is connected');
        console.log('3. Verify environment variables in invisible.env');
    }
}

// Test user registration and payment flow
async function testUserFlow() {
    console.log('\n🧪 TESTING COMPLETE USER FLOW');
    console.log('='.repeat(60));

    const baseURL = 'http://localhost:5002/api';
    const testUser = {
        name: 'Test User',
        email: `test${Date.now()}@example.com`,
        password: 'testpassword123'
    };

    try {
        // Step 1: Register user
        console.log('1️⃣ Registering test user...');
        const registerResponse = await axios.post(`${baseURL}/auth/register`, testUser);
        console.log('✅ User registered:', registerResponse.data.data.user.email);

        const token = registerResponse.data.data.token;
        const userId = registerResponse.data.data.user.id;

        // Step 2: Create test license directly
        console.log('\n2️⃣ Creating test license...');
        const licenseResponse = await axios.get(`${baseURL}/payments/create-test-license/${userId}`, {
            headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Test license created:', licenseResponse.data.data.licenseKey);

        // Step 3: Test download link creation
        console.log('\n3️⃣ Testing download link creation...');
        const downloadResponse = await axios.post(`${baseURL}/download/create-link`, {
            licenseKey: licenseResponse.data.data.licenseKey
        }, {
            headers: { Authorization: `Bearer ${token}` }
        });
        console.log('✅ Download link created:', downloadResponse.data.data.downloadUrl);

        console.log('\n🎉 COMPLETE USER FLOW TEST PASSED!');
        console.log('📋 Test Results:');
        console.log(`   User: ${testUser.email}`);
        console.log(`   License: ${licenseResponse.data.data.licenseKey}`);
        console.log(`   Download: ${downloadResponse.data.data.downloadUrl}`);

    } catch (error) {
        console.error('❌ User flow test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

// Run tests
async function runAllTests() {
    await testBackend();
    await testUserFlow();

    console.log('\n🚀 NEXT STEPS:');
    console.log('1. Start the backend: cd invisible/license-server && npm start');
    console.log('2. Start the frontend: cd invisible/web-frontend && npm start');
    console.log('3. Open http://localhost:3000 in your browser');
    console.log('4. Register a new account and test the payment flow');
}

runAllTests();
