{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst LampCeiling = createLucideIcon(\"LampCeiling\", [[\"path\", {\n  d: \"M12 2v5\",\n  key: \"nd4vlx\"\n}], [\"path\", {\n  d: \"M6 7h12l4 9H2l4-9Z\",\n  key: \"123d64\"\n}], [\"path\", {\n  d: \"M9.17 16a3 3 0 1 0 5.66 0\",\n  key: \"1061mw\"\n}]]);\nexport { LampCeiling as default };", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "createLucideIcon", "d", "key"], "sources": ["D:\\electron\\invisible\\web-frontend\\node_modules\\lucide-react\\src\\icons\\lamp-ceiling.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LampCeiling\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMnY1IiAvPgogIDxwYXRoIGQ9Ik02IDdoMTJsNCA5SDJsNC05WiIgLz4KICA8cGF0aCBkPSJNOS4xNyAxNmEzIDMgMCAxIDAgNS42NiAwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/lamp-ceiling\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampCeiling = createLucideIcon('LampCeiling', [\n  ['path', { d: 'M12 2v5', key: 'nd4vlx' }],\n  ['path', { d: 'M6 7h12l4 9H2l4-9Z', key: '123d64' }],\n  ['path', { d: 'M9.17 16a3 3 0 1 0 5.66 0', key: '1061mw' }],\n]);\n\nexport default LampCeiling;\n"], "mappings": ";;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,2BAA6B;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC3D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}