import type { ParseSettings } from '../parseSettings';
import type { ASTAndProgram } from './shared';
/**
 * @param code The code of the file being linted
 * @returns Returns a new source file and program corresponding to the linted code
 */
declare function createIsolatedProgram(parseSettings: ParseSettings): ASTAndProgram;
export { createIsolatedProgram };
//# sourceMappingURL=createIsolatedProgram.d.ts.map