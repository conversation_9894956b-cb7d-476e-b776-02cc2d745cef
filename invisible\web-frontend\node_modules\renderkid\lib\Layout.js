"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var Block, Layout, SpecialString, cloneAndMergeDeep, i, len, prop, ref, terminalWidth;
Block = require('./layout/Block');

var _require = require('./tools');

cloneAndMergeDeep = _require.cloneAndMergeDeep;
SpecialString = require('./layout/SpecialString');
terminalWidth = require('./tools').getCols();

module.exports = Layout = function () {
  var self;

  var Layout = /*#__PURE__*/function () {
    function Layout() {
      var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};
      var rootBlockConfig = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};

      _classCallCheck(this, Layout);

      var rootConfig;
      this._written = [];
      this._activeBlock = null;
      this._config = cloneAndMergeDeep(self._defaultConfig, config); // Every layout has a root block

      rootConfig = cloneAndMergeDeep(self._rootBlockDefaultConfig, rootBlockConfig);
      this._root = new Block(this, null, rootConfig, '__root');

      this._root._open();
    }

    _createClass(Layout, [{
      key: "getRootBlock",
      value: function getRootBlock() {
        return this._root;
      }
    }, {
      key: "_append",
      value: function _append(text) {
        return this._written.push(text);
      }
    }, {
      key: "_appendLine",
      value: function _appendLine(text) {
        var s;

        this._append(text);

        s = new SpecialString(text);

        if (s.length < this._config.terminalWidth) {
          this._append('<none>\n</none>');
        }

        return this;
      }
    }, {
      key: "get",
      value: function get() {
        this._ensureClosed();

        if (this._written[this._written.length - 1] === '<none>\n</none>') {
          this._written.pop();
        }

        return this._written.join("");
      }
    }, {
      key: "_ensureClosed",
      value: function _ensureClosed() {
        if (this._activeBlock !== this._root) {
          throw Error("Not all the blocks have been closed. Please call block.close() on all open blocks.");
        }

        if (this._root.isOpen()) {
          this._root.close();
        }
      }
    }]);

    return Layout;
  }();

  ;
  self = Layout;
  Layout._rootBlockDefaultConfig = {
    linePrependor: {
      options: {
        amount: 0
      }
    },
    lineAppendor: {
      options: {
        amount: 0
      }
    },
    blockPrependor: {
      options: {
        amount: 0
      }
    },
    blockAppendor: {
      options: {
        amount: 0
      }
    }
  };
  Layout._defaultConfig = {
    terminalWidth: terminalWidth
  };
  return Layout;
}.call(void 0);

ref = ['openBlock', 'write'];

for (i = 0, len = ref.length; i < len; i++) {
  prop = ref[i];

  (function () {
    var method;
    method = prop;
    return Layout.prototype[method] = function () {
      return this._root[method].apply(this._root, arguments);
    };
  })();
}