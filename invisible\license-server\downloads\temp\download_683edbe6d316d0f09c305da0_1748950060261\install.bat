@echo off
echo 🚀 Installing Invisible Assessment Tool...
echo ================================
echo.

REM Create application directory
set "INSTALL_DIR=%LOCALAPPDATA%\InvisibleAssessmentTool"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

REM Copy files
echo Copying application files...
copy "InvisibleAssessmentTool.exe" "%INSTALL_DIR%\" >nul
copy "license.key" "%INSTALL_DIR%\" >nul

REM Create desktop shortcut
echo Creating desktop shortcut...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('$env:USERPROFILE\Desktop\Invisible Assessment Tool.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\InvisibleAssessmentTool.exe'; $Shortcut.Save()"

echo.
echo ✅ Installation complete!
echo 📍 Installed to: %INSTALL_DIR%
echo 🖥️ Desktop shortcut created
echo.
echo 🚀 You can now run the Invisible Assessment Tool!
echo 💡 The software will validate your license on first run.
echo.
pause

REM Clean up installation files
cd /d "%TEMP%"
rmdir /s /q "%~dp0" 2>nul
